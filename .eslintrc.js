module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: 'tsconfig.json',
    tsconfigRootDir: __dirname,
    sourceType: 'module',
  },
  plugins: ['@typescript-eslint/eslint-plugin', 'filenames'],
  extends: [
    'plugin:@typescript-eslint/recommended',
    'plugin:prettier/recommended',
  ],
  root: true,
  env: {
    node: true,
    jest: true,
  },
  ignorePatterns: ['.eslintrc.js', 'test-e2e/**'],
  rules: {
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'off',

    'filenames/match-regex': [
      2,
      '^[a-z-.(service|schema|dto|enum|controller|e2e-spec).ts]+$',
      true,
    ],
    'no-console': ['warn', { allow: ['error'] }],
    '@typescript-eslint/naming-convention': [
      'error',
      {
        selector: 'variable',
        format: ['camelCase'],
        // in destructure  things come from databse  or request
        filter: {
          regex: '^_id$',
          match: false,
        },
      },
      {
        selector: 'enum',
        format: ['camelCase'],
      },
      {
        selector: 'enumMember',
        format: ['snake_case'],
      },
      {
        selector: 'property',
        modifiers: ['readonly'],
        format: ['snake_case'],
      },
      {
        selector: 'parameterProperty',
        format: ['camelCase'],
        filter: {
          regex: '^_id$',
          match: false,
        },
      },
      {
        selector: 'objectLiteralProperty',
        format: ['snake_case'],
        filter: {
          regex:
            '^(_id|fileSize|loadClass|privateKey|echangeOpts|deletedAt|accountingNode|hasNextPage|hasPrevPage|propertyName|maxPoolSize|maxIdleTimeMS|useCache|queueOptions|useValue|arrayFilters|updateOne|runValidators|localField|foreignField|findOne|exceptionCode|statusCode|isArray|toClassOnly|keepAlive|useNewUrlParser|useUnifiedTopology|jwtFromRequest|passReqToCallback|passResToCallback|ignoreExpiration|secretOrKey|httpOnly|sameSite|publicKey|signOptions|expiresIn|createdAt|updatedAt|inheritAppConfig|allowedHeaders|responseData|additionalData|serviceTrace|enableImplicitConversion|skipMissingProperties|skipNullProperties|skipUndefinedProperties|validationError|stopAtFirstError|forbidUnknownValues|ignoreGlobalPrefix|swaggerOptions|displayRequestDuration|envFilePath|isGlobal|useClass|\\.|\\$|`.*`|^\\$.*||[a-zA-Z]+\\.\\$\\.[a-zA-Z]+|[a-zA-Z]+\\.[a-zA-Z]+|[a-zA-Z]+\\.\\$\\[[a-zA-Z]+\\]\\.[a-zA-Z]+)$',
          match: false,
        },
      },
      {
        selector: 'typeProperty',
        format: ['camelCase'],
        filter: {
          regex: '^_id$',
          match: false,
        },
      },

      //this will effect dto parameters that are sneak case
      // {
      //   "selector": "classProperty",
      //   "format": ["camelCase"],
      //   "filter": {
      //     "regex": "^_id$",
      //     "match": false
      //   },
      // },
      {
        selector: 'classMethod',
        format: ['camelCase'],
        filter: {
          regex: '^_id$',
          match: false,
        },
      },
      {
        selector: 'objectLiteralMethod',
        format: ['camelCase'],
      },
      {
        selector: 'function',
        format: ['camelCase'],
      },
    ],
  },
};
