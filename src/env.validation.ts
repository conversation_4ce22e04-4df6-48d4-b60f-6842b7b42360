import { plainToClass, Transform } from 'class-transformer';
import {
  ArrayNotEmpty,
  ArrayUnique,
  IsArray,
  IsIn,
  IsNumber,
  IsOptional,
  IsString,
  validateSync,
} from 'class-validator';
import { Services } from './utils/interface/exceptions.interface';
import { LogLevel } from '@nestjs/common';

const logLevelsArray: LogLevel[] = [
  'log',
  'error',
  'warn',
  'debug',
  'verbose',
  'fatal',
];

class EnvironmentVariables {
  @IsIn(['invoice_portal'])
  SERVICE_NAME: Services;

  @IsNumber()
  PORT: number;

  @IsString()
  DB_URL: string;

  @Transform(({ value }) => {
    try {
      return JSON.parse(value);
    } catch (e) {
      return [];
    }
  })
  @IsArray()
  @ArrayNotEmpty() // Ensures the array is not empty
  @ArrayUnique() // Ensures all items in the array are unique
  @IsIn(logLevelsArray, { each: true })
  @IsOptional()
  LOG_LEVEL: LogLevel[] = ['debug', 'verbose', 'log', 'error']; //default value if not provided
  @IsString()
  @IsOptional()
  PREFIX_DB: string;

  @Transform(({ value }) => {
    try {
      return JSON.parse(value);
    } catch (e) {
      return undefined;
    }
  })
  @IsArray()
  BASE_URL: string[];

  @IsNumber()
  ACCESS_TOKEN_EXPIRE: number;

  @IsNumber()
  REFRESH_TOKEN_EXPIRE: number;

  @IsString()
  JWT_PK: string;

  @IsString()
  JWT_PUBKEY: string;

  @IsString()
  @IsOptional()
  APP_URL_PREFIX: string;

  @IsString()
  NODE_ENV: string; //dev //dev-prod //prod

  @IsString()
  COMMON_DB: string;
  @IsString()
  @IsOptional()
  test_URL: string;
}

export function validate(config: Record<string, unknown>) {
  const validatedConfig = plainToClass(EnvironmentVariables, config, {
    enableImplicitConversion: true,
  });
  const errors = validateSync(validatedConfig, {
    skipMissingProperties: false,
  });

  if (errors.length > 0) {
    throw new Error(errors.toString());
  }
  return validatedConfig;
}
