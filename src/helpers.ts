import { Connection } from 'mongoose';

export const clearDb = async (
  dbName: string,
  connection: Connection,
): Promise<void> => {
  await connection.dropDatabase();
  // await connection.query(`CREATE DATABASE IF NOT EXISTS ${dbName} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;`)
  // const entities = connection.entityMetadatas;
  // await connection.query('SET foreign_key_checks = 0;');
  // for (const entity of entities) {
  //   const repository = connection.getRepository(entity.name);
  //   //console.log(entity.tableName);
  //   await repository.query(`DELETE FROM ${entity.tableName};`);
  // }
};
