/* eslint-disable @typescript-eslint/no-unused-expressions */
import mongoose, { Types } from 'mongoose';
import { faker, fakerAR } from '@faker-js/faker';
import * as dotenv from 'dotenv';
import { User_Schema } from '../modules/users/schemas/user.schema';
import { roleSchema } from '../modules/roles/schemas/role.schema';
import { branchSchema } from '../modules/branch/schemas/branch.schema';
import { companySchema } from '../modules/company/schemas/company.schema';
import { permissionSchema } from '../modules/permissions/schema/permisstions.schema';
import { userBranchRoleSchema } from '../modules/user-branches-roles/schema/user-branches-roles.schema';
import {
  documentPolicy,
  documentPolicyStatus,
  level,
  pricingLevel,
} from '../modules/company/schemas/company-settings.schema';
import { paymentTypeSchema } from '../modules/payment-types/schema/payment-type.schema';
import { permList } from './assets/perm';
import { ServiceVersionSeeder } from './seeders/service-version-seeder';
import { tenantSchema, tenantUserSchema } from '../modules/tenants/schemas';
import { storeSchema } from '../modules/store/schema/store.schema';
import { itemGroupSchema } from '../modules/group-item/schema/item-group.schema';
import { itemType } from '../modules/inventory-item/types/item-types.enum';
import { ItemSeeder } from './seeders/item-seeder';
import { TemplateSeeder } from './seeders/template-seeder';
import { unitSchema } from '../modules/unit/schema/unit.schema';

dotenv.config({});
// write the following string to console in yollow font use --drop to drop the database --tenant 1 seed tenant_1 database
console.log(
  '\x1b[33m%s\x1b[0m',
  'you can use the following arguments "npm run seed -- --tenant 1 --drop"',
);
console.log('\x1b[33m%s\x1b[0m', '--drop          drops tenant_id database ');
console.log('\x1b[33m%s\x1b[0m', '--tenant id     seeds tenant_id database');
console.log('\x1b[33m%s\x1b[0m', '--multi     seeds tenants 0 to 3');

const obj = {} as any;
mongoose.set('strictQuery', false);

async function tenantSeed(tenantid: number) {
  const prefix = process.env.PREFIX_DB || '';
  const baseUrl = process.env.DB_URL?.replace(/\/+$/, '') || '';
  const dbName = `${prefix}tenant_${tenantid}`;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const params = new URLSearchParams({ authSource: 'admin' });

  const uri = `${baseUrl}/${dbName}?${params.toString()}`;
  const tenantDb = mongoose.createConnection(uri);
  //check all arguments the argument if it --drop exist drop the table. if the database is droped write database droped in red color
  if (process.argv.includes('--drop')) {
    await tenantDb.dropDatabase();
    console.log(
      '\x1b[31m%s\x1b[0m',
      `Tenant ${tenantid} database has been droped`,
    );
  }

  if (
    process.argv.includes('--only-templates') &&
    !process.argv.includes('--drop')
  ) {
    console.log('reseeding templates');
    await tenantDb.dropCollection('templatedesigns');
    const templateSeeder = new TemplateSeeder(tenantDb);
    await templateSeeder.seed();
    await tenantDb.close();
    return;
  }

  const i = tenantid;
  const roleModel = tenantDb.model('role', roleSchema);
  const permissionModel = tenantDb.model('permission', permissionSchema);
  const paymentTypesModel = tenantDb.model('paymenttypes', paymentTypeSchema);
  const branchModel = tenantDb.model('branch', branchSchema);
  const userModel = tenantDb.model('user', User_Schema);
  const userBeanchRolesModel = tenantDb.model(
    'userbranchroles',
    userBranchRoleSchema,
  );
  const companyModel = tenantDb.model('company', companySchema);
  const storeModel = tenantDb.model('stores', storeSchema);
  const unitModel = tenantDb.model('units', unitSchema);
  const itemGroupModel = tenantDb.model('itemgroups', itemGroupSchema);

  const chairsGroupId = faker.number.int();
  const tablesGroupId = faker.number.int();
  const drinksGroupId = faker.number.int();
  const spicesGroupId = faker.number.int();
  const plasticGroupId = faker.number.int();
  const washGroupId = faker.number.int();
  const repairGroupId = faker.number.int();
  const branchlist = [
    {
      _id: new mongoose.Types.ObjectId('64956ef5f506e9edf21df4e8'),
      general_information: {
        code: '1',
        fax: '',
        email: '',
        phone: faker.helpers.replaceSymbolWithNumber('+9665########'),
        activation_status: true,
        name: {
          en: faker.company.name(),
          ar: fakerAR.company.name(),
        },
        tax_code: faker.string.numeric(15),
        registration_code: faker.string.numeric(10),
        is_default: true,
        is_main_branch: false,
      },
      national_address: {
        commercial_activities: faker.commerce.department(),
        trade_name: {
          en: faker.commerce.department(),
          ar: fakerAR.commerce.department(),
        },
        short_address: faker.string.alpha(3),
        building_no: faker.string.numeric(4),
        street: faker.location.street(),
        secondary_no: faker.string.numeric(3),
        district: faker.location.state(),
        postal_code: faker.string.numeric(5),
        city: faker.location.city(),
        governorate: faker.location.state(),
      },
      settings: {
        general: {
          allow_documents_deletion: true,
          allow_documents_edition: {
            enabled: true,
            type: 'unrestricted-editing',
          },
          use_multi_stores: false,
        },
        global_accounts: {
          tax_accounts: {
            purchase_tax_account: '6501b9f946ad8225ab76cb92',
            sales_tax_account: '6501b9f946ad8225ab76cb9e',
            payment_commission_tax_account: '6501b9f946ad8225ab76cb93',
          },
          sales_accounts: {
            cash_sales_account: '6501b9f946ad8225ab76cbba',
            credit_sales_account: '6501b9f946ad8225ab76cbbb',
            cash_sales_return_account: '6501b9f946ad8225ab76cbbc',
            credit_sales_return_account: '6501b9f946ad8225ab76cbbd',
            sales_discount_account: '6501b9f946ad8225ab76cbbe',
            customers_deposits_account: '6501b9f946ad8225ab76cbd9',
          },
          cash_and_bank: {
            cash_account: '6501b9f946ad8225ab76cb7f',
            bank_account: '6501b9f946ad8225ab76cb83',
            additional_expense_account: '',
            cash_invoices_under_settlement: '',
            profit_account: '6501b9f946ad8225ab76cba3',
          },
          sub_accounts: {
            cash_sales_commission_account: '',
            credit_sales_commission_account: '',
            levy_commission_account: '',
            customer_group_account: '6501b9f946ad8225ab76cb85',
            vendor_group_account: '6501b9f946ad8225ab76cb97',
          },
          other_accounts: {
            transfer_account: '',
            adjustment_account: '6501b9f946ad8225ab76cbb6',
            inventory_account: '6501b9f946ad8225ab76cb8e',
            beginning_balance_account: '6501b9f946ad8225ab76cba7',
            ending_balance_account: '',
            cost_of_sales_account: '6501b9f946ad8225ab76cbb5',
            cost_of_purchase_account: '',
          },
          purchase_accounts: {
            cash_purchase_account: '6501b9f946ad8225ab76cba9',
            credit_purchase_account: '6501b9f946ad8225ab76cbaa',
            cash_purchase_return_account: '6501b9f946ad8225ab76cbab',
            credit_purchase_return_account: '6501b9f946ad8225ab76cbac',
            purchase_discount_account: '6501b9f946ad8225ab76cbad',
            deferred_discount_account: '',
          },
        },
      },
      document: {
        sales_invoices: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },

        sales_return: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        purchases_invoices: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        purchase_return: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        transfer_order: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        quotation: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        reservation: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        preparation: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        journal: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        payment_voucher_cash: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        receipt_voucher: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        debit_memo: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        credit_memo: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        service_invoice_customer: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        service_invoice_vendors: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        purchase_order: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        store_transactions_and_adjustments: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        import_order: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        prepare_purchase_entry: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        transfer_receive: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        quotation_order_and_comp: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        physical_inventory_voucher: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        physical_inventory_posting: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        sales_receipts_consignment: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        purchase_receipts_consignment: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        issue_permission: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        addition_permission: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        work_orders: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        entry_permission: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        release_permission: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        work_order_production: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        transfer_preparing_order: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        production_order: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        quotation_request_p: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        cargo_manifest: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        cargo_manifest_invoice: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        sales_order: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
      },
      dates: {
        year_beginning_date: new Date('2024-01-01').toISOString(),
        year_ending_date: new Date('2024-12-31').toISOString(),
      },
    },

    {
      _id: new mongoose.Types.ObjectId('64956ef5f506e9edf21df4e9'),
      general_information: {
        code: '2',
        fax: '',
        email: '',
        phone: faker.helpers.replaceSymbolWithNumber('+9665########'),
        activation_status: true,
        name: {
          en: faker.company.name(),
          ar: fakerAR.company.name(),
        },
        tax_code: faker.string.numeric(15),
        registration_code: faker.string.numeric(10),
        is_default: true,
        is_main_branch: false,
      },
      national_address: {
        commercial_activities: faker.commerce.department(),
        trade_name: {
          en: faker.commerce.department(),
          ar: fakerAR.commerce.department(),
        },
        short_address: faker.string.alpha(3),
        building_no: faker.string.numeric(4),
        street: faker.location.street(),
        secondary_no: faker.string.numeric(3),
        district: faker.location.state(),
        postal_code: faker.string.numeric(5),
        city: faker.location.city(),
        governorate: faker.location.state(),
      },
      settings: {
        general: {
          allow_documents_deletion: true,
          allow_documents_edition: {
            enabled: true,
            type: 'unrestricted-editing',
          },
          use_multi_stores: false,
        },
        global_accounts: {
          tax_accounts: {
            purchase_tax_account: '6501b9f946ad8225ab76cb92',
            sales_tax_account: '6501b9f946ad8225ab76cb9e',
            payment_commission_tax_account: '6501b9f946ad8225ab76cb93',
          },
          sales_accounts: {
            cash_sales_account: '6501b9f946ad8225ab76cbba',
            credit_sales_account: '6501b9f946ad8225ab76cbbb',
            cash_sales_return_account: '6501b9f946ad8225ab76cbbc',
            credit_sales_return_account: '6501b9f946ad8225ab76cbbd',
            sales_discount_account: '6501b9f946ad8225ab76cbbe',
            customers_deposits_account: '6501b9f946ad8225ab76cbd9',
          },
          cash_and_bank: {
            cash_account: '6501b9f946ad8225ab76cb7f',
            bank_account: '6501b9f946ad8225ab76cb83',
            additional_expense_account: '',
            cash_invoices_under_settlement: '',
            profit_account: '6501b9f946ad8225ab76cba3',
          },
          sub_accounts: {
            cash_sales_commission_account: '',
            credit_sales_commission_account: '',
            levy_commission_account: '',
            customer_group_account: '6501b9f946ad8225ab76cb85',
            vendor_group_account: '6501b9f946ad8225ab76cb97',
          },
          other_accounts: {
            transfer_account: '',
            adjustment_account: '6501b9f946ad8225ab76cbb6',
            inventory_account: '6501b9f946ad8225ab76cb8e',
            beginning_balance_account: '',
            ending_balance_account: '',
            cost_of_sales_account: '6501b9f946ad8225ab76cbb5',
            cost_of_purchase_account: '',
          },
          purchase_accounts: {
            cash_purchase_account: '6501b9f946ad8225ab76cba9',
            credit_purchase_account: '6501b9f946ad8225ab76cbaa',
            cash_purchase_return_account: '6501b9f946ad8225ab76cbab',
            credit_purchase_return_account: '6501b9f946ad8225ab76cbac',
            purchase_discount_account: '6501b9f946ad8225ab76cbad',
            deferred_discount_account: '',
          },
        },
      },
      document: {
        sales_invoices: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 100,
          default_unit_as_biggest: false,
        },
        sales_return: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 100,
          default_unit_as_biggest: false,
        },
        purchases_invoices: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 100,
          default_unit_as_biggest: false,
        },
        purchase_return: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        transfer_order: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        quotation: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        reservation: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        preparation: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        journal: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 100,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        payment_voucher_cash: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        receipt_voucher: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        debit_memo: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        credit_memo: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        service_invoice_customer: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        service_invoice_vendors: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        purchase_order: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        store_transactions_and_adjustments: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        import_order: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        prepare_purchase_entry: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        transfer_receive: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        quotation_order_and_comp: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        physical_inventory_voucher: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        physical_inventory_posting: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        sales_receipts_consignment: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        purchase_receipts_consignment: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        issue_permission: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        addition_permission: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        work_orders: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        entry_permission: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        release_permission: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        work_order_production: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        transfer_preparing_order: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        production_order: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        quotation_request_p: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        cargo_manifest: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        cargo_manifest_invoice: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        sales_order: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
      },
      dates: {
        year_beginning_date: new Date('2024-01-01').toISOString(),
        year_ending_date: new Date('2024-12-31').toISOString(),
      },
    },
  ];
  const storeId = [
    new mongoose.Types.ObjectId('64ad000ab16fbc375c9649de'),
    new mongoose.Types.ObjectId('64956ef5f506e9edf21df4e9'),
    new mongoose.Types.ObjectId('64ad000ab16fbc375c9649df'),
    new mongoose.Types.ObjectId('64956ef5f506e9edf21df4ea'),
  ];

  const unitIds = {
    kilogram: faker.number.int(),
    gram: faker.number.int(),
    piece: faker.number.int(),
    ten_pic: faker.number.int(),
    dozen: faker.number.int(),
    box_twenty_pic: faker.number.int(),
    box_hundred_pic: faker.number.int(),
    roll: faker.number.int(),
    bag_three_hundred_gram: faker.number.int(),
    basic: faker.number.int(),
    medium: faker.number.int(),
    premium: faker.number.int(),
    one_person: faker.number.int(),
    more_than_one_person: faker.number.int(),
  };

  const unitData = [
    //items
    {
      id: unitIds.gram,
      code: '1',
      name: { ar: 'جرام', en: 'gram' },
      type: itemType.goods,
    },
    {
      id: unitIds.kilogram,
      code: '2',
      name: { ar: 'كيلوجرام', en: 'kilogram' },
      type: itemType.goods,
    },
    {
      id: unitIds.piece,
      code: '3',
      name: { ar: 'قطعة', en: 'piece' },
      type: itemType.goods,
    },
    {
      id: unitIds.ten_pic,
      code: '4',
      name: { ar: 'عشر قطع', en: 'ten pic' },
      type: itemType.goods,
    },
    {
      id: unitIds.dozen,
      code: '5',
      name: { ar: 'دستة', en: 'dozen' },
      type: itemType.goods,
    },
    {
      id: unitIds.box_twenty_pic,
      code: '6',
      name: { ar: 'كرتون 20 قطعة', en: 'box 20 pic' },
      type: itemType.goods,
    },
    {
      id: unitIds.box_hundred_pic,
      code: '7',
      name: { ar: 'كرتون 100 قطعة', en: 'box 100 pic' },
      type: itemType.goods,
    },
    {
      id: unitIds.roll,
      code: '8',
      name: { ar: 'رول', en: 'roll' },
      type: itemType.goods,
    },
    {
      id: unitIds.bag_three_hundred_gram,
      code: '9',
      name: { ar: 'كيس 300 جرام', en: 'bag 300 gram' },
      type: itemType.goods,
    },

    //service items
    {
      id: unitIds.basic,
      code: '10',
      name: { ar: 'خطة أساسية', en: 'basic plan' },
      type: itemType.service,
    },
    {
      id: unitIds.medium,
      code: '11',
      name: { ar: 'خطة متوسطة', en: 'medium plan' },
      type: itemType.service,
    },
    {
      id: unitIds.premium,
      code: '12',
      name: { ar: 'خطة متقدمة', en: 'premium plan' },
      type: itemType.service,
    },
    {
      id: unitIds.one_person,
      code: '13',
      name: { ar: 'شخص واحد', en: 'one person' },
      type: itemType.service,
    },
    {
      id: unitIds.more_than_one_person,
      code: '14',
      name: { ar: 'أكثر من شخص', en: 'more than one person' },
      type: itemType.service,
    },
  ];
  await unitModel.create(unitData);

  // try {
  await paymentTypesModel.create([
    {
      _id: new mongoose.Types.ObjectId('6501b9f946ad8225ab76cb92'),
      id: 1,
      name: {
        en: 'cash',
        ar: 'نقدي',
      },
      code: '1',
    },
    {
      _id: new mongoose.Types.ObjectId('6501b9f946ad8225ab76cba3'),
      id: 2,
      name: {
        en: 'credit card',
        ar: ' بطاقة ائتمان',
      },
      code: '2',
    },
    {
      _id: new mongoose.Types.ObjectId('6501b9f946ad8225ab76cbba'),
      id: 3,
      name: {
        en: 'bank transfer',
        ar: 'تحويل بنكي',
      },
      code: '3',
    },
    {
      _id: new mongoose.Types.ObjectId('6501b9f946ad8225ab76cbad'),
      id: 4,
      name: {
        en: 'other',
        ar: 'أخرى',
      },
      code: '4',
    },
  ]);

  const permissions = await permissionModel.insertMany(permList);

  const permissionIds = permissions.map((permission) => permission._id);
  const roleModelData = await roleModel.create([
    {
      name: 'admin' + i,
      permissions: permissionIds,
    },
    {
      name: 'salesman',
      permissions: permissionIds,
    },
    {
      name: 'cashier',
      permissions: permissionIds,
    },
  ]);

  const serviceVersionSeeder = new ServiceVersionSeeder(tenantDb);
  await serviceVersionSeeder.seed();

  const branchesData = await branchModel.create(branchlist);

  await companyModel.create({
    name: {
      en: 'Osus For Computer Systems',
      ar: 'شركة اسس النظم والحاسب الالى لتقنية المعلومات',
    },
    short_name: 'OSUS',
    settings: {
      pricing_level: pricingLevel.company,
      average_cost_level: level.store,
      document_policy: documentPolicy.adjustment,
      document_policy_status: documentPolicyStatus.enable,
    },
  });
  const roles = roleModelData.map((role) => role._id);
  const branchesIds = branchesData.map((branch) => branch._id);

  const userModelData = await userModel.create({
    _id: new Types.ObjectId('65128e387d6e377e37b40a6c'),
    name: 'مسؤول النظام',
    password: 'a123456789',
    email: 'test' + i + '@test.com',
    notes: faker.lorem.paragraph(),
    email_verified: true,
    positions: [{ roles: roles, branches: branchesIds }],
    active: true,
  });

  const userBranchRoleData = [];
  branchesData.forEach((branch) => {
    userBranchRoleData.push({
      user: userModelData._id,
      branch: branch._id,
      roles: roles,
    });
  });
  await userBeanchRolesModel.create(userBranchRoleData);

  const storesList = [
    {
      _id: storeId[0],
      number: 1,
      'name.ar': 'المخزن الافتراضي',
      'name.en': 'default store',
      branch: '64956ef5f506e9edf21df4e8',
      phone: '+201011111111',
      is_default: true,
    },
    {
      _id: storeId[1],
      number: 2,
      'name.ar': 'المخزن الخلفي',
      'name.en': 'back store',
      branch: '64956ef5f506e9edf21df4e8',
      phone: '+201011111111',
      is_default: false,
    },
    {
      _id: storeId[2],
      number: 1,
      'name.ar': ' الفرع الثاني - المخزن الافتراضي',
      'name.en': 'default store - Second branch',
      branch: '64956ef5f506e9edf21df4e9',
      phone: '+201011111111',
      is_default: true,
    },
    {
      _id: storeId[3],
      number: 2,
      'name.ar': ' الفرع الثاني - المخزن الخلفي',
      'name.en': 'back store - Second branch',
      branch: '64956ef5f506e9edf21df4e9',
      phone: '+201011111111',
      is_default: false,
    },
  ];

  await storeModel.create(storesList);

  // item group creation
  const itemGroupData = [
    //items
    {
      id: chairsGroupId,
      code: '1',
      name: { en: 'chairs', ar: 'كراسي' },
      type: itemType.goods,
    },
    {
      id: tablesGroupId,
      code: '2',
      name: { en: 'tables', ar: 'طاولات' },
      type: itemType.goods,
    },
    {
      id: drinksGroupId,
      code: '3',
      name: { en: 'drinks', ar: 'مشروبات' },
      type: itemType.goods,
    },
    {
      id: spicesGroupId,
      code: '4',
      name: { en: 'spices', ar: 'توابل' },
      type: itemType.goods,
    },
    {
      id: plasticGroupId,
      code: '5',
      name: { en: 'plastic', ar: 'بلاستيك' },
      type: itemType.goods,
    },

    //service items
    {
      id: washGroupId,
      code: '6',
      name: { en: 'wash', ar: 'غسيل' },
      type: itemType.service,
    },
    {
      id: repairGroupId,
      code: '7',
      name: { en: 'repair', ar: 'تصليح' },
      type: itemType.service,
    },
  ];
  await itemGroupModel.create(itemGroupData);

  // // item creation
  const itemSeeder = new ItemSeeder(tenantDb);
  await itemSeeder.seed(
    itemGroupData[0].id,
    itemGroupData[1].id,
    itemGroupData[2].id,
    itemGroupData[3].id,
    itemGroupData[4].id,
    itemGroupData[5].id,
    itemGroupData[6].id,
    unitData,
    storesList,
  );

  const templateSeeder = new TemplateSeeder(tenantDb);
  await templateSeeder.seed();

  await tenantDb.close();
  console.log(`seeding Tenant ${tenantid} done`);
}

async function commonSeed(tenantId: number, commonDb: mongoose.Connection) {
  const tenatModel = commonDb.model('tenant', tenantSchema);
  const tenantUserModel = commonDb.model('tenant_user', tenantUserSchema);

  const tenatModelData = await tenatModel.create({
    name: 'rozbeh',
    phone: faker.helpers.replaceSymbolWithNumber('+9665########'),
    address: 'test',
    tax_code: faker.string.numeric(15),
    code: tenantId,
    registration_no: '1223',
    activation_status: true,
    email: 'test' + tenantId + '@test.com',
    website: faker.internet.url(),
    notes: faker.lorem.paragraph(),
    contact_person: {
      name: 'testTenant' + tenantId,
      mobile: faker.helpers.replaceSymbolWithNumber('+9665########'),
      phone: faker.helpers.replaceSymbolWithNumber('+9665########'),
      email: faker.internet.email(),
    },
    main: true,
    db_url: 'test' + tenantId + '@test.com',
    db_user: 'test' + tenantId + '@test.com',
    db_password: 'test' + tenantId + '@test.com',
    user: {
      name: faker.person.firstName(),
      email: faker.internet.email(),
    },
  });
  (obj.tenant = tenatModelData._id),
    (obj.user_email = 'test' + tenantId + '@test.com');
  await tenantUserModel.create(obj);
  console.log('seeding Commond db is done for tenant: ', tenantId);
}

async function seedInit() {
  const prefix = process.env.PREFIX_DB || '';
  const baseUrl = process.env.DB_URL?.replace(/\/+$/, '') || '';
  const dbName = `${prefix}${process.env.COMMON_DB}`;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const params = new URLSearchParams({ authSource: 'admin' });

  const uri = `${baseUrl}/${dbName}?${params.toString()}`;
  const commonDb = mongoose.createConnection(uri);
  // Get if drop is requried from command line
  if (process.argv.includes('--drop')) {
    await commonDb.dropDatabase();
    console.log('\x1b[31m%s\x1b[0m', 'Common database has been droped');
  }

  // Get if multi tenant seeding is required
  if (process.argv.includes('--multi')) {
    for (let i = 0; i < 4; i++) {
      await tenantSeed(i);
      await commonSeed(i, commonDb);
    }
    await commonDb.close();
    process.exit(0);
  } else {
    let tenantId: number;
    // Get tenant id from command line
    process.argv.forEach((val, index) => {
      if (val === '--tenant') {
        tenantId = Number(process.argv[index + 1]);
      }
    });
    const i = tenantId || 0;
    await tenantSeed(i);
    await commonSeed(i, commonDb);
    await commonDb.close();
    process.exit(0);
  }
}

seedInit();
