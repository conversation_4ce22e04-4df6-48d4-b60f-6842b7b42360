export const globalAccountCodeMap = {
  tax_accounts: {
    purchase_tax_account: '1020601',
    sales_tax_account: '2010301',
    payment_commission_tax_account: '1020602',
  },
  sales_accounts: {
    cash_sales_account: '4010101',
    credit_sales_account: '4010102',
    cash_sales_return_account: '4010103',
    credit_sales_return_account: '4010104',
    sales_discount_account: '4010105',
    customers_deposits_account: '20104',
  },
  cash_and_bank: {
    cash_account: '1020101',
    bank_account: '1020201',
    // additional_expense_account: undefined,
    //cash_invoices_under_settlement: undefined,
    profit_account: '2020301',
  },
  sub_accounts: {
    //   cash_sales_commission_account: undefined,
    // credit_sales_commission_account: undefined,
    //  levy_commission_account: undefined,
    customer_group_account: '10203',
    vendor_group_account: '20101',
  },
  other_accounts: {
    //   transfer_account: undefined,
    adjustment_account: '30108',
    inventory_account: '1020401',
    beginning_balance_account: '30101',
    // ending_balance_account: undefined,
    cost_of_sales_account: '30107',
    //cost_of_purchase_account: undefined,
  },
  purchase_accounts: {
    cash_purchase_account: '3010201',
    credit_purchase_account: '3010202',
    cash_purchase_return_account: '3010203',
    credit_purchase_return_account: '3010204',
    purchase_discount_account: '3010205',
    //deferred_discount_account: undefined,
  },
};
