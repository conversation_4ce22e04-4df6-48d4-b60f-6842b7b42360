export const permList = [
  {
    name: 'sales_invoice_create',
    privileges: [
      { action: 'create', subject: 'sales_invoice' },
      { action: 'list', subject: 'abstract_items' },
      { action: 'list', subject: 'abstract_service_items' },
      { action: 'list', subject: 'abstract_stores' },
      { action: 'list', subject: 'abstract_customers' },
      { action: 'list', subject: 'abstract_expenses' },
      { action: 'list', subject: 'abstract_payment_types' },
      { action: 'list', subject: 'abstract_accounting_node' },
      { action: 'list', subject: 'abstract_sales_representative' },
    ],
  },
  {
    name: 'sales_invoice_list',
    privileges: [{ action: 'list', subject: 'sales_invoice' }],
  },
  {
    name: 'sales_invoice_details',
    privileges: [{ action: 'read', subject: 'sales_invoice' }],
  },
  {
    name: 'sales_invoice_update',
    privileges: [{ action: 'edit', subject: 'sales_invoice' }],
  },
  {
    name: 'sales_invoice_delete',
    privileges: [{ action: 'delete', subject: 'sales_invoice' }],
  },
  {
    name: 'purchase_invoice_create',
    privileges: [
      { action: 'create', subject: 'purchase_invoice' },
      { action: 'list', subject: 'abstract_items' },
      { action: 'list', subject: 'abstract_stores' },
      { action: 'list', subject: 'abstract_vendors' },
      { action: 'list', subject: 'abstract_expenses' },
      { action: 'list', subject: 'abstract_payment_types' },
      { action: 'list', subject: 'abstract_accounting_node' },
    ],
  },
  {
    name: 'purchase_invoice_list',
    privileges: [{ action: 'list', subject: 'purchase_invoice' }],
  },
  {
    name: 'purchase_invoice_details',
    privileges: [{ action: 'read', subject: 'purchase_invoice' }],
  },
  {
    name: 'purchase_invoice_update',
    privileges: [{ action: 'edit', subject: 'purchase_invoice' }],
  },
  {
    name: 'purchase_invoice_delete',
    privileges: [{ action: 'delete', subject: 'purchase_invoice' }],
  },
  {
    name: 'purchase_return_invoice_create',
    privileges: [
      { action: 'create', subject: 'purchase_return_invoice' },
      { action: 'list', subject: 'abstract_items' },
      { action: 'list', subject: 'abstract_stores' },
      { action: 'list', subject: 'abstract_vendors' },
      { action: 'list', subject: 'abstract_payment_types' },
      { action: 'list', subject: 'abstract_accounting_node' },
      { action: 'list', subject: 'abstract_sales_representative' },
    ],
  },
  {
    name: 'purchase_return_invoice_list',
    privileges: [{ action: 'list', subject: 'purchase_return_invoice' }],
  },
  {
    name: 'purchase_return_invoice_details',
    privileges: [{ action: 'read', subject: 'purchase_return_invoice' }],
  },
  {
    name: 'purchase_return_invoice_update',
    privileges: [{ action: 'edit', subject: 'purchase_return_invoice' }],
  },
  {
    name: 'purchase_return_invoice_delete',
    privileges: [{ action: 'delete', subject: 'purchase_return_invoice' }],
  },
  {
    name: 'sales_return_invoice_create',
    privileges: [
      { action: 'create', subject: 'sales_return_invoice' },
      { action: 'list', subject: 'abstract_items' },
      { action: 'list', subject: 'abstract_service_items' },
      { action: 'list', subject: 'abstract_stores' },
      { action: 'list', subject: 'abstract_customers' },
      { action: 'list', subject: 'abstract_payment_types' },
      { action: 'list', subject: 'abstract_accounting_node' },
      { action: 'list', subject: 'abstract_sales_representative' },
    ],
  },
  {
    name: 'sales_return_invoice_list',
    privileges: [{ action: 'list', subject: 'sales_return_invoice' }],
  },
  {
    name: 'sales_return_invoice_details',
    privileges: [{ action: 'read', subject: 'sales_return_invoice' }],
  },
  {
    name: 'sales_return_invoice_update',
    privileges: [{ action: 'edit', subject: 'sales_return_invoice' }],
  },
  {
    name: 'sales_return_invoice_delete',
    privileges: [{ action: 'delete', subject: 'sales_return_invoice' }],
  },
  {
    name: 'vendor_create',
    privileges: [
      { action: 'create', subject: 'vendor' },
      { action: 'list', subject: 'abstract_vendor_group' },
      { action: 'list', subject: 'abstract_sales_representative' },
    ],
  },
  {
    name: 'sales_order_create',
    privileges: [
      { action: 'create', subject: 'sales_order' },
      { action: 'list', subject: 'abstract_items' },
      { action: 'list', subject: 'abstract_service_items' },
      { action: 'list', subject: 'abstract_stores' },
      { action: 'list', subject: 'abstract_customers' },
      { action: 'list', subject: 'abstract_payment_types' },
      { action: 'list', subject: 'abstract_accounting_node' },
      { action: 'list', subject: 'abstract_sales_representative' },
    ],
  },
  {
    name: 'sales_order_list',
    privileges: [{ action: 'list', subject: 'sales_order' }],
  },
  {
    name: 'sales_order_details',
    privileges: [{ action: 'read', subject: 'sales_order' }],
  },
  {
    name: 'sales_order_update',
    privileges: [{ action: 'edit', subject: 'sales_order' }],
  },
  {
    name: 'sales_order_delete',
    privileges: [{ action: 'delete', subject: 'sales_order' }],
  },
  {
    name: 'vendor_list',
    privileges: [{ action: 'list', subject: 'vendor' }],
  },
  {
    name: 'vendor_details',
    privileges: [{ action: 'read', subject: 'vendor' }],
  },
  {
    name: 'vendor_update',
    privileges: [
      { action: 'edit', subject: 'vendor' },
      { action: 'list', subject: 'abstract_sales_representative' },
    ],
  },
  {
    name: 'vendor_delete',
    privileges: [{ action: 'delete', subject: 'vendor' }],
  },
  {
    name: 'customer_create',
    privileges: [
      { action: 'create', subject: 'customer' },
      { action: 'list', subject: 'abstract_customer_group' },
      { action: 'list', subject: 'abstract_sales_representative' },
    ],
  },
  {
    name: 'customer_list',
    privileges: [{ action: 'list', subject: 'customer' }],
  },
  {
    name: 'customer_details',
    privileges: [{ action: 'read', subject: 'customer' }],
  },
  {
    name: 'customer_update',
    privileges: [
      { action: 'edit', subject: 'customer' },
      { action: 'list', subject: 'abstract_sales_representative' },
    ],
  },
  {
    name: 'customer_delete',
    privileges: [{ action: 'delete', subject: 'customer' }],
  },
  {
    name: 'expense_create',
    privileges: [{ action: 'create', subject: 'expense' }],
  },
  {
    name: 'expense_list',
    privileges: [{ action: 'list', subject: 'expense' }],
  },
  {
    name: 'expense_details',
    privileges: [{ action: 'read', subject: 'expense' }],
  },
  {
    name: 'expense_update',
    privileges: [{ action: 'edit', subject: 'expense' }],
  },
  {
    name: 'expense_delete',
    privileges: [{ action: 'delete', subject: 'expense' }],
  },
  {
    name: 'customer_group_create',
    privileges: [{ action: 'create', subject: 'customer_group' }],
  },
  {
    name: 'customer_group_list',
    privileges: [{ action: 'list', subject: 'customer_group' }],
  },
  {
    name: 'customer_group_details',
    privileges: [{ action: 'read', subject: 'customer_group' }],
  },
  {
    name: 'customer_group_update',
    privileges: [{ action: 'edit', subject: 'customer_group' }],
  },
  {
    name: 'customer_group_delete',
    privileges: [{ action: 'delete', subject: 'customer_group' }],
  },
  {
    name: 'vendor_group_create',
    privileges: [{ action: 'create', subject: 'vendor_group' }],
  },
  {
    name: 'vendor_group_list',
    privileges: [{ action: 'list', subject: 'vendor_group' }],
  },
  {
    name: 'vendor_group_details',
    privileges: [{ action: 'read', subject: 'vendor_group' }],
  },
  {
    name: 'vendor_group_update',
    privileges: [{ action: 'edit', subject: 'vendor_group' }],
  },
  {
    name: 'vendor_group_delete',
    privileges: [{ action: 'delete', subject: 'vendor_group' }],
  },
  {
    name: 'payment_type_create',
    privileges: [{ action: 'create', subject: 'payment_type' }],
  },
  {
    name: 'payment_type_list',
    privileges: [{ action: 'list', subject: 'payment_type' }],
  },
  {
    name: 'payment_type_details',
    privileges: [{ action: 'read', subject: 'payment_type' }],
  },
  {
    name: 'payment_type_update',
    privileges: [{ action: 'edit', subject: 'payment_type' }],
  },
  {
    name: 'payment_type_delete',
    privileges: [{ action: 'delete', subject: 'payment_type' }],
  },
  {
    name: 'store_create',
    privileges: [{ action: 'create', subject: 'store' }],
  },
  {
    name: 'store_list',
    privileges: [{ action: 'list', subject: 'store' }],
  },
  {
    name: 'store_details',
    privileges: [{ action: 'read', subject: 'store' }],
  },
  {
    name: 'store_update',
    privileges: [{ action: 'edit', subject: 'store' }],
  },
  {
    name: 'store_delete',
    privileges: [{ action: 'delete', subject: 'store' }],
  },
  {
    name: 'sales_representative_create',
    privileges: [{ action: 'create', subject: 'sales_representative' }],
  },
  {
    name: 'sales_representative_list',
    privileges: [{ action: 'list', subject: 'sales_representative' }],
  },
  {
    name: 'sales_representative_details',
    privileges: [{ action: 'read', subject: 'sales_representative' }],
  },
  {
    name: 'sales_representative_update',
    privileges: [{ action: 'edit', subject: 'sales_representative' }],
  },
  {
    name: 'sales_representative_delete',
    privileges: [{ action: 'delete', subject: 'sales_representative' }],
  },
  {
    name: 'item_group_create',
    privileges: [{ action: 'create', subject: 'item_group' }],
  },
  {
    name: 'item_group_list',
    privileges: [{ action: 'list', subject: 'item_group' }],
  },
  {
    name: 'item_group_details',
    privileges: [{ action: 'read', subject: 'item_group' }],
  },
  {
    name: 'item_group_update',
    privileges: [{ action: 'edit', subject: 'item_group' }],
  },
  {
    name: 'item_group_delete',
    privileges: [{ action: 'delete', subject: 'item_group' }],
  },
  {
    name: 'item_create',
    privileges: [
      { action: 'create', subject: 'item' },
      { action: 'list', subject: 'abstract_item_groups' },
      { action: 'list', subject: 'abstract_units' },
    ],
  },
  {
    name: 'item_list',
    privileges: [{ action: 'list', subject: 'item' }],
  },
  {
    name: 'item_details',
    privileges: [{ action: 'read', subject: 'item' }],
  },
  {
    name: 'item_update',
    privileges: [
      { action: 'edit', subject: 'item' },
      { action: 'list', subject: 'abstract_item_groups' },
      { action: 'list', subject: 'abstract_units' },
    ],
  },
  {
    name: 'item_delete',
    privileges: [{ action: 'delete', subject: 'item' }],
  },
  {
    name: 'service_item_group_create',
    privileges: [{ action: 'create', subject: 'service_item_group' }],
  },
  {
    name: 'service_item_group_list',
    privileges: [{ action: 'list', subject: 'service_item_group' }],
  },
  {
    name: 'service_item_group_details',
    privileges: [{ action: 'read', subject: 'service_item_group' }],
  },
  {
    name: 'service_item_group_update',
    privileges: [{ action: 'edit', subject: 'service_item_group' }],
  },
  {
    name: 'service_item_group_delete',
    privileges: [{ action: 'delete', subject: 'service_item_group' }],
  },
  {
    name: 'service_item_create',
    privileges: [
      { action: 'create', subject: 'service_item' },
      { action: 'list', subject: 'abstract_service_item_groups' },
      { action: 'list', subject: 'abstract_units' },
    ],
  },
  {
    name: 'service_item_list',
    privileges: [{ action: 'list', subject: 'service_item' }],
  },
  {
    name: 'service_item_details',
    privileges: [{ action: 'read', subject: 'service_item' }],
  },
  {
    name: 'service_item_update',
    privileges: [
      { action: 'edit', subject: 'service_item' },
      { action: 'list', subject: 'abstract_service_item_groups' },
      { action: 'list', subject: 'abstract_units' },
    ],
  },
  {
    name: 'service_item_delete',
    privileges: [{ action: 'delete', subject: 'service_item' }],
  },
  {
    name: 'unit_create',
    privileges: [{ action: 'create', subject: 'unit' }],
  },
  {
    name: 'unit_list',
    privileges: [{ action: 'list', subject: 'unit' }],
  },
  {
    name: 'unit_details',
    privileges: [{ action: 'read', subject: 'unit' }],
  },
  {
    name: 'unit_update',
    privileges: [{ action: 'edit', subject: 'unit' }],
  },
  {
    name: 'unit_delete',
    privileges: [{ action: 'delete', subject: 'unit' }],
  },
  {
    name: 'service_item_unit_create',
    privileges: [{ action: 'create', subject: 'service_item_unit' }],
  },
  {
    name: 'service_item_unit_list',
    privileges: [{ action: 'list', subject: 'service_item_unit' }],
  },
  {
    name: 'service_item_unit_details',
    privileges: [{ action: 'read', subject: 'service_item_unit' }],
  },
  {
    name: 'service_item_unit_update',
    privileges: [{ action: 'edit', subject: 'service_item_unit' }],
  },
  {
    name: 'service_item_unit_delete',
    privileges: [{ action: 'delete', subject: 'service_item_unit' }],
  },
  {
    name: 'store_adjustment_create',
    privileges: [{ action: 'create', subject: 'store_adjustment' }],
  },
  {
    name: 'store_adjustment_list',
    privileges: [{ action: 'list', subject: 'store_adjustment' }],
  },
  {
    name: 'store_adjustment_details',
    privileges: [{ action: 'read', subject: 'store_adjustment' }],
  },
  {
    name: 'store_adjustment_update',
    privileges: [{ action: 'edit', subject: 'store_adjustment' }],
  },
  {
    name: 'store_adjustment_delete',
    privileges: [{ action: 'delete', subject: 'store_adjustment' }],
  },
  {
    name: 'physical_inventory_create',
    privileges: [
      { action: 'create', subject: 'physical_inventory' },
      { action: 'list', subject: 'abstract_item_groups' },
    ],
  },
  {
    name: 'physical_inventory_list',
    privileges: [{ action: 'list', subject: 'physical_inventory' }],
  },
  {
    name: 'physical_inventory_details',
    privileges: [{ action: 'read', subject: 'physical_inventory' }],
  },
  {
    name: 'physical_inventory_delete',
    privileges: [{ action: 'delete', subject: 'physical_inventory' }],
  },
  {
    name: 'physical_inventory_update',
    privileges: [{ action: 'edit', subject: 'physical_inventory' }],
  },
  {
    name: 'physical_inventory_posting_create',
    privileges: [{ action: 'create', subject: 'physical_inventory_posting' }],
  },
  {
    name: 'physical_inventory_posting_list',
    privileges: [{ action: 'list', subject: 'physical_inventory_posting' }],
  },
  {
    name: 'physical_inventory_posting_details',
    privileges: [{ action: 'read', subject: 'physical_inventory_posting' }],
  },
  {
    name: 'physical_inventory_posting_delete',
    privileges: [{ action: 'delete', subject: 'physical_inventory_posting' }],
  },
  {
    name: 'invoice_template_create',
    privileges: [{ action: 'create', subject: 'invoice_template' }],
  },
  {
    name: 'invoice_template_list',
    privileges: [{ action: 'list', subject: 'invoice_template' }],
  },
  {
    name: 'invoice_template_details',
    privileges: [{ action: 'read', subject: 'invoice_template' }],
  },
  {
    name: 'invoice_template_update',
    privileges: [{ action: 'edit', subject: 'invoice_template' }],
  },
  {
    name: 'invoice_template_delete',
    privileges: [{ action: 'delete', subject: 'invoice_template' }],
  },
  {
    name: 'accounting_node_create',
    privileges: [{ action: 'create', subject: 'accounting_node' }],
  },
  {
    name: 'accounting_node_list',
    privileges: [{ action: 'list', subject: 'accounting_node' }],
  },
  {
    name: 'accounting_node_details',
    privileges: [{ action: 'read', subject: 'accounting_node' }],
  },
  {
    name: 'accounting_node_update',
    privileges: [{ action: 'edit', subject: 'accounting_node' }],
  },
  {
    name: 'accounting_node_delete',
    privileges: [{ action: 'delete', subject: 'accounting_node' }],
  },
  {
    name: 'accounting_node_tree_details',
    privileges: [{ action: 'read', subject: 'accounting_node_tree' }],
  },
  {
    name: 'accounting_node_template_create',
    privileges: [{ action: 'create', subject: 'accounting_node_template' }],
  },
  {
    name: 'accounting_node_template_list',
    privileges: [{ action: 'list', subject: 'accounting_node_template' }],
  },
  {
    name: 'accounting_node_template_details',
    privileges: [{ action: 'read', subject: 'accounting_node_template' }],
  },
  {
    name: 'accounting_node_template_update',
    privileges: [{ action: 'edit', subject: 'accounting_node_template' }],
  },
  {
    name: 'accounting_node_template_delete',
    privileges: [{ action: 'delete', subject: 'accounting_node_template' }],
  },
  {
    name: 'accounting_template_create',
    privileges: [{ action: 'create', subject: 'accounting_template' }],
  },
  {
    name: 'accounting_template_list',
    privileges: [{ action: 'list', subject: 'accounting_template' }],
  },
  {
    name: 'accounting_template_details',
    privileges: [{ action: 'read', subject: 'accounting_template' }],
  },
  {
    name: 'accounting_template_update',
    privileges: [{ action: 'edit', subject: 'accounting_template' }],
  },
  {
    name: 'accounting_template_delete',
    privileges: [{ action: 'delete', subject: 'accounting_template' }],
  },
  {
    name: 'fiscal_year_create',
    privileges: [{ action: 'create', subject: 'fiscal_year' }],
  },
  {
    name: 'fiscal_year_list',
    privileges: [{ action: 'list', subject: 'fiscal_year' }],
  },
  {
    name: 'fiscal_year_details',
    privileges: [{ action: 'read', subject: 'fiscal_year' }],
  },
  {
    name: 'fiscal_year_update',
    privileges: [{ action: 'edit', subject: 'fiscal_year' }],
  },
  {
    name: 'fiscal_year_delete',
    privileges: [{ action: 'delete', subject: 'fiscal_year' }],
  },
  {
    name: 'journal_create',
    privileges: [{ action: 'create', subject: 'journal' }],
  },
  {
    name: 'journal_list',
    privileges: [{ action: 'list', subject: 'journal' }],
  },
  {
    name: 'journal_details',
    privileges: [{ action: 'read', subject: 'journal' }],
  },
  {
    name: 'journal_update',
    privileges: [{ action: 'edit', subject: 'journal' }],
  },
  {
    name: 'journal_delete',
    privileges: [{ action: 'delete', subject: 'journal' }],
  },
  {
    name: 'receipt_voucher_create',
    privileges: [{ action: 'create', subject: 'receipt_voucher' }],
  },
  {
    name: 'receipt_voucher_list',
    privileges: [{ action: 'list', subject: 'receipt_voucher' }],
  },
  {
    name: 'receipt_voucher_details',
    privileges: [{ action: 'read', subject: 'receipt_voucher' }],
  },
  {
    name: 'receipt_voucher_update',
    privileges: [{ action: 'edit', subject: 'receipt_voucher' }],
  },
  {
    name: 'receipt_voucher_delete',
    privileges: [{ action: 'delete', subject: 'receipt_voucher' }],
  },
  {
    name: 'payment_voucher_create',
    privileges: [{ action: 'create', subject: 'payment_voucher' }],
  },
  {
    name: 'payment_voucher_list',
    privileges: [{ action: 'list', subject: 'payment_voucher' }],
  },
  {
    name: 'payment_voucher_details',
    privileges: [{ action: 'read', subject: 'payment_voucher' }],
  },
  {
    name: 'payment_voucher_update',
    privileges: [{ action: 'edit', subject: 'payment_voucher' }],
  },
  {
    name: 'payment_voucher_delete',
    privileges: [{ action: 'delete', subject: 'payment_voucher' }],
  },
  {
    name: 'memo_voucher_create',
    privileges: [{ action: 'create', subject: 'memo_voucher' }],
  },
  {
    name: 'memo_voucher_list',
    privileges: [{ action: 'list', subject: 'memo_voucher' }],
  },
  {
    name: 'memo_voucher_details',
    privileges: [{ action: 'read', subject: 'memo_voucher' }],
  },
  {
    name: 'memo_voucher_update',
    privileges: [{ action: 'edit', subject: 'memo_voucher' }],
  },
  {
    name: 'memo_voucher_delete',
    privileges: [{ action: 'delete', subject: 'memo_voucher' }],
  },
  {
    name: 'temp_beginning_balance_update',
    privileges: [{ action: 'edit', subject: 'temp_beginning_balance' }],
  },
  {
    name: 'temp_beginning_balance_create',
    privileges: [{ action: 'create', subject: 'temp_beginning_balance' }],
  },
  {
    name: 'temp_beginning_balance_list',
    privileges: [
      { action: 'list', subject: 'temp_beginning_balance' },
      { action: 'list', subject: 'abstract_accounting_node' },
    ],
  },
  {
    name: 'cashier_report_list',
    privileges: [
      { action: 'list', subject: 'cashier_report' },
      { action: 'list', subject: 'abstract_accounting_node' },
    ],
  },
  {
    name: 'vendor_report_list',
    privileges: [
      { action: 'list', subject: 'vendor_report' },
      { action: 'list', subject: 'abstract_accounting_node' },
    ],
  },
  {
    name: 'customer_report_list',
    privileges: [
      { action: 'list', subject: 'customer_report' },
      { action: 'list', subject: 'abstract_accounting_node' },
    ],
  },
  {
    name: 'account_report_list',
    privileges: [
      { action: 'list', subject: 'account_report' },
      { action: 'list', subject: 'abstract_accounting_node' },
    ],
  },
  {
    name: 'journal_report_list',
    privileges: [
      { action: 'list', subject: 'journal_report' },
      { action: 'list', subject: 'abstract_accounting_node' },
    ],
  },
  {
    name: 'voucher_report_list',
    privileges: [
      { action: 'list', subject: 'voucher_report' },
      { action: 'list', subject: 'abstract_accounting_node' },
    ],
  },
  {
    name: 'trail_balance_report_list',
    privileges: [{ action: 'list', subject: 'trail_balance_report' }],
  },
  {
    name: 'income_statement_report',
    privileges: [{ action: 'list', subject: 'income_statement_report' }],
  },
  {
    name: 'balance_sheet_report',
    privileges: [{ action: 'list', subject: 'balance_sheet_report' }],
  },
  {
    name: 'physical_inventory_posting_report',
    privileges: [
      { action: 'list', subject: 'physical_inventory_posting_report' },
      { action: 'list', subject: 'physical_inventory_posting' },
      { action: 'list', subject: 'abstract_physical_inventory' },
      { action: 'list', subject: 'abstract_item_groups' },
      { action: 'read', subject: 'report_request' },
    ],
  },
  {
    name: 'report_request_list',
    privileges: [{ action: 'list', subject: 'report_request' }],
  },
  {
    name: 'report_request_details',
    privileges: [{ action: 'read', subject: 'report_request' }],
  },
  {
    name: 'user_create',
    privileges: [
      { action: 'create', subject: 'users' },
      { action: 'list', subject: 'abstract_roles_and_permissions' },
    ],
  },
  {
    name: 'user_list',
    privileges: [{ action: 'list', subject: 'users' }],
  },
  {
    name: 'user_details',
    privileges: [{ action: 'read', subject: 'users' }],
  },
  {
    name: 'user_update',
    privileges: [{ action: 'edit', subject: 'users' }],
  },
  {
    name: 'user_delete',
    privileges: [{ action: 'delete', subject: 'users' }],
  },
  {
    name: 'company_information_update',
    privileges: [{ action: 'edit', subject: 'company_information' }],
  },
  {
    name: 'company_information_details',
    privileges: [{ action: 'read', subject: 'company_information' }],
  },
  {
    name: 'branch_create',
    privileges: [{ action: 'create', subject: 'branches_management' }],
  },
  {
    name: 'branch_update',
    privileges: [{ action: 'edit', subject: 'branches_management' }],
  },
  {
    name: 'branch_list',
    privileges: [{ action: 'list', subject: 'branches_management' }],
  },
  {
    name: 'branch_details',
    privileges: [{ action: 'read', subject: 'branches_management' }],
  },
  {
    name: 'branch_delete',
    privileges: [{ action: 'delete', subject: 'branches_management' }],
  },

  {
    name: 'permissions_list',
    privileges: [{ action: 'list', subject: 'permissions' }],
  },

  {
    name: 'roles_and_permissions_create',
    privileges: [{ action: 'create', subject: 'roles_and_permissions' }],
  },
  {
    name: 'roles_and_permissions_update',
    privileges: [{ action: 'edit', subject: 'roles_and_permissions' }],
  },
  {
    name: 'roles_and_permissions_list',
    privileges: [{ action: 'list', subject: 'roles_and_permissions' }],
  },
  {
    name: 'roles_and_permissions_details',
    privileges: [{ action: 'read', subject: 'roles_and_permissions' }],
  },
  {
    name: 'roles_and_permissions_delete',
    privileges: [{ action: 'delete', subject: 'roles_and_permissions' }],
  },
  {
    name: 'item_activity_report_list',
    privileges: [
      { action: 'list', subject: 'item_activity_report' },
      { action: 'read', subject: 'report_request' },
      { action: 'list', subject: 'abstract_item_groups' },
      { action: 'list', subject: 'item' },
    ],
  },

  {
    name: 'sales_invoice_report_list',
    privileges: [
      { action: 'list', subject: 'sales_report' },
      { action: 'list', subject: 'abstract_sales_representative' },
      { action: 'list', subject: 'abstract_customers' },
      { action: 'list', subject: 'abstract_stores' },
    ],
  },
  {
    name: 'purchases_invoice_report',
    privileges: [
      { action: 'list', subject: 'purchases_report' },
      { action: 'list', subject: 'abstract_vendors' },
      { action: 'list', subject: 'abstract_stores' },
    ],
  },
];
