export enum queueName {
  //  tenant_created_event = 'tenant_created_event',
  // branch_created_event = 'branch_created_event',
  users_queue = 'users_queue',
  account_queue = 'account_queue',
  //company_queue = 'company_queue',
  notifications_queue = 'notifications_queue',
  //setting_queue = 'setting_queue',
  // update_setting_queue = 'update_setting_queue',
  //tenant_queue = 'tenant_queue',
  tenant_service_queue = 'tenant_service_queue',
  inventory_queue = 'inventory_queue',
  trade_queue = 'trade_queue',
}

export enum tradeMessagePattern {
  create_customer_group = 'create_customer_group',
  create_vendor_group = 'create_vendor_group',
  update_sale = 'update_sale',
  seed_policy = 'seed_policy',
  update_policy = 'update_policy',
  upsert_policy = 'upsert_policy',
  get_policies = 'get_policies',
  get_auto_numbering = 'get_auto_numbering',
  update_auto_numbering = 'update_auto_numbering',
  sign_onboarding_invoice = 'sign_onboarding_invoice',

  //Dashboard
  get_invoices_count = 'get_invoices_count',
  get_recent_invoices = 'get_recent_invoices',
  get_invoice_statistics = 'get_invoice_statistics',
}

export enum notificationMessagePattern {
  send_email = 'send_email',
  create_notification = 'create_notification',
}

export enum userMessagePattern {
  on_board_status = 'on_board_status',
  check_abilities = 'check_abilities',
  get_settings = 'get_settings',
  get_company_settings = 'get_company_settings',
  get_company = 'get_company',
  get_company_csid = 'get_company_csid',
  update = 'update',
  update_event = 'update_event',
  create_payment_type = 'create_payment_type',
  payment_types_get_one = 'payment_types_get_one',
  create_local_tenant = 'create_local_tenant',
  update_doc_number = 'update_doc_number',
  update_document_numbering = 'update_document_numbering',
  update_policy = 'update_policy',
  seed_policy = 'seed_policy',
  get_branches = 'get_branches',
  get_user = 'get_user',
  drop_user_branch = 'drop_user_branch',
  get_one_branch = 'get_one_branch',
  get_zatca_production_csid = 'get_zatca_production_csid',
  disable_document_policy = 'disable_document_policy',
}

export enum generalEventPattern {
  tenant_created = 'tenant_created',
  tenant_deleted = 'tenant_deleted',
}

export enum accountingMessagePattern {
  populate_account = 'populate_account',
  fill_ac_node = 'fill_ac_node',
  abstract_accounting_node_code = 'abstract_accounting_node_code',
  get_single_accounting_node = 'get_single_accounting_node',
  has_any_accounting_node = 'has_any_accounting_node',
  get_journal = 'get_journal',
  seed_policy = 'seed_policy',
  update_policy = 'update_policy',
  upsert_policy = 'upsert_policy',
  get_auto_numbering = 'get_auto_numbering',
  update_auto_numbering = 'update_auto_numbering',
  abstract_accounting_node = 'abstract_accounting_node',

  //Dashboard
  get_income_statement = 'get_income_statement',
  get_bank_accounts = 'get_bank_accounts',
}

export enum inventoryMessagePattern {
  create_item_group = 'create_item_group',
  create_unit = 'create_unit',
  has_any_store = 'has_any_store',
  seed_policy = 'seed_policy',
  update_policy = 'update_policy',
  upsert_policy = 'upsert_policy',
  get_auto_numbering = 'get_auto_numbering',
  update_auto_numbering = 'update_auto_numbering',
  branch_created_event = 'branch_created',
}

export enum metricsMessagePattern {
  increment_counter = 'increment_counter',
  set_gauge = 'set_gauge',
  observe_histogram = 'observe_histogram',
  observe_summary = 'observe_summary',
}

export enum tenantMessagePattern {
  create_tenant = 'create_tenant',
  create_tenant_user_bulk = 'create_tenant_user_bulk',
  plain_create_tenant = 'plain_create_tenant',
  get_tenant = 'get_tenant',
  get_last_code = 'get_last_code',
  get_tenant_codes = 'get_tenant_codes',
  find_tenant = 'find_tenant',
  find_tenant_user = 'find_tenant_user',
  find_one_tenant_user = 'find_one_tenant_user',
  update_tenant = 'update_tenant',
  update_tenant_user = 'update_tenant_user',
  delete_tenant_user = 'delete_tenant_user',
  tenant_created = 'tenant_created',
  tenant_deleted = 'tenant_deleted',
  get_tenant_features = 'get_tenant_features',
  check_feature_accessibility = 'check_feature_accessibility',
}
