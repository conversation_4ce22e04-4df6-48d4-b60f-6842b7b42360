import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';
import { Types } from 'mongoose';
import { safeMongoIdTransformer } from '../transformers/mongo-id-transformer';

export class IdValidatorDto {
  @ApiProperty({ required: true, type: 'string' })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  _id: Types.ObjectId;
}
