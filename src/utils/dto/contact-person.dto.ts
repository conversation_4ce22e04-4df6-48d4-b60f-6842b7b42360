import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsPhoneNumber, IsOptional, IsEmail } from 'class-validator';

export class ContactPersonDto {
  @ApiProperty({
    description: 'contact person name',
    example: '<PERSON>',
    required: false,
  })
  @IsOptional()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'contact person email',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'contact person phone',
    example: '+201122334455',
    required: false,
  })
  @IsOptional()
  @IsPhoneNumber()
  phone: string;

  @ApiProperty({
    description: 'contact person mobile',
    example: '+201122334455',
    required: false,
  })
  @IsOptional()
  @IsPhoneNumber()
  mobile: string;
}
