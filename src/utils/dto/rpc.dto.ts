import { Types } from 'mongoose';
import { IsNotEmpty, IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';
import { safeMongoIdTransformer } from '../transformers/mongo-id-transformer';

export class RpcDto {
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch?: Types.ObjectId;

  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  user_id?: Types.ObjectId;

  @IsNotEmpty()
  @IsNumber()
  code?: number;
}
