import { HttpException } from '@nestjs/common';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsObjectId } from 'class-validator-mongo-object-id';
import { Types } from 'mongoose';

export class BranchHeaderDto {
  @ApiProperty({
    description: 'brnach in headers',
    example: '6347e554fcf62efbbe757b66',
    required: false,
  })
  @IsObjectId()
  @Transform((value) => safeMongoIdTransform(value))
  branch: Types.ObjectId;
}

export const safeMongoIdTransform = ({ value }) => {
  try {
    if (
      Types.ObjectId.isValid(value) &&
      new Types.ObjectId(value).toString() === value
    ) {
      console.log(value);

      return new Types.ObjectId(value);
    }
    throw new HttpException('Id validation fail', 404);
  } catch (error) {
    throw new HttpException('Id validation fail', 404);
  }
};
