import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsIn } from 'class-validator';

export class FindCommonDto {
  @ApiProperty({
    description: 'field to sort by',
    example: 'id',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  sortBy? = 'createdAt';

  @ApiProperty({
    description: 'sort type asc , desc',
    example: 'desc',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['asc', 'desc'])
  sortType? = 'desc';
}
