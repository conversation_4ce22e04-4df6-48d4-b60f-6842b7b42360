import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { languages } from '../languages';

export class NameDto {
  @ApiProperty()
  @IsNotEmpty()
  en: string;

  @ApiProperty()
  @IsNotEmpty()
  ar: string;
}

@ValidatorConstraint({ name: 'language_ISO639', async: false })
export class validateLanguage implements ValidatorConstraintInterface {
  validate(object: Record<string, string>[]) {
    if (!object) return false;
    const keys = Object.keys(object);
    return !!(
      (object['en'] || object['ar']) &&
      keys.every((ai) => languages.includes(ai))
    );
  }

  defaultMessage() {
    return 'must be ISO639-1 language!';
  }
}

@ValidatorConstraint({ name: 'arLanguage', async: false })
export class validateArLanguage implements ValidatorConstraintInterface {
  validate(object: Record<string, string>[]) {
    if (!object) return false;
    return !!object['ar'];
  }

  defaultMessage() {
    return 'must contain "ar"';
  }
}

@ValidatorConstraint({ name: 'enLanguage', async: false })
export class validateEnLanguage implements ValidatorConstraintInterface {
  validate(object: Record<string, string>[]) {
    if (!object) return false;
    return !!object['en'];
  }

  defaultMessage() {
    return 'must contain "en"';
  }
}
