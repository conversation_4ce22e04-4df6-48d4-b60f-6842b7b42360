import { IsInt, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { Model } from 'mongoose';
import { FindCommonDto } from './find-common.dto';

export class PaginationDto extends FindCommonDto {
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  public page?: number = 1;
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  public limit?: number = 10;
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  queries?: string;
}
export type PaginationMetaType = {
  pages: number | string;
  total: number;
  prev: number;
  next: number;
  page: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
};
export async function paginate<limit, page, model, query>(
  limit: number,
  page: number,
  model: Model<any>,
  query: any,
): Promise<PaginationMetaType> {
  const total = await model.countDocuments(query);
  const pages =
    parseInt((total / limit).toFixed()) == 0
      ? 1
      : parseInt((total / limit).toFixed());
  return {
    pages,
    total,
    prev: page - 1,
    page,
    next: page + 1,
    hasNextPage: pages !== page,
    hasPrevPage: page > 1,
  };
}

export async function aggregatePaginate<limit, page, model, query>(
  limit: number,
  page: number,
  model: Model<any>,
  query: any,
): Promise<PaginationMetaType> {
  const total =
    (await model.aggregate([...query, { $count: 'totalCount' }]))[0]
      ?.totalCount || (0 as any);

  const pages =
    parseInt((total / limit).toFixed()) == 0
      ? 1
      : parseInt((total / limit).toFixed());
  return {
    pages,
    total,
    prev: page - 1,
    page,
    next: page + 1,
    hasNextPage: pages !== page,
    hasPrevPage: page > 1,
  };
}
