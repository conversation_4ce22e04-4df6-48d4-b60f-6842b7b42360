export function mergeTransactions(transactions) {
  const mergedTransactions = transactions.reduce((acc, curr) => {
    // Check if an item with the same 'item' and 'unit' already exists in the accumulator
    const existingTransaction = acc.find(
      (transaction) =>
        transaction.item === curr.item && transaction.unit === curr.unit,
    );

    if (existingTransaction) {
      // If exists, sum the qty
      existingTransaction.qty += curr.qty;
    } else {
      // If not, add the current transaction to the accumulator
      acc.push({ ...curr });
    }

    return acc;
  }, []);
  return mergedTransactions;
}
