import { Schema, Document, Model } from 'mongoose';

export interface SoftDeleteDocument extends Document {
  deletedAt: Date | null;
  softDelete(): Promise<this>;
  restore(): Promise<this>;
}

export interface SoftDeleteModel<T extends Document> extends Model<T> {
  findNotDeleted(): Promise<T[]>;
  findByIdWithDeleted(id: string): Promise<T | null>;
}

export const softDeletePlugin = (schema: Schema) => {
  schema.add({ deletedAt: { type: Date, default: null } });

  // Pre-find middleware to automatically exclude soft-deleted documents
  schema.pre('find', function () {
    this.where({ deletedAt: null });
  });

  schema.pre('findOne', function () {
    if (!this.getOptions()?.overrideSoftDelete) {
      this.where({ deletedAt: null }); // Default soft delete behavior
    }
  });

  schema.pre('countDocuments', function () {
    this.where({ deletedAt: null });
  });

  schema.pre('aggregate', function () {
    this.pipeline().unshift({ $match: { deletedAt: null } });
  });

  schema.methods.softDelete = function (): Promise<SoftDeleteDocument> {
    this.deletedAt = new Date();
    return this.save();
  };

  schema.methods.restore = function (): Promise<SoftDeleteDocument> {
    this.deletedAt = null;
    return this.save();
  };

  schema.statics.findByIdWithDeleted = function (id) {
    return (
      this.findOne({ _id: id })
        // eslint-disable-next-line @typescript-eslint/naming-convention
        .setOptions({ overrideSoftDelete: true })
        .exec()
    );
  };

  schema.statics.findNotDeleted = function () {
    return this.find({ deletedAt: null });
  };
};
