import * as jsrsasign from 'jsrsasign';

/* eslint-disable @typescript-eslint/naming-convention */

type CertificateDN = {
  countryName: string; // C
  stateOrProvinceName: string; // ST
  localityName: string; // L
  organizationName: string; // O
  organizationalUnitName?: string; // OU (optional)
  commonName: string; // CN
  emailAddress?: string; // emailAddress (optional)
  // eslint-disable-next-line @typescript-eslint/naming-convention
  egs_serial: string;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  invoice_type: string;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  organization_identifier: string;
  location: string;
  industry: string;
};
type KeyType = {
  privateKey: string;
  publicKey: string;
};
import axios from 'axios';
import { HttpException } from '@nestjs/common';
import { nameOf } from './object-key-name';
import { usersExceptionCode } from '../exceptions/exception-code.users';

export class EcdsaZatcaCsrGenerator {
  private keyPairs: KeyType;
  private certificateDN: CertificateDN;
  private csr: string;
  private otp: number;
  private csrData: CsrDataType;
  private keyParisProvided: boolean = false;

  constructor(certificateDN: CertificateDN, otp: number, keyParis?: KeyType) {
    this.certificateDN = certificateDN;
    this.otp = otp;
    this.keyPairs =
      keyParis.privateKey && keyParis.privateKey
        ? this.validateKeyPairs(keyParis)
        : this.generateKeyPairs();

    this.generateCsr();
  }

  private generateKeyPairs = () => {
    const kp = jsrsasign.KEYUTIL.generateKeypair('EC', 'secp256k1');
    const pub = kp.pubKeyObj;
    const prv = kp.prvKeyObj;
    const privateKey = jsrsasign.KEYUTIL.getPEM(prv, 'PKCS8PRV');
    const publicKey = jsrsasign.KEYUTIL.getPEM(pub);
    return { privateKey, publicKey };
  };

  private validateKeyPairs = (keyPairs: KeyType) => {
    // TODO: need to improved
    if (keyPairs.privateKey && keyPairs.publicKey) {
      this.keyParisProvided = true;
      return keyPairs;
    }
    throw new Error('key not validated');
  };

  private generateCsr() {
    const csr = new jsrsasign.KJUR.asn1.csr.CertificationRequest({
      subject: {
        str: `/C=${this.certificateDN.countryName}/ST=${this.certificateDN.stateOrProvinceName}/L=${this.certificateDN.localityName}/O=${this.certificateDN.organizationName}/OU=${this.certificateDN.organizationalUnitName}/CN=${this.certificateDN.commonName}/emailAddress=${this.certificateDN.emailAddress}`,
      },
      sbjpubkey: this.keyPairs.publicKey,
      extreq: [
        {
          extname: '*******.4.1.311.20.2',
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-expect-error
          extn: { prnstr: { str: 'PREZATCA-Code-Signing' } },
        },
        /*         {
                  extname: 'basicConstraints',
                  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                  // @ts-expect-error
                  cA: false, // CA
                }, */
        /*       {
                  extname: 'keyUsage',
                  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                  // @ts-expect-error
                  names: ['digitalSignature', 'keyEncipherment', 'clientAuth'],
                  critical: false,
                }, */
        {
          extname: 'subjectAltName',
          array: [
            {
              dn: new jsrsasign.KJUR.asn1.x509.X500Name({
                str: `/SN=${this.certificateDN.egs_serial}/title=${this.certificateDN.invoice_type}/UID=${this.certificateDN.organization_identifier}/********=${this.certificateDN.location}/businessCategory=${this.certificateDN.industry}`,
              }),
            },
          ],
        },
      ],
      sigalg: 'SHA256withECDSA',
      sbjprvkey: this.keyPairs.privateKey,
    });
    this.csr = csr.getPEM();
  }

  private async submitZatcaCsr() {
    try {
      const rs = await axios.post(
        'https://gw-fatoora.zatca.gov.sa/e-invoicing/simulation/compliance',
        {
          csr: Buffer.from(this.csr).toString('base64'),
        },
        {
          headers: {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            'Accept-Version': 'V2',
            // eslint-disable-next-line @typescript-eslint/naming-convention
            Otp: this.otp,
          },
        },
      );
      this.csrData = rs.data;
      return rs;
    } catch (error) {
      console.log(error.response);
      console.log(error.response.data);

      throw new HttpException(
        nameOf(usersExceptionCode, (x) => x.zatcaError),
        usersExceptionCode.zatcaError,
      );
    }
  }

  public async exportData(): Promise<ExportDataType> {
    await this.submitZatcaCsr();

    // eslint-disable-next-line @typescript-eslint/naming-convention
    const rs: ExportDataType = { csrData: this.csrData };
    if (this.keyParisProvided == false) {
      rs.keyPairs = this.keyPairs;
    }
    return rs;
  }
}

type ExportDataType = {
  csrData: CsrDataType;
  keyPairs?: KeyType;
};

type CsrDataType = {
  requestID: number;
  dispositionMessage: string;
  binarySecurityToken: string;
  secret: string;
};
