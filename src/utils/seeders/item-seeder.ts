import { faker, fakerAR } from '@faker-js/faker';
import mongoose, { Types } from 'mongoose';
import { itemSchema } from '../../modules/inventory-item/schema/item.schema';
import { itemType } from '../../modules/inventory-item/types/item-types.enum';

export class ItemSeeder {
  itemModel: any;
  branchItemModel: any;

  constructor(private db: any) {
    this.db = db;
    this.itemModel = this.db.model('items', itemSchema);
  }

  static itemId = {
    chairs: faker.number.int(),
    tables: faker.number.int(),
    cocacola: faker.number.int(),
    pepsi: faker.number.int(),
    cover: faker.number.int(),
    cepper: faker.number.int(),
    cardamom: faker.number.int(),
    tumeric: faker.number.int(),
    car_wash: faker.number.int(),
    car_repair: faker.number.int(),
  };

  static mergedAccountListSet: Set<string> = new Set([
    ...String(this.itemId.chairs),
  ]);

  static generateUniqueObjectId(): mongoose.Types.ObjectId {
    let newId: mongoose.Types.ObjectId;

    do {
      newId = new mongoose.Types.ObjectId();
    } while (this.mergedAccountListSet.has(newId.toHexString()));

    return newId;
  }

  async stressSeeding(chairsGroupId: Types.ObjectId, unitIds: any) {
    const itemsData = [];
    for (let i = 100; i < 2100; i++) {
      const item = {
        //beach chairs
        name: {
          en: faker.commerce.productName(),
          ar: fakerAR.commerce.productName(),
        },
        code: i,
        abbreviation: faker.commerce.productDescription(),
        country: faker.location.countryCode(),
        active: true,
        note: faker.commerce.productDescription(),
        min_quantity: 5,
        max_quantity: 50,
        record_point: 10,
        cash_only: false,
        is_service: false,
        tax_on_sales: true,
        tax_on_purchases: true,
        modified_by: '',
        vendors: [],
        costs: [],
        item_group: chairsGroupId,
        _id: ItemSeeder.generateUniqueObjectId(),
        id: faker.number.int(),
        units: [
          {
            unit: unitIds.piece,
            is_default: true,
            unit_up: unitIds.piece,
            factor: 1,
            base_factor: 1,
            barcode1: '',
            barcode2: '',
            sales_price: 22,
            wholesale_price: 17,
            special_price: 20,
            profit_margin: 15,
            active: true,
          },
          {
            unit: unitIds.ten_pic,
            is_default: false,
            unit_up: unitIds.piece,
            factor: 10,
            base_factor: 10,
            barcode1: '',
            barcode2: '',
            sales_price: 200,
            wholesale_price: 170,
            special_price: 190,
            profit_margin: 10,
            active: true,
          },
        ],
      };

      itemsData.push(item);
    }

    const items = await this.itemModel.create(itemsData);

    return items;
  }

  async seed(
    chairsGroupId: number,
    tablesGroupId: number,
    drinksGroupId: number,
    spicesGroupId: number,
    plasticGroupId: number,
    washGroupId: number,
    repairGroupId: number,
    unitData: any,
    storesList: any,
  ) {
    const itemsData = [
      //items
      {
        //beach chairs
        name: { en: 'beach chairs', ar: 'كراسي بحر' },
        id: 1,
        code: '1',
        active: true,
        type: itemType.goods,
        tax_on_sales: true,
        item_group: chairsGroupId,
        // _id: ItemSeeder.itemId.chairs,
        average_cost_in_base_unit: 10,
        max_quantity_in_base_unit: 10,
        units: [
          {
            unit: unitData[2].id,
            is_default: true,
            barcode1: '',
            barcode2: '',
            sales_price: 22,
            wholesale_price: 17,
            special_price: 20,
            quantity: 100,
            avg_cost: 10,
          },
          {
            unit: unitData[3].id,
            is_default: false,
            barcode1: '',
            barcode2: '',
            sales_price: 200,
            wholesale_price: 170,
            special_price: 190,
            quantity: 100,
            avg_cost: 10,
          },
        ],
      },
      {
        //wooden
        'name.ar': 'طاولة خشب',
        'name.en': 'wooden table',
        id: 2,
        code: '2',
        active: true,
        type: itemType.goods,
        tax_on_sales: true,
        item_group: tablesGroupId,
        // _id: ItemSeeder.itemId.tables,
        average_cost_in_base_unit: 10,
        max_quantity_in_base_unit: 10,
        units: [
          {
            unit: unitData[2].id,
            is_default: true,
            barcode1: '',
            barcode2: '',
            sales_price: 100,
            wholesale_price: 80,
            special_price: 90,
            quantity: 100,
            avg_cost: 10,
          },
          {
            unit: unitData[3].id,
            is_default: false,
            barcode1: '',
            barcode2: '',
            sales_price: 900,
            wholesale_price: 750,
            special_price: 850,
            quantity: 100,
            avg_cost: 10,
          },
        ],
      },
      {
        //cocacola
        'name.ar': 'كوكاكولا',
        'name.en': 'cocacola',
        id: 3,
        code: '3',
        active: true,
        type: itemType.goods,
        tax_on_sales: true,
        item_group: drinksGroupId,
        // _id: ItemSeeder.itemId.cocacola,
        average_cost_in_base_unit: 10,
        max_quantity_in_base_unit: 10,
        units: [
          {
            unit: unitData[2].id,
            is_default: true,
            barcode1: '',
            barcode2: '',
            sales_price: 1.5,
            wholesale_price: 1.3,
            special_price: 1.4,
            quantity: 100,
            avg_cost: 10,
          },
          {
            unit: unitData[4].id,
            is_default: false,
            barcode1: '',
            barcode2: '',
            sales_price: 14,
            wholesale_price: 12,
            special_price: 13,
            quantity: 100,
            avg_cost: 10,
          },
          {
            unit: unitData[5].id,
            is_default: false,
            barcode1: '',
            barcode2: '',
            sales_price: 30,
            wholesale_price: 25,
            special_price: 28,
            quantity: 100,
            avg_cost: 10,
          },
        ],
      },
      {
        //Pepsi
        'name.ar': 'بيبسي',
        'name.en': 'Pepsi',
        id: 4,
        code: '4',
        active: true,
        type: itemType.goods,
        tax_on_sales: true,
        item_group: drinksGroupId,
        // _id: ItemSeeder.itemId.pepsi,
        average_cost_in_base_unit: 10,
        max_quantity_in_base_unit: 10,
        units: [
          {
            unit: unitData[2].id,
            is_default: true,
            barcode1: '',
            barcode2: '',
            sales_price: 1.2,
            wholesale_price: 1.0,
            special_price: 1.1,
            quantity: 100,
            avg_cost: 10,
          },
          {
            unit: unitData[4].id,
            is_default: false,
            barcode1: '',
            barcode2: '',
            sales_price: 14,
            wholesale_price: 12,
            special_price: 13,
            quantity: 100,
            avg_cost: 10,
          },
          {
            unit: unitData[5].id,
            is_default: false,
            barcode1: '',
            barcode2: '',
            sales_price: 100,
            wholesale_price: 80,
            special_price: 90,
            quantity: 100,
            avg_cost: 10,
          },
        ],
      },
      {
        //Dining Table Cover
        'name.ar': 'غطاء طاولة طعام',
        'name.en': 'Dining Table Cover',
        id: 5,
        code: '5',
        active: true,
        type: itemType.goods,
        tax_on_sales: true,
        item_group: spicesGroupId,
        // _id: ItemSeeder.itemId.cover,
        average_cost_in_base_unit: 10,
        max_quantity_in_base_unit: 10,
        units: [
          {
            unit: unitData[7].id,
            is_default: true,
            barcode1: '',
            barcode2: '',
            sales_price: 50,
            wholesale_price: 40,
            special_price: 45,
            quantity: 100,
            avg_cost: 10,
          },
          {
            unit: unitData[5].id,
            is_default: false,
            barcode1: '',
            barcode2: '',
            sales_price: 950,
            wholesale_price: 800,
            special_price: 850,
            quantity: 100,
            avg_cost: 10,
          },
        ],
      },
      {
        //Black Pepper
        'name.ar': 'فلفل أسود',
        'name.en': 'Black Pepper',
        id: 6,
        code: '6',
        active: true,
        type: itemType.goods,
        tax_on_sales: true,
        item_group: spicesGroupId,
        // _id: ItemSeeder.itemId.cepper,
        average_cost_in_base_unit: 10,
        max_quantity_in_base_unit: 10,
        units: [
          {
            unit: unitData[0].id,
            is_default: true,
            barcode1: '',
            barcode2: '',
            sales_price: 3.5,
            wholesale_price: 2.8,
            special_price: 3.2,
            quantity: 100,
            avg_cost: 10,
          },
          {
            unit: unitData[8].id,
            is_default: false,
            barcode1: '',
            barcode2: '',
            sales_price: 10.5,
            wholesale_price: 9,
            special_price: 9.8,
            quantity: 100,
            avg_cost: 10,
          },
          {
            unit: unitData[1].id,
            is_default: false,
            barcode1: '',
            barcode2: '',
            sales_price: 35,
            wholesale_price: 32,
            special_price: 33,
            quantity: 100,
            avg_cost: 10,
          },
        ],
      },
      {
        //Car Repair
        'name.ar': 'تصليح سيارة',
        'name.en': 'Car Repair',
        id: 7,
        code: '7',
        active: true,
        type: itemType.service,
        tax_on_sales: true,
        item_group: repairGroupId,
        // _id: ItemSeeder.itemId.car_repair,
        average_cost_in_base_unit: 10,
        max_quantity_in_base_unit: 10,
        units: [
          {
            unit: unitData[12].id,
            is_default: true,
            barcode1: '',
            barcode2: '',
            sales_price: 5,
            wholesale_price: 4,
            special_price: 4.5,
            quantity: 100,
            avg_cost: 10,
          },
          {
            unit: unitData[13].id,
            is_default: false,
            barcode1: '',
            barcode2: '',
            sales_price: 12,
            wholesale_price: 10,
            special_price: 10.5,
            quantity: 100,
            avg_cost: 10,
          },
        ],
      },
    ];
    for (const store of storesList) {
      for (const item of itemsData) {
        await this.itemModel.create({
          ...item,
          store: new Types.ObjectId(store._id),
        });
      }
    }

    return true;
  }
}
