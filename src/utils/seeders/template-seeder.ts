import mongoose from 'mongoose';
import { faker } from '@faker-js/faker';
import { templateTypeEnum } from '../../modules/template-design/dto/create-template-design.dto';
import { logoAlign } from '../../modules/template-design/schema/logo-settings.schema';
import {
  textAlign,
  titleWeight,
} from '../../modules/template-design/schema/design-options.schema';
import {
  TemplateDesign,
  templateDesignSchema,
} from '../../modules/template-design/schema/template-design.schema';
import { simplifiedTaxInvoiceTemplate } from './templates/simplified-tax-invoice';
import { taxInvoiceTemplate } from './templates/tax-invoice';
import { thermalInvoiceTemplate } from './templates/thermal-invoice';
import { salesReturnTemplate } from './templates/sales-return-template';

export class TemplateSeeder {
  templateModel: any;

  constructor(private db: any = null) {
    if (this.db !== null) {
      this.db = db;
      this.templateModel = this.db.model(
        TemplateDesign.name,
        templateDesignSchema,
      );
    }
  }

  public templatesArray: Array<any> = [
    {
      template: taxInvoiceTemplate,
      _id: new mongoose.Types.ObjectId('64acf899b16fbc375c964991'),
      type: templateTypeEnum.tax_invoice,
      control_options: {
        invoice_title: true,
        tax_number: true,
        payment_method: true,
        store_address: true,
        vat: true,
        footer: true,
        _id: new mongoose.Types.ObjectId('64acf899b16fbc375c964992'),
      },
      logo_settings: {
        align: faker.helpers.enumValue(logoAlign),
        logo_size: 0,
        _id: new mongoose.Types.ObjectId('64acf899b16fbc375c964993'),
      },
      design_options: {
        logo_in_background: true,
        title_weight: faker.helpers.enumValue(titleWeight),
        text_align: faker.helpers.enumValue(textAlign),
        _id: new mongoose.Types.ObjectId('64acf899b16fbc375c964994'),
      },
      send_options: {
        email: true,
        sms: true,
        _id: new mongoose.Types.ObjectId('64acf899b16fbc375c964995'),
      },
    },
    {
      template: simplifiedTaxInvoiceTemplate,
      _id: new mongoose.Types.ObjectId(),
      type: templateTypeEnum.simplified_tax_invoice,
      control_options: {
        invoice_title: true,
        tax_number: true,
        payment_method: true,
        store_address: true,
        vat: true,
        footer: true,
        _id: new mongoose.Types.ObjectId(),
      },
      logo_settings: {
        align: faker.helpers.enumValue(logoAlign),
        logo_size: 0,
        _id: new mongoose.Types.ObjectId(),
      },
      design_options: {
        logo_in_background: true,
        title_weight: faker.helpers.enumValue(titleWeight),
        text_align: faker.helpers.enumValue(textAlign),
        _id: new mongoose.Types.ObjectId(),
      },
      send_options: {
        email: true,
        sms: true,
        _id: new mongoose.Types.ObjectId(),
      },
    },
    {
      template: thermalInvoiceTemplate,
      _id: new mongoose.Types.ObjectId(),
      type: templateTypeEnum.thermal_invoice,
      control_options: {
        invoice_title: true,
        tax_number: true,
        payment_method: true,
        store_address: true,
        vat: true,
        footer: true,
        _id: new mongoose.Types.ObjectId(),
      },
      logo_settings: {
        align: faker.helpers.enumValue(logoAlign),
        logo_size: 0,
        _id: new mongoose.Types.ObjectId(),
      },
      design_options: {
        logo_in_background: true,
        title_weight: faker.helpers.enumValue(titleWeight),
        text_align: faker.helpers.enumValue(textAlign),
        _id: new mongoose.Types.ObjectId(),
      },
      send_options: {
        email: true,
        sms: true,
        _id: new mongoose.Types.ObjectId(),
      },
    },
    {
      template: salesReturnTemplate,
      _id: new mongoose.Types.ObjectId(),
      type: templateTypeEnum.sales_return,
      control_options: {
        invoice_title: true,
        tax_number: true,
        payment_method: true,
        store_address: true,
        vat: true,
        footer: true,
        _id: new mongoose.Types.ObjectId(),
      },
      logo_settings: {
        align: faker.helpers.enumValue(logoAlign),
        logo_size: 0,
        _id: new mongoose.Types.ObjectId(),
      },
      design_options: {
        logo_in_background: true,
        title_weight: faker.helpers.enumValue(titleWeight),
        text_align: faker.helpers.enumValue(textAlign),
        _id: new mongoose.Types.ObjectId(),
      },
      send_options: {
        email: true,
        sms: true,
        _id: new mongoose.Types.ObjectId(),
      },
    },
  ];

  async seed() {
    return await this.templateModel.create(this.templatesArray);
  }

  public getTemplates() {
    return {
      templates_array: this.templatesArray,
    };
  }
}
