export const thermalInvoiceTemplate = {
  en: `<!DOCTYPE html>
<!-- prettier-ignore -->
<html lang="en">

<head>
    <style>

@media print {
            @page {
                margin: 20px 0;
            }

            thead {
                display: table-header-group;
            }

            tfoot {
                display: table-footer-group;
            }

            body {
                margin: 0;
            }

            .header,
            .footer {
                position: fixed;
                width: 100%;
            }

            .header {
                top: 0;
            }

            .footer {
                bottom: 0;
            }

            .page-break {
                page-break-before: always;
            }
        }

        .main {
            font-size: 12px;
            color: #121212;
            background-color: #fff;
            font-family: Poppins;
            line-height: 16px;
            width: 100%;
            max-width: 100%;
            margin: 0 auto;
            font-family: 'Poppins', Arial, sans-serif;
        }

        .body {
            width: 100%;
            height: 100%;
        }

        p,
        h1,
        h2 {
            margin: 0;
        }

        h1 {
            font-size: 20px;
            font-weight: 600;
            line-height: 28px;
            text-transform: uppercase;
            margin-bottom: 8px;
        }

        h2 {
            font-size: 16px;
            font-weight: 600;
            line-height: 23px;
            text-transform: uppercase;
            margin-bottom: 8px;
        }

        .inline_container {
            display: flex;
            gap: 6px;
            margin-bottom: 4px
        }

        .block_container {
            display: flex;
            flex-direction: column;
        }

        .title {
            color: #67677A;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .text {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .line_height_18 {
            line-height: 18px !important;
        }

        .bold_400 {
            font-weight: 500 !important;
        }

        .bold_400 {
            font-weight: 500 !important;
        }

        .bold_500 {
            font-weight: 500 !important;
        }

        .bold_600 {
            font-weight: 600 !important;
        }

        .text-center {
            text-align: center !important;
        }

        .text-left {
            text-align: left !important;
        }

        .text-right {
            text-align: right !important;
        }

        .col {
            padding: 0 !important;
        }
        
        .col_4 {
            grid-column: span 4;
            width: 100%;
        }

        .col_2 {
            grid-column: span 8;
            width: 100%;
        }

        .flex {
            display: flex;
        }

        .logo {
            height: 32px;
            margin-bottom: 4px;
        }

        .tax_code {
            color: #67677A;
            font-size: 10px;
            font-weight: 500;
            line-height: 14px;
        }

        /* general info section */
        .general_info_container {
            color: #67677A;
            font-weight: 500;
            min-width: 100%;
            display: grid;
            grid-gap: 14px;
            justify-items: start;
            align-items: center;
            margin: 10px 0;
            padding: 0px 20px;
            border-bottom: solid 1px #E8E8ED;
            justify-content: center;
            font-family: 'Cairo', Arial, sans-serif;
        }

        .general_info_container h1{
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            color: #121212;
            line-height: 24px;
            text-transform: none;
            margin-bottom: 5px;
        }

        .general_info_container p {
            margin-right: 4px;
            font-size: 22px;
            font-style: normal;
            font-weight: 500;
            color: #121212;
            line-height: 20px;
        }

        .general_info_container span {
            font-size: 22px;
            font-weight: 500;
            color: #121212;
            line-height:20px;
        }

        .general_info_container > div{
            margin-bottom: 10px;
        }
        .general_info_container .multi_payment_wrapper {
            display: flex;
            gap: 8px;
            flex-wrap: nowrap;
        }

        .general_info_container .multi_payment_wrapper .payment_amount {
            color: #67677A;
            display: inline-block;
            margin: 0 1px;
        }
        .general_info_container .qr-container{
            width: 100%;
            margin: 10px 0px;
        }

        .bill-no {
            padding: 0;
        }
.qr-container{
     margin: 10px 0px;
}
        .bill-data{
            margin: 0px 20px 16px;
            font-family: 'Cairo', Arial, sans-serif;
            font-size: 22px;
            font-style: normal;
            font-weight: 500;
            line-height: 22px;
            color: #121212;
        }

        .time-container {
            display: flex;
            gap: 8px;
            align-items: center;
            justify-content: flex-end;
            line-height: 14px;
            padding: 0;
        }

        /* table section */
        .table_container {
            overflow: hidden;
            margin: 0 20px;
        }

        .table {
            border-collapse: collapse;
            margin: 0;
            width: 100%;
        }

        .table_cell,
        .head_cell {
            /* border: 1px solid #D7D7DE; */
            font-size: 10px;
            font-weight: 600;
            padding: 7px 24px;
        }

        .head_cell {
            text-align: center;
            background: #ffffff !important;
            height: 34px;
            border-left: 0px;
            border-right: 0px;
            border-top: 1px dashed #DFDFDF;
            border-bottom: 1px dashed #DFDFDF;
            padding: 0.5rem 0rem !important;
            font-size: 12px;
            font-weight: 600;
            line-height: 18px;
        }

        .table_cell {
            font-weight: 500;
            padding: 0.5rem 0rem !important;
            font-size: 12px;
            font-weight: 500;
            line-height: 18px;
            border-left: 0px;
            border-right: 0px;
            /* border-top: 1px solid #DFDFDF; */
            /* border-bottom: 1px solid #DFDFDF; */
        }
        .table > :not(caption) > * > *{
            border-bottom-width:none;
        }
        /* tbody tr:first-of-type .table_cell{
            border-top: 1px dashed #DFDFDF;
        } */
        /*  table summary section */
        .summary_container {
            display: block;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0px 0;
            margin: 0px 20px;
            border: 1px solid #E8E8ED;
            background: #ffffff;
            border-top: 0px;
            border-right: 0px;
            border-left: 0px;
            border-bottom: 0px;
        }

        .summary_item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0px;
            margin-bottom: 8px;
             font-family: 'Cairo', Arial, sans-serif;
        }
        .summary_item p{
            font-size: 12px;
            font-weight: 500;
            color: #121221;
            line-height: 22px;
            margin-bottom: 8px;
        }
        .summary_item span{
            font-size: 12px;
            font-weight: 600;
            color: #121221;
            line-height: 22px;
        }
        .summary_item.total-col {
            border-top: 1px dashed #DFDFDF;
        }
        .summary_item.total-col.border-bottom{
            border-bottom: 1px dashed #DFDFDF;
        }
        .summary_item.total-col span {
            padding: 8px 0px;
            margin: 0;
            display: inline-block;
            font-size: 22px;
            font-style: normal;
            font-weight: 500;
        }

        .qr_code {
            /* margin: 0 24px; */
            width: 80px;
            height: 80px;
        }

        /* Payment types */

        .payments {
            display: flex;
            width: 100%;
            flex-direction: column;
            gap: 8px;
             border-bottom: 1px dashed #DFDFDF;
             padding: 16px 0px;
        }

        .payment_wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0 20px;
            font-size: 20px;
        }

        /* footer section */
        .thanks-text{
            text-align: center;
            font-family:'Cairo', Arial, sans-serif;
            font-size: 20px;
            font-style: normal;
            font-weight: 700;
            line-height: 18px; 
            padding: 10px 0;
            color:  #121221;
        }
        .invoice-title{
            display: grid;
            justify-content: center;
            font-family: 'Cairo', Arial, sans-serif;
            padding-bottom: 8px;
            margin-bottom: 8px;
            border-bottom: solid 1px #E8E8ED;
        }
        .invoice-title p{
            justify-content: center;
            font-size: 20px;
            font-style: normal;
            font-weight: 500;
            display: flex;
            margin-bottom: 5px;
        }
        .mb-1{
            margin-bottom: 5px;
        }
        .rtl{
            direction: rtl;
        }
        .double_cell{
            /* display: grid; */
            font-family: 'Cairo', Arial, sans-serif;
            text-align: left;
        }
        .double_cell .bold{
            font-size: 22px;
            font-style: normal;
            font-weight: 700;
            display: block;
            margin-bottom: 5px;
        }
        .double_cell .light{
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            display: block;
        }
        .summary_item.total-col .sub-total{
            font-size: 25px;
            font-weight: 700;
        }
        .summary_item.total-col .total{
            font-size: 35px;
            font-weight: 700;
        }
    </style>
</head>

<body>
    <table class="main">
        <tbody>
            <tr>
                <td>
                    <div class="body">
                        <!-- general info section -->
                        <div class="general_info_container">
                            <div class="">
                                 <% if(data?.company_info?.logo){ %>
                                    <div class="flex justify-content-center">
                                        <img src=<%=data?.company_info?.logo %> alt="company logo" class="logo" />
                                    </div>
                                <% } %>
                                <div class="flex justify-content-center mb-1"><p> Address:</p>
                                    <span> <%- data.company_info?.address  %></span>
                                </div>

                                     <div class="flex justify-content-center mb-1"> <p>VAT :</p>
                                <span>
                                    <%- data?.company_info?.tax_code %>
                                </span>
                            </div>
                                    <div class="flex justify-content-center mb-1">
                                <p class="flex">Phone: </p>
                                <span> <%- data?.company_info?.phone  %></span></div>
                            </div>
                           
                           
                        </div>
                        <div class="invoice-title">
                            <p>فاتورة ضريبية مبسطة</p>
                            <p>Simplified tax invoice</p>
                        </div>
                        <div class="flex row bill-data">
                            <div class="col flex">
                                <p>Invoice No. :  </p></div>
                                <div class="col flex justify-content-center">
                                <span>
                                    <%= data?.invoice_no %>
                                </span>
                                </div>
                                <div class="col flex justify-content-start rtl">
                                <p>رقم الفاتورة : </p></div>
                            </div>
                        </div>
<div class="flex row bill-data">
                            <div class="col flex">
                                <p>Time :   </p>
                            </div>
                                <div class="col flex justify-content-center">
                                <span><%= formatTime(data?.invoice_time) %></span>
                                </div>
                                <div class="col flex justify-content-start rtl">
                                <p>الوقت :</p></div>
                            </div>
                        </div>
                        <div class="flex row bill-data">
                            <div class="col flex">
                                <p>Date :   </p>
                            </div>
                                <div class="col flex justify-content-center">
                                <span><%= formatTime(data?.invoice_date) %></span>
                                </div>
                                <div class="col flex justify-content-start rtl">
                                <p>التاريخ :</p></div>
                            </div>
                        </div>
                        <!-- table section -->
                        <div class="table_container">
                            <table class="table">
                                <thead>
                                    <tr>
                                      <th class="head_cell double_cell" style="width: 60px;"><span class="bold">Qty</span><span class="light">كمية</span></th>
                                        <th class="head_cell double_cell" style="width: 300px;"><span class="bold">Item Description</span><span class="light">وصف المنتج</span></th>
                                        <th class="head_cell double_cell text-right" style="width: 50px;"><svg xmlns="http://www.w3.org/2000/svg" width="30" height="32" viewBox="0 0 30 32" fill="none">
<path d="M28.5713 26.0869C28.3825 27.614 28.3 28.2761 27.5947 29.7646L16.7666 32C17.0155 30.3912 17.3474 29.1505 17.8867 28.4062L28.5713 26.0869ZM13.4902 15.5566L16.7256 14.8545V4.63086C17.931 3.27776 18.672 2.67038 20.127 1.90234V14.1162L28.5713 12.2832C28.3825 13.8102 28.3 14.4724 27.5947 15.9609L20.127 17.5391V20.9727L28.5713 19.1855C28.3825 20.7126 28.3 21.3747 27.5947 22.8633L20.127 24.4043V24.4375L16.7256 25.1396V18.2588L13.4902 18.9424V23.2793L13.4355 23.29C12.6914 24.595 11.6396 26.1624 10.627 27.4141L0 29.4375C0.0952884 28.0701 0.294753 27.3002 0.913086 25.9229L10.0879 23.9336V19.6611L1.58398 21.46C1.67927 20.0926 1.87782 19.3225 2.49609 17.9453L10.0879 16.2959V2.72949C11.2935 1.37619 12.035 0.768139 13.4902 0V15.5566Z" fill="#121221"/>
</svg></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% for (let item of data?.transactions){ %>
                                        <tr>
                                             <td class="table_cell text-left">
                                                <%= item?.qty %> X
                                            </td>
                                            <td class="table_cell">
                                                <%= item?.item_invoice_name %>
                                            </td>
                                            <td class="table_cell text-right">
                                                <%= Number(item?.price || 0)?.toFixed(2) %>
                                            </td>
                                        </tr>
                                    <% } %>
                                </tbody>
                            </table>
                        </div>
                        <!-- table summary section  -->
                        <div class="summary_container row">
                            <div class="summary_item total-col col">
                               <div> <span >Total Discount &nbsp; </span>
                                <span>الخصم : </span>  </div>
                                <span>
                                    <%= Number(data?.total_discount || 0)?.toFixed(2) %>
                                </span>
                            </div>
                            <div class="summary_item total-col col">
                                <div><span class="sub-total">Sub Total &nbsp;</span><span> المجموع الفرعى:</span></div>
                                <span class="sub-total">
                                    <%= Number(data?.total_subtotal || 0)?.toFixed(2) %>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="24" viewBox="0 0 22 24" fill="none">
<path d="M19.8564 18.3047C19.7384 19.259 19.6858 19.6723 19.2451 20.6025L12.4785 22C12.6341 20.9945 12.8417 20.2191 13.1787 19.7539L19.8564 18.3047ZM10.4307 11.7227L12.4531 11.2842V4.89453C13.2065 4.04877 13.6697 3.66855 14.5791 3.18848V10.8223L19.8564 9.67676C19.7384 10.6312 19.6859 11.0452 19.2451 11.9756L14.5791 12.9619V15.1074L19.8564 13.9912C19.7384 14.9456 19.6858 15.3588 19.2451 16.2891L14.5791 17.252V17.2734L12.4531 17.7129V13.4111L10.4307 13.8379V16.5498L10.3955 16.5566C9.93045 17.3721 9.27429 18.3518 8.6416 19.1338L2 20.3984C2.05954 19.5439 2.184 19.0628 2.57031 18.2021L8.30469 16.958V14.2881L2.99023 15.4121C3.0498 14.5577 3.17417 14.0765 3.56055 13.2158L8.30469 12.1846V3.70605C9.05813 2.86024 9.52121 2.48009 10.4307 2V11.7227Z" fill="#121221"/>
</svg>
                                </span>
                            </div>
                            <div class="summary_item total-col col">
                              <div><span >VAT &nbsp;</span><span> الضريبة:</span></div>
                                <span>
                                    <%= Number(data?.total_vat || 0)?.toFixed(2) %>
                                </span>
                            </div>
                            <div class="summary_item total-col border-bottom col">
                              <div><span class="total" >Total &nbsp;</span><span > الإجمالى:</span></div>
                                <span class="total">
                                    <%= Number(data?.invoice_total || 0)?.toFixed(2) %>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="29" height="32" viewBox="0 0 29 32" fill="none">
<path d="M28.5723 26.0869C28.3834 27.614 28.3 28.2761 27.5947 29.7646L16.7676 32C17.0165 30.3913 17.3484 29.1505 17.8877 28.4062L28.5723 26.0869ZM13.4902 15.5576L16.7256 14.8555V4.63086C17.9312 3.27756 18.6727 2.67048 20.1279 1.90234V14.1162L28.5723 12.2832C28.3834 13.8102 28.3 14.4724 27.5947 15.9609L20.1279 17.5391V20.9727L28.5723 19.1855C28.3834 20.7126 28.3 21.3747 27.5947 22.8633L20.1279 24.4043V24.4375L16.7256 25.1396V18.2588L13.4902 18.9424V23.2793L13.4355 23.29C12.6914 24.5949 11.6405 26.1625 10.6279 27.4141L0 29.4375C0.0952905 28.0701 0.29474 27.3002 0.913086 25.9229L10.0879 23.9336V19.6611L1.58398 21.46C1.67927 20.0926 1.87781 19.3225 2.49609 17.9453L10.0879 16.2959V2.72949C11.2935 1.37619 12.035 0.768139 13.4902 0V15.5576Z" fill="#121221"/>
</svg>
                                </span>
                            </div>
                        </div>
                        <!-- Payment types -->
                        <% if(data?.payment_types){ %>
                            <div class="payments">
                                    <% for (let type of data?.payment_types){ %>
                                        <div class="payment_wrapper">
                                            <span class="bold_400 line_height_18">
                                                <%= type?.name %>
                                            </span>
                                            <span class="bold_600 line_height_18">
                                                <%=  Number(type?.amount || 0)?.toFixed(2) %>
                                            </span>
                                        </div>
                                    <% } %>
                            </div>
                       <% } %>
                        <div class="block qr-container">
                                <% if(data?.qr_code){ %>
                                    <div class="text-center"><img src=<%=data.qr_code %> alt="QR Code" class="qr_code"></div>
                                <% } %>
                               
                            </div>
                        <p class="thanks-text">THANK YOU FOR YOUR VISIT</p>
                         <p class="thanks-text">شكرا لزيارتكم </p>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</body>

</html>

<% function formatTime(date = new Date()) { const convertToDate = new Date(date); const hours =
convertToDate.getHours(); const minutes = String(convertToDate.getMinutes()).padStart(2, '0'); const
seconds = String(convertToDate.getSeconds()).padStart(2, '0'); const ampm = hours >= 12 ? 'PM' :
'AM'; const formattedHours = String(hours % 12 || 12).padStart(2, '0'); return
(\`\${formattedHours}:\${minutes}:\${seconds} \${ampm}\`); } %> <% function formatDate(date = new Date())
{ const convertToDate = new Date(date); const day = convertToDate.getDate().toString().padStart(2,
'0'); const month = (convertToDate.getMonth() + 1).toString().padStart(2, '0'); const year =
convertToDate.getFullYear(); return \`\${day}/\${month}/\${year}\`; } %>`,
  ar: `<!DOCTYPE html>
<!-- prettier-ignore -->
<html lang="en">

<head>
    <style>

@media print {
            @page {
                margin: 20px 0;
            }

            thead {
                display: table-header-group;
            }

            tfoot {
                display: table-footer-group;
            }

            body {
                margin: 0;
            }

            .header,
            .footer {
                position: fixed;
                width: 100%;
            }

            .header {
                top: 0;
            }

            .footer {
                bottom: 0;
            }

            .page-break {
                page-break-before: always;
            }
        }

        .main {
            font-size: 12px;
            color: #121212;
            background-color: #fff;
            font-family: Poppins;
            line-height: 16px;
            width: 100%;
            max-width: 100%;
            margin: 0 auto;
            font-family: 'Poppins', Arial, sans-serif;
        }

        .body {
            width: 100%;
            height: 100%;
        }

        p,
        h1,
        h2 {
            margin: 0;
        }

        h1 {
            font-size: 20px;
            font-weight: 600;
            line-height: 28px;
            text-transform: uppercase;
            margin-bottom: 8px;
        }

        h2 {
            font-size: 16px;
            font-weight: 600;
            line-height: 23px;
            text-transform: uppercase;
            margin-bottom: 8px;
        }

        .inline_container {
            display: flex;
            gap: 6px;
            margin-bottom: 4px
        }

        .block_container {
            display: flex;
            flex-direction: column;
        }

        .title {
            color: #67677A;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .text {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .line_height_18 {
            line-height: 18px !important;
        }

        .bold_400 {
            font-weight: 500 !important;
        }

        .bold_400 {
            font-weight: 500 !important;
        }

        .bold_500 {
            font-weight: 500 !important;
        }

        .bold_600 {
            font-weight: 600 !important;
        }

        .text-center {
            text-align: center !important;
        }

        .text-left {
            text-align: left !important;
        }

        .text-right {
            text-align: right !important;
        }

        .col {
            padding: 0 !important;
        }
        
        .col_4 {
            grid-column: span 4;
            width: 100%;
        }

        .col_2 {
            grid-column: span 8;
            width: 100%;
        }

        .flex {
            display: flex;
        }

        .logo {
            height: 32px;
            margin-bottom: 4px;
        }

        .tax_code {
            color: #67677A;
            font-size: 10px;
            font-weight: 500;
            line-height: 14px;
        }

        /* general info section */
        .general_info_container {
            color: #67677A;
            font-weight: 500;
            min-width: 100%;
            display: grid;
            grid-gap: 14px;
            justify-items: start;
            align-items: center;
            margin: 10px 0;
            padding: 0px 20px;
            border-bottom: solid 1px #E8E8ED;
            justify-content: center;
            font-family: 'Cairo', Arial, sans-serif;
        }

        .general_info_container h1{
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            color: #121212;
            line-height: 24px;
            text-transform: none;
            margin-bottom: 5px;
        }

        .general_info_container p {
            margin-right: 4px;
            font-size: 22px;
            font-style: normal;
            font-weight: 500;
            color: #121212;
            line-height: 20px;
        }

        .general_info_container span {
            font-size: 22px;
            font-weight: 500;
            color: #121212;
            line-height:20px;
        }

        .general_info_container > div{
            margin-bottom: 10px;
        }
        .general_info_container .multi_payment_wrapper {
            display: flex;
            gap: 8px;
            flex-wrap: nowrap;
        }

        .general_info_container .multi_payment_wrapper .payment_amount {
            color: #67677A;
            display: inline-block;
            margin: 0 1px;
        }
        .general_info_container .qr-container{
            width: 100%;
            margin: 10px 0px;
        }

        .bill-no {
            padding: 0;
        }
.qr-container{
     margin: 10px 0px;
}
        .bill-data{
            margin: 0px 20px 16px;
            font-family: 'Cairo', Arial, sans-serif;
            font-size: 22px;
            font-style: normal;
            font-weight: 500;
            line-height: 22px;
            color: #121212;
        }

        .time-container {
            display: flex;
            gap: 8px;
            align-items: center;
            justify-content: flex-end;
            line-height: 14px;
            padding: 0;
        }

        /* table section */
        .table_container {
            overflow: hidden;
            margin: 0 20px;
        }

        .table {
            border-collapse: collapse;
            margin: 0;
            width: 100%;
        }

        .table_cell,
        .head_cell {
            /* border: 1px solid #D7D7DE; */
            font-size: 10px;
            font-weight: 600;
            padding: 7px 24px;
        }

        .head_cell {
            text-align: center;
            background: #ffffff !important;
            height: 34px;
            border-left: 0px;
            border-right: 0px;
            border-top: 1px dashed #DFDFDF;
            border-bottom: 1px dashed #DFDFDF;
            padding: 0.5rem 0rem !important;
            font-size: 12px;
            font-weight: 600;
            line-height: 18px;
        }

        .table_cell {
            font-weight: 500;
            padding: 0.5rem 0rem !important;
            font-size: 12px;
            font-weight: 500;
            line-height: 18px;
            border-left: 0px;
            border-right: 0px;
            /* border-top: 1px solid #DFDFDF; */
            /* border-bottom: 1px solid #DFDFDF; */
        }
        .table > :not(caption) > * > *{
            border-bottom-width:none;
        }
        /* tbody tr:first-of-type .table_cell{
            border-top: 1px dashed #DFDFDF;
        } */
        /*  table summary section */
        .summary_container {
            display: block;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0px 0;
            margin: 0px 20px;
            border: 1px solid #E8E8ED;
            background: #ffffff;
            border-top: 0px;
            border-right: 0px;
            border-left: 0px;
            border-bottom: 0px;
        }

        .summary_item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0px;
            margin-bottom: 8px;
             font-family: 'Cairo', Arial, sans-serif;
        }
        .summary_item p{
            font-size: 12px;
            font-weight: 500;
            color: #121221;
            line-height: 22px;
            margin-bottom: 8px;
        }
        .summary_item span{
            font-size: 12px;
            font-weight: 600;
            color: #121221;
            line-height: 22px;
        }
        .summary_item.total-col {
            border-top: 1px dashed #DFDFDF;
        }
        .summary_item.total-col.border-bottom{
            border-bottom: 1px dashed #DFDFDF;
        }
        .summary_item.total-col span {
            padding: 8px 0px;
            margin: 0;
            display: inline-block;
            font-size: 22px;
            font-style: normal;
            font-weight: 500;
        }

        .qr_code {
            /* margin: 0 24px; */
            width: 80px;
            height: 80px;
        }

        /* Payment types */

        .payments {
            display: flex;
            width: 100%;
            flex-direction: column;
            gap: 8px;
             border-bottom: 1px dashed #DFDFDF;
             padding: 16px 0px;
        }

        .payment_wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0 20px;
            font-size: 20px;
        }

        /* footer section */
        .thanks-text{
            text-align: center;
            font-family:'Cairo', Arial, sans-serif;
            font-size: 20px;
            font-style: normal;
            font-weight: 700;
            line-height: 18px; 
            padding: 10px 0;
            color:  #121221;
        }
        .invoice-title{
            display: grid;
            justify-content: center;
            font-family: 'Cairo', Arial, sans-serif;
            padding-bottom: 8px;
            margin-bottom: 8px;
            border-bottom: solid 1px #E8E8ED;
        }
        .invoice-title p{
            justify-content: center;
            font-size: 20px;
            font-style: normal;
            font-weight: 500;
            display: flex;
            margin-bottom: 5px;
        }
        .mb-1{
            margin-bottom: 5px;
        }
        .rtl{
            direction: rtl;
        }
        .double_cell{
            /* display: grid; */
            font-family: 'Cairo', Arial, sans-serif;
            text-align: left;
        }
        .double_cell .bold{
            font-size: 22px;
            font-style: normal;
            font-weight: 700;
            display: block;
            margin-bottom: 5px;
        }
        .double_cell .light{
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            display: block;
        }
        .summary_item.total-col .sub-total{
            font-size: 25px;
            font-weight: 700;
        }
        .summary_item.total-col .total{
            font-size: 35px;
            font-weight: 700;
        }
    </style>
</head>

<body>
    <table class="main">
        <tbody>
            <tr>
                <td>
                    <div class="body">
                        <!-- general info section -->
                        <div class="general_info_container">
                            <div class="">
                                 <% if(data?.company_info?.logo){ %>
                                    <div class="flex justify-content-center">
                                        <img src=<%=data?.company_info?.logo %> alt="company logo" class="logo" />
                                    </div>
                                <% } %>
                                <div class="flex justify-content-center mb-1"><p> Address:</p>
                                    <span> <%- data.company_info?.address  %></span>
                                </div>

                                     <div class="flex justify-content-center mb-1"> <p>VAT :</p>
                                <span>
                                    <%- data?.company_info?.tax_code %>
                                </span>
                            </div>
                                    <div class="flex justify-content-center mb-1">
                                <p class="flex">Phone: </p>
                                <span> <%- data?.company_info?.phone  %></span></div>
                            </div>
                           
                           
                        </div>
                        <div class="invoice-title">
                            <p>فاتورة ضريبية مبسطة</p>
                            <p>Simplified tax invoice</p>
                        </div>
                        <div class="flex row bill-data">
                            <div class="col flex">
                                <p>Invoice No. :  </p></div>
                                <div class="col flex justify-content-center">
                                <span>
                                    <%= data?.invoice_no %>
                                </span>
                                </div>
                                <div class="col flex justify-content-start rtl">
                                <p>رقم الفاتورة : </p></div>
                            </div>
                        </div>
<div class="flex row bill-data">
                            <div class="col flex">
                                <p>Time :   </p>
                            </div>
                                <div class="col flex justify-content-center">
                                <span><%= formatTime(data?.invoice_time) %></span>
                                </div>
                                <div class="col flex justify-content-start rtl">
                                <p>الوقت :</p></div>
                            </div>
                        </div>
                        <div class="flex row bill-data">
                            <div class="col flex">
                                <p>Date :   </p>
                            </div>
                                <div class="col flex justify-content-center">
                                <span><%= formatTime(data?.invoice_date) %></span>
                                </div>
                                <div class="col flex justify-content-start rtl">
                                <p>التاريخ :</p></div>
                            </div>
                        </div>
                        <!-- table section -->
                        <div class="table_container">
                            <table class="table">
                                <thead>
                                    <tr>
                                      <th class="head_cell double_cell" style="width: 60px;"><span class="bold">Qty</span><span class="light">كمية</span></th>
                                        <th class="head_cell double_cell" style="width: 300px;"><span class="bold">Item Description</span><span class="light">وصف المنتج</span></th>
                                        <th class="head_cell double_cell text-right" style="width: 50px;"><svg xmlns="http://www.w3.org/2000/svg" width="30" height="32" viewBox="0 0 30 32" fill="none">
<path d="M28.5713 26.0869C28.3825 27.614 28.3 28.2761 27.5947 29.7646L16.7666 32C17.0155 30.3912 17.3474 29.1505 17.8867 28.4062L28.5713 26.0869ZM13.4902 15.5566L16.7256 14.8545V4.63086C17.931 3.27776 18.672 2.67038 20.127 1.90234V14.1162L28.5713 12.2832C28.3825 13.8102 28.3 14.4724 27.5947 15.9609L20.127 17.5391V20.9727L28.5713 19.1855C28.3825 20.7126 28.3 21.3747 27.5947 22.8633L20.127 24.4043V24.4375L16.7256 25.1396V18.2588L13.4902 18.9424V23.2793L13.4355 23.29C12.6914 24.595 11.6396 26.1624 10.627 27.4141L0 29.4375C0.0952884 28.0701 0.294753 27.3002 0.913086 25.9229L10.0879 23.9336V19.6611L1.58398 21.46C1.67927 20.0926 1.87782 19.3225 2.49609 17.9453L10.0879 16.2959V2.72949C11.2935 1.37619 12.035 0.768139 13.4902 0V15.5566Z" fill="#121221"/>
</svg></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% for (let item of data?.transactions){ %>
                                        <tr>
                                             <td class="table_cell text-left">
                                                <%= item?.qty %> X
                                            </td>
                                            <td class="table_cell">
                                                <%= item?.item_invoice_name %>
                                            </td>
                                            <td class="table_cell text-right">
                                                <%= Number(item?.price || 0)?.toFixed(2) %>
                                            </td>
                                        </tr>
                                    <% } %>
                                </tbody>
                            </table>
                        </div>
                        <!-- table summary section  -->
                        <div class="summary_container row">
                            <div class="summary_item total-col col">
                               <div> <span >Total Discount &nbsp; </span>
                                <span>الخصم : </span>  </div>
                                <span>
                                    <%= Number(data?.total_discount || 0)?.toFixed(2) %>
                                </span>
                            </div>
                            <div class="summary_item total-col col">
                                <div><span class="sub-total">Sub Total &nbsp;</span><span> المجموع الفرعى:</span></div>
                                <span class="sub-total">
                                    <%= Number(data?.total_subtotal || 0)?.toFixed(2) %>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="24" viewBox="0 0 22 24" fill="none">
<path d="M19.8564 18.3047C19.7384 19.259 19.6858 19.6723 19.2451 20.6025L12.4785 22C12.6341 20.9945 12.8417 20.2191 13.1787 19.7539L19.8564 18.3047ZM10.4307 11.7227L12.4531 11.2842V4.89453C13.2065 4.04877 13.6697 3.66855 14.5791 3.18848V10.8223L19.8564 9.67676C19.7384 10.6312 19.6859 11.0452 19.2451 11.9756L14.5791 12.9619V15.1074L19.8564 13.9912C19.7384 14.9456 19.6858 15.3588 19.2451 16.2891L14.5791 17.252V17.2734L12.4531 17.7129V13.4111L10.4307 13.8379V16.5498L10.3955 16.5566C9.93045 17.3721 9.27429 18.3518 8.6416 19.1338L2 20.3984C2.05954 19.5439 2.184 19.0628 2.57031 18.2021L8.30469 16.958V14.2881L2.99023 15.4121C3.0498 14.5577 3.17417 14.0765 3.56055 13.2158L8.30469 12.1846V3.70605C9.05813 2.86024 9.52121 2.48009 10.4307 2V11.7227Z" fill="#121221"/>
</svg>
                                </span>
                            </div>
                            <div class="summary_item total-col col">
                              <div><span >VAT &nbsp;</span><span> الضريبة:</span></div>
                                <span>
                                    <%= Number(data?.total_vat || 0)?.toFixed(2) %>
                                </span>
                            </div>
                            <div class="summary_item total-col border-bottom col">
                              <div><span class="total" >Total &nbsp;</span><span > الإجمالى:</span></div>
                                <span class="total">
                                    <%= Number(data?.invoice_total || 0)?.toFixed(2) %>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="29" height="32" viewBox="0 0 29 32" fill="none">
<path d="M28.5723 26.0869C28.3834 27.614 28.3 28.2761 27.5947 29.7646L16.7676 32C17.0165 30.3913 17.3484 29.1505 17.8877 28.4062L28.5723 26.0869ZM13.4902 15.5576L16.7256 14.8555V4.63086C17.9312 3.27756 18.6727 2.67048 20.1279 1.90234V14.1162L28.5723 12.2832C28.3834 13.8102 28.3 14.4724 27.5947 15.9609L20.1279 17.5391V20.9727L28.5723 19.1855C28.3834 20.7126 28.3 21.3747 27.5947 22.8633L20.1279 24.4043V24.4375L16.7256 25.1396V18.2588L13.4902 18.9424V23.2793L13.4355 23.29C12.6914 24.5949 11.6405 26.1625 10.6279 27.4141L0 29.4375C0.0952905 28.0701 0.29474 27.3002 0.913086 25.9229L10.0879 23.9336V19.6611L1.58398 21.46C1.67927 20.0926 1.87781 19.3225 2.49609 17.9453L10.0879 16.2959V2.72949C11.2935 1.37619 12.035 0.768139 13.4902 0V15.5576Z" fill="#121221"/>
</svg>
                                </span>
                            </div>
                        </div>
                        <!-- Payment types -->
                        <% if(data?.payment_types){ %>
                            <div class="payments">
                                    <% for (let type of data?.payment_types){ %>
                                        <div class="payment_wrapper">
                                            <span class="bold_400 line_height_18">
                                                <%= type?.name %>
                                            </span>
                                            <span class="bold_600 line_height_18">
                                                <%=  Number(type?.amount || 0)?.toFixed(2) %>
                                            </span>
                                        </div>
                                    <% } %>
                            </div>
                       <% } %>
                        <div class="block qr-container">
                                <% if(data?.qr_code){ %>
                                    <div class="text-center"><img src=<%=data.qr_code %> alt="QR Code" class="qr_code"></div>
                                <% } %>
                               
                            </div>
                        <p class="thanks-text">THANK YOU FOR YOUR VISIT</p>
                         <p class="thanks-text">شكرا لزيارتكم </p>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</body>

</html>

<% function formatTime(date = new Date()) { const convertToDate = new Date(date); const hours =
convertToDate.getHours(); const minutes = String(convertToDate.getMinutes()).padStart(2, '0'); const
seconds = String(convertToDate.getSeconds()).padStart(2, '0'); const ampm = hours >= 12 ? 'PM' :
'AM'; const formattedHours = String(hours % 12 || 12).padStart(2, '0'); return
(\`\${formattedHours}:\${minutes}:\${seconds} \${ampm}\`); } %> <% function formatDate(date = new Date())
{ const convertToDate = new Date(date); const day = convertToDate.getDate().toString().padStart(2,
'0'); const month = (convertToDate.getMonth() + 1).toString().padStart(2, '0'); const year =
convertToDate.getFullYear(); return \`\${day}/\${month}/\${year}\`; } %>`,
};
