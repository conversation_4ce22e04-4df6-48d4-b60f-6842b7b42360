export const taxInvoiceTemplate = {
  en: `<!-- documentTemplate.ejs -->
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice #<%= data?.invoice_no %></title>
    <style>
        @media print {
            @page {
                size: A4;
                margin: 0;
            }

            thead {
                display: table-header-group;
            }

            tfoot {
                display: table-footer-group;
            }

            body {
                margin: 0;
            }

            .header,
            .footer {
                position: fixed;
                width: 100%;
            }

            .header {
                top: 0;
            }

            .footer {
                bottom: 0;
            }

            .page-break {
                page-break-before: always;
            }
        }

        .main {
            font-size: 12px;
            color: #121212;
            background-color: #fff;
            font-family: 'Poppins', Arial, sans-serif;
            line-height: 16px;
            width: 100%;
            min-width: 100%;
            max-width: 100%;
            height: 100%;
            min-height: 100%;
            max-height: 100%;
            margin: 0;
            border-collapse: collapse;
        }

        .body {
            width: 100%;
            height: 100%;
        }

        p,
        h1,
        h2 {
            margin: 0;
        }

        h1 {
            font-size: 20px;
            font-weight: 600;
            line-height: 28px;
            text-transform: uppercase;
            margin-bottom: 2px;
        }

        h2 {
            font-size: 16px;
            font-weight: 600;
            line-height: 23px;
            text-transform: uppercase;
            margin-bottom: 8px;
            white-space: normal;
            word-break: normal;
            overflow-wrap: normal;
        }

        .inline_container {
            display: flex;
            gap: 6px;
            margin-bottom: 4px
        }

        .block_container {
            display: flex;
            flex-direction: column;
        }

        .title {
            color: #67677A;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .text {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .font_10 {
            font-size: 10px !important;
        }

        .bold_500 {
            font-weight: 500 !important;
        }

        .bold_600 {
            font-weight: 600 !important;
        }

        .text-center {
            text-align: center !important;
        }

        .text-left {
            text-align: left !important;
        }

        .text-right {
            text-align: right !important;
        }

        .col_4 {
            grid-column: span 4;
            width: 100%;
        }

        .col_2 {
            grid-column: span 8;
            width: 100%;
        }

        .flex {
            display: flex;
        }

        /* Header */
        .header_wrapper {
            height: 175px;
            width: 100%;
            margin-bottom: 24px;
        }

        .header {
            padding: 16px 32px !important;
            width: 100%;
            display: flex;
            gap: 12px;
            justify-content: space-between;
            align-items: center;
            border-bottom: 0.5px solid #EBEDF2;
            background: #F9FAFC;
            height: 175px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .header_column {
            max-width: 40%;
            position: relative;
        }

        .header_border {
            border-right: 12px #9112FA solid !important;
            border-radius: 20px;
            position: absolute;
            height: 105%;
            left: -40px;
            top: -3px;
        }

        .logo {
            height: 34px;
            margin-bottom: 4px;
        }

        .vat {
            color: #67677A;
            font-size: 12px;
            font-weight: 500;
            line-height: 18px;
        }

        .tax_code {
            color: #121221;
            font-size: 12px;
            font-weight: 500;
            line-height: 18px;
        }

        /* general info section */
        .general_info_container {
            color: #67677A;
            font-weight: 500;
            min-width: 100%;
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            grid-gap: 14px;
            justify-items: center;
            align-items: center;
            margin: 0 24px 16px;
        }

        .general_info_container p {
            margin: 0 4px;
        }

        .general_info_container span {
            font-weight: 600;
            color: #121212;
        }

        .general_info_container .multi_payment_wrapper {
            display: flex;
            gap: 8px;
            flex-wrap: nowrap;
        }

        .general_info_container .multi_payment_wrapper .payment_amount {
            color: #67677A;
            display: inline-block;
            margin: 0 1px;
        }

        /* table section */
        .table_container {
            overflow: hidden;
            margin: 0 24px;
        }

        .table {
            border-collapse: collapse;
            margin: 0;
            width: 100%;
        }

        .table_cell,
        .head_cell {
            border: 1px solid #D7D7DE;
            font-size: 10px;
            font-weight: 500;
            padding: 7px 24px;
        }

        .head_cell {
            text-align: center;
            background: #F9FAFC !important;
            height: 34px;
        }

        .table_cell {
            font-size: 12px;
            height: 32px;
        }

        /*  table summary section */
        .summary_container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 16px;
            margin: 16px 24px;
            border: 1px solid #E8E8ED;
            background: #F9FAFC;
            border-radius: 5px;
        }

        .summary_item {
            display: flex;
            flex-direction: column;
            min-width: 13%;
        }

        .qr_code {
            margin: 0 24px;
            width: 72px;
            height: 72px;
        }

        /* footer section */
        .footer_wrapper {
            height: 50px;
            width: 100%;
            margin-top: 24px;
        }

        .footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 45px;
            color: white;
            width: 100%;
            overflow: hidden;
            height: 50px;
            background-color: #9112FA;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .footer_item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .footer_item span {
            font-size: 10px;
        }

        /* Prevent content from going behind the header and footer */
        .content {
            padding: 0;
        }
    </style>
</head>

<body>
    <table class="main">
        <thead>
            <tr>
                <td>
                    <!-- Header -->
                    <div class="header_wrapper">
                        <div class="header">
                            <div class="header_column">
                                <div class="header_border"></div>
                                <div>
                                    <h1>Sales INVOICE</h1>
                                    <div class="inline_container">
                                        <p class="title">Invoice No</p>
                                        <p class="text">#<%= data?.invoice_no %></p>
                                    </div>
                                    <% if(data?.invoice_type !=="cash" ){ %>
                                        <div class="inline_container">
                                            <p class="title">Date</p>
                                            <p class="text"><%= formatDate(data?.invoice_date) %></p>
                                        </div>
                                    <% } %>
                                    <div class="inline_container">
                                        <p class="title">Customer</p>
                                        <p class="text"><%= data.customer?.name %></p>
                                    </div>
                                    <div class="inline_container">
                                        <p class="title">Customer Phone</p>
                                        <p class="text"><%- data?.customer?.phone? data?.customer?.phone: "-" %></p>
                                    </div>
                                    <% if(data?.invoice_type==="cash" ){ %>
                                        <div class="inline_container">
                                            <p class="title">Customer VAT</p>
                                            <p class="text"><%- data.customer?.tax_code? data.customer?.tax_code: "-" %></p>
                                        </div>
                                    <% } %>
                                </div>
                            </div>
                            <div class="header_column">
                                <% if(data?.company_info?.logo){ %>
                                    <div>
                                        <img src=<%=data?.company_info?.logo %> alt="company logo" class="logo" />
                                    </div>
                                <% } %>
                                <h2><%= data?.company_info?.name?.en %></h2>
                                <div class="inline_container">
                                    <p class="vat">VAT ID</p>
                                    <p class="tax_code"><%= data?.company_info?.branch?.general_information?.tax_code %></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td class="content">
                    <div class="body">
                        <!-- general info section -->
                        <div class="general_info_container">
                            <div class="col_4 flex">
                                <p>Invoice Type</p><span><%= data?.invoice_type %></span>
                            </div>
                            <div class="col_4 flex">
                                <p>Sales Representative </p>
                                <span><%- data?.sales_representative?.name? data?.sales_representative?.name : "-" %></span>
                            </div>
                            <div class="col_4 flex">
                                <p><%- data?.invoice_type==="cash" ? "Date" : "VAT Id" %></p>
                                <span><%- data?.invoice_type==="cash" ? formatDate(data?.invoice_date) : data?.customer?.tax_code %></span>
                            </div>
                            <% if (data.payment_type.length===1){ %>
                                <div class="col_4 flex">
                                    <p>Payment Type </p>
                                    <span><%= data?.payment_type[0]?.name?.en %></span>
                                </div>
                            <% } else { %>
                                <div class="col_2 flex">
                                    <p>Payment Type </p>
                                    <div class="multi_payment_wrapper">
                                        <% for (let type of data?.payment_type){ %>
                                            <div>
                                                <span><%= type?.name?.en %></span>
                                                <span class="payment_amount"><%= type?.amount %></span>
                                            </div>
                                        <% } %>
                                    </div>
                                </div>
                            <% } %>
                            <% if(data?.invoice_type !=="cash" ){ %>
                                <div class="col_4 flex">
                                    <p>Due Date</p>
                                    <span><%= formatDate(data?.due_date) %></span>
                                </div>
                            <% } %>
                            <div class="col_4 flex">
                                <p>Note </p>
                                <span><%- data?.note? data?.note: "-" %></span>
                            </div>
                        </div>

                        <!-- table section -->
                        <div class="table_container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th class="head_cell text-left" style="width: 100px;">Item Code</th>
                                        <th class="head_cell text-left" style="width: 250px;">Item Name</th>
                                        <th class="head_cell text-center" style="width: 100px;">Price</th>
                                        <th class="head_cell text-center" style="width: 100px;">Qty</th>
                                        <th class="head_cell text-center" style="width: 100px;">Subtotal</th>
                                        <th class="head_cell text-center" style="width: 100px;">Dis.</th>
                                        <th class="head_cell text-center" style="width: 100px;">VAT (<%- data.tax_rate?data.tax_rate : 0 %>%)</th>
                                        <th class="head_cell text-center" style="width: 100px;">Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% for (let item of data?.transactions){ %>
                                        <tr>
                                            <td class="table_cell"><%= item?.code %></td>
                                            <td class="table_cell"><%= item?.item_invoice_name %></td>
                                            <td class="table_cell text-center"><%= Number(item?.price || 0)?.toFixed(2) %></td>
                                            <td class="table_cell text-center"><%= item?.qty %></td>
                                            <td class="table_cell text-center"><%= Number(item?.subtotal || 0)?.toFixed(2) %></td>
                                            <td class="table_cell text-center"><%= Number(item?.discount || 0)?.toFixed(2) %></td>
                                            <td class="table_cell text-center"><%= Number(item?.vat || 0)?.toFixed(2) %></td>
                                            <td class="table_cell text-center"><%= Number(item?.total || 0)?.toFixed(2) %></td>
                                        </tr>
                                    <% } %>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- table summary section  -->
                        <div class="summary_container">
                            <div class="summary_item">
                                <span class="title font_10">Items</span>
                                <span class="text bold_500"><%= Number(data?.total_item_qty || 0)?.toFixed(2) %></span>
                            </div>
                            <div class="summary_item">
                                <span class="title font_10">Subtotal</span>
                                <span class="text bold_500"><%= Number(data?.total_subtotal || 0)?.toFixed(2) %></span>
                            </div>
                            <div class="summary_item">
                                <span class="title font_10">Total Discount</span>
                                <span class="text bold_500"><%= Number(data?.total_discount || 0)?.toFixed(2) %></span>
                            </div>
                            <div class="summary_item">
                                <span class="title font_10">Total VAT</span>
                                <span class="text bold_500"><%= Number(data?.total_vat || 0)?.toFixed(2) %></span>
                            </div>
                            <div class="summary_item">
                                <span class="title font_10">Final Price</span>
                                <span class="text bold_600"><%= Number(data?.invoice_total || 0)?.toFixed(2) %></span>
                            </div>
                        </div>
                        
                        <div><img src=<%=data.qr_code %> alt="QR Code" class="qr_code"></div>
                    </div>
                </td>
            </tr>
        </tbody>
        <tfoot>
            <tr>
                <td>
                    <!-- footer section -->
                    <div class="footer_wrapper">
                        <div class="footer">
                            <div class="footer_item">
                                <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M2.41333 5.19333C3.37333 7.08 4.92 8.62667 6.80667 9.58667L8.27333 8.12C8.46 7.93333 8.72 7.88 8.95333 7.95333C9.7 8.2 10.5 8.33333 11.3333 8.33333C11.5101 8.33333 11.6797 8.40357 11.8047 8.5286C11.9298 8.65362 12 8.82319 12 9V11.3333C12 11.5101 11.9298 11.6797 11.8047 11.8047C11.6797 11.9298 11.5101 12 11.3333 12C8.32755 12 5.44487 10.806 3.31946 8.68054C1.19404 6.55513 0 3.67245 0 0.666667C0 0.489856 0.0702379 0.320286 0.195262 0.195262C0.320286 0.0702379 0.489856 0 0.666667 0H3C3.17681 0 3.34638 0.0702379 3.4714 0.195262C3.59643 0.320286 3.66667 0.489856 3.66667 0.666667C3.66667 1.5 3.8 2.3 4.04667 3.04667C4.12 3.28 4.06667 3.54 3.88 3.72667L2.41333 5.19333Z" fill="#FEFEFE" />
                                </svg>
                                <span><%- data.company_info.contact_person.phone? data.company_info.contact_person.phone: "-" %></span>
                            </div>
                            <div class="footer_item">
                                <svg width="15" height="10" viewBox="0 0 15 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M14.2 8.6V1.4C14.2 0.735997 13.664 0.199997 13 0.199997H1.792C1.128 0.199997 0.591995 0.735997 0.591995 1.4V8.6C0.591995 9.264 1.128 9.8 1.792 9.8H13C13.664 9.8 14.2 9.264 14.2 8.6ZM13.152 1.312C13.416 1.576 13.272 1.848 13.128 1.984L9.88 4.96L13 8.208C13.096 8.32 13.16 8.496 13.048 8.616C12.944 8.744 12.704 8.736 12.6 8.656L9.104 5.672L7.392 7.232L5.688 5.672L2.192 8.656C2.088 8.736 1.848 8.744 1.744 8.616C1.632 8.496 1.696 8.32 1.792 8.208L4.91199 4.96L1.664 1.984C1.52 1.848 1.376 1.576 1.64 1.312C1.904 1.048 2.176 1.176 2.4 1.368L7.392 5.4L12.392 1.368C12.616 1.176 12.888 1.048 13.152 1.312Z" fill="#FEFEFE" />
                                </svg>
                                <span><%- data.company_info.contact_person.email? data.company_info.contact_person.email:"-"%></span>
                            </div>
                            <div class="footer_item">
                                <svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M6.00001 0C4.54184 0.00172024 3.1439 0.581735 2.11282 1.61281C1.08174 2.64389 0.501726 4.04184 0.500006 5.5C0.498259 6.69161 0.887496 7.85089 1.60801 8.8C1.60801 8.8 1.75801 8.9975 1.78251 9.026L6.00001 14L10.2195 9.0235C10.2415 8.997 10.392 8.8 10.392 8.8L10.3925 8.7985C11.1127 7.84981 11.5017 6.69107 11.5 5.5C11.4983 4.04184 10.9183 2.64389 9.88719 1.61281C8.85612 0.581735 7.45817 0.00172024 6.00001 0ZM6.00001 7.5C5.60444 7.5 5.21776 7.3827 4.88887 7.16294C4.55997 6.94318 4.30362 6.63082 4.15225 6.26537C4.00087 5.89991 3.96126 5.49778 4.03844 5.10982C4.11561 4.72186 4.30609 4.36549 4.58579 4.08579C4.8655 3.80608 5.22186 3.6156 5.60983 3.53843C5.99779 3.46126 6.39992 3.50087 6.76537 3.65224C7.13082 3.80362 7.44318 4.05996 7.66294 4.38886C7.88271 4.71776 8.00001 5.10444 8.00001 5.5C7.99934 6.03023 7.78842 6.53855 7.41349 6.91348C7.03856 7.28841 6.53024 7.49934 6.00001 7.5Z" fill="#FEFEFE" />
                                </svg>
                                <span><%- data.company_info.address? data.company_info.address:"-"%></span>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        </tfoot>
    </table>
</body>

</html>

<% function formatDate(date=new Date()) { 
    const convertToDate = new Date(date); 
    const day = convertToDate.getDate().toString().padStart(2, '0'); 
    const month = (convertToDate.getMonth() + 1).toString().padStart(2, '0'); 
    const year = convertToDate.getFullYear();
    return \`\${year}/\${month}/\${day}\`;
}; %>`,
  ar: `<!-- documentTemplate.ejs -->
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice #<%= data?.invoice_no %></title>
    <style>
        @media print {
            @page {
                size: A4;
                margin: 0;
            }

            thead {
                display: table-header-group;
            }

            tfoot {
                display: table-footer-group;
            }

            body {
                margin: 0;
            }

            .header,
            .footer {
                position: fixed;
                width: 100%;
            }

            .header {
                top: 0;
            }

            .footer {
                bottom: 0;
            }

            .page-break {
                page-break-before: always;
            }
        }

        .main {
            font-size: 12px;
            color: #121212;
            background-color: #fff;
            font-family: 'Poppins', Arial, sans-serif;
            line-height: 16px;
            width: 100%;
            min-width: 100%;
            max-width: 100%;
            height: 100%;
            min-height: 100%;
            max-height: 100%;
            margin: 0;
            border-collapse: collapse;
            direction: rtl;
        }

        .body {
            width: 100%;
            height: 100%;
        }

        p,
        h1,
        h2 {
            margin: 0;
        }

        h1 {
            font-size: 20px;
            font-weight: 600;
            line-height: 28px;
            text-transform: uppercase;
            margin-bottom: 2px;
        }

        h2 {
            font-size: 16px;
            font-weight: 600;
            line-height: 23px;
            text-transform: uppercase;
            margin-bottom: 8px;
            white-space: normal;
            word-break: normal;
            overflow-wrap: normal;
        }

        .inline_container {
            display: flex;
            gap: 6px;
            margin-bottom: 4px
        }

        .block_container {
            display: flex;
            flex-direction: column;
        }

        .title {
            color: #67677A;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .text {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .font_10 {
            font-size: 10px !important;
        }

        .bold_500 {
            font-weight: 500 !important;
        }

        .bold_600 {
            font-weight: 600 !important;
        }

        .text-center {
            text-align: center !important;
        }

        .text-left {
            text-align: left !important;
        }

        .text-right {
            text-align: right !important;
        }

        .col_4 {
            grid-column: span 4;
            width: 100%;
        }

        .col_2 {
            grid-column: span 8;
            width: 100%;
        }

        .flex {
            display: flex;
        }

        /* Header */
        .header_wrapper {
            height: 175px;
            width: 100%;
            margin-bottom: 24px;
        }

        .header {
            padding: 16px 32px !important;
            width: 100%;
            display: flex;
            gap: 12px;
            justify-content: space-between;
            align-items: center;
            border-bottom: 0.5px solid #EBEDF2;
            background: #F9FAFC;
            height: 175px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .header_column {
            max-width: 40%;
            position: relative;
        }

        .header_border {
            border-right: 12px #9112FA solid !important;
            border-radius: 20px;
            position: absolute;
            height: 105%;
            right: -40px;
            top: -3px;
        }

        .logo {
            height: 34px;
            margin-bottom: 4px;
        }

        .vat {
            color: #67677A;
            font-size: 12px;
            font-weight: 500;
            line-height: 18px;
        }

        .tax_code {
            color: #121221;
            font-size: 12px;
            font-weight: 500;
            line-height: 18px;
        }

        /* general info section */
        .general_info_container {
            color: #67677A;
            font-weight: 500;
            min-width: 100%;
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            grid-gap: 14px;
            justify-items: center;
            align-items: center;
            margin: 0 24px 16px;
        }

        .general_info_container p {
            margin: 0 4px;
        }

        .general_info_container span {
            font-weight: 600;
            color: #121212;
        }

        .general_info_container .multi_payment_wrapper {
            display: flex;
            gap: 8px;
            flex-wrap: nowrap;
        }

        .general_info_container .multi_payment_wrapper .payment_amount {
            color: #67677A;
            display: inline-block;
            margin: 0 1px;
        }

        /* table section */
        .table_container {
            overflow: hidden;
            margin: 0 24px;
        }

        .table {
            border-collapse: collapse;
            margin: 0;
            width: 100%;
        }

        .table_cell,
        .head_cell {
            border: 1px solid #D7D7DE;
            font-size: 10px;
            font-weight: 500;
            padding: 7px 24px;
        }

        .head_cell {
            text-align: center;
            background: #F9FAFC !important;
            height: 34px;
        }

        .table_cell {
            font-size: 12px;
            height: 32px;
        }

        /*  table summary section */
        .summary_container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 16px;
            margin: 16px 24px;
            border: 1px solid #E8E8ED;
            background: #F9FAFC;
            border-radius: 5px;
        }

        .summary_item {
            display: flex;
            flex-direction: column;
            min-width: 13%;
        }

        .qr_code {
            margin: 0 24px;
            width: 72px;
            height: 72px;
        }

        /* footer section */
        .footer_wrapper {
            height: 50px;
            width: 100%;
            margin-top: 24px;
        }

        .footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 45px;
            color: white;
            width: 100%;
            overflow: hidden;
            height: 50px;
            background-color: #9112FA;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .footer_item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .footer_item span {
            font-size: 10px;
        }

        /* Prevent content from going behind the header and footer */
        .content {
            padding: 0;
        }
    </style>
</head>

<body>
    <table class="main">
        <thead>
            <tr>
                <td>
                    <!-- Header -->
                    <div class="header_wrapper">
                        <div class="header">
                            <div class="header_column">
                                <div class="header_border"></div>
                                <div>
                                    <h1>فاتورة مبيعات</h1>
                                    <div class="inline_container">
                                        <p class="title">رقم الفاتورة</p>
                                        <p class="text">#<%= data?.invoice_no %></p>
                                    </div>
                                    <% if(data?.invoice_type !=="cash" ){ %>
                                        <div class="inline_container">
                                            <p class="title">التاريخ</p>
                                            <p class="text"><%= formatDate(data?.invoice_date) %></p>
                                        </div>
                                    <% } %>
                                    <div class="inline_container">
                                        <p class="title">إسم العميل </p>
                                        <p class="text"><%= data.customer?.name %></p>
                                    </div>
                                    <div class="inline_container">
                                        <p class="title">رقم هاتف العميل</p>
                                        <p class="text"><%- data?.customer?.phone? data?.customer?.phone: "-" %></p>
                                    </div>
                                    <% if(data?.invoice_type==="cash" ){ %>
                                        <div class="inline_container">
                                            <p class="title">الرقم الضريبي للعميل</p>
                                            <p class="text"><%- data.customer?.tax_code? data.customer?.tax_code: "-" %></p>
                                        </div>
                                    <% } %>
                                </div>
                            </div>
                            <div class="header_column">
                                <% if(data?.company_info?.logo){ %>
                                    <div>
                                        <img src=<%=data?.company_info?.logo %> alt="company logo" class="logo" />
                                    </div>
                                <% } %>
                                <h2><%= data?.company_info?.name?.ar %></h2>
                                <div class="inline_container">
                                    <p class="vat">الرقم الضريبي</p>
                                    <p class="tax_code"><%= data?.company_info?.branch?.general_information?.tax_code %></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td class="content">
                    <div class="body">
                        <!-- general info section -->
                        <div class="general_info_container">
                            <div class="col_4 flex">
                                <p>نوع الفاتورة</p><span><%= data?.invoice_type %></span>
                            </div>
                            <div class="col_4 flex">
                                <p>مندوب المبيعات</p>
                                <span><%- data?.sales_representative?.name? data?.sales_representative?.name : "-" %></span>
                            </div>
                            <div class="col_4 flex">
                                <p><%- data?.invoice_type==="cash" ? "التاريخ" : "الرقم الضريبي للعميل" %></p>
                                <span><%- data?.invoice_type==="cash" ? formatDate(data?.invoice_date) : data?.customer?.tax_code %></span>
                            </div>
                            <% if (data.payment_type.length===1){ %>
                                <div class="col_4 flex">
                                    <p>نوع الدفع</p>
                                    <span><%= data?.payment_type[0]?.name?.ar %></span>
                                </div>
                            <% } else { %>
                                <div class="col_2 flex">
                                    <p>نوع الدفع</p>
                                    <div class="multi_payment_wrapper">
                                        <% for (let type of data?.payment_type){ %>
                                            <div>
                                                <span><%= type?.name?.ar %></span>
                                                <span class="payment_amount"><%= type?.amount %></span>
                                            </div>
                                        <% } %>
                                    </div>
                                </div>
                            <% } %>
                            <% if(data?.invoice_type !=="cash" ){ %>
                                <div class="col_4 flex">
                                    <p>تاريخ الاستحقاق</p>
                                    <span><%= formatDate(data?.due_date) %></span>
                                </div>
                            <% } %>
                            <div class="col_4 flex">
                                <p>ملحوظة</p>
                                <span><%- data?.note? data?.note: "-" %></span>
                            </div>
                        </div>

                        <!-- table section -->
                        <div class="table_container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th class="head_cell text-right" style="width: 100px;">رقم الصنف</th>
                                        <th class="head_cell text-right" style="width: 250px;">إسم الصنف </th>
                                        <th class="head_cell text-center" style="width: 100px;">السعر</th>
                                        <th class="head_cell text-center" style="width: 100px;">الكمية</th>
                                        <th class="head_cell text-center" style="width: 100px;">المجموع</th>
                                        <th class="head_cell text-center" style="width: 100px;">الخصم</th>
                                        <th class="head_cell text-center" style="width: 100px;">الضريبة (<%-
                                                data.tax_rate?data.tax_rate : 0 %>%)</th>
                                        <th class="head_cell text-center" style="width: 100px;">الاجمالي</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% for (let item of data?.transactions){ %>
                                        <tr>
                                            <td class="table_cell"><%= item?.code %></td>
                                            <td class="table_cell"><%= item?.item_invoice_name %></td>
                                            <td class="table_cell text-center"><%= Number(item?.price || 0)?.toFixed(2) %></td>
                                            <td class="table_cell text-center"><%= item?.qty %></td>
                                            <td class="table_cell text-center"><%= Number(item?.subtotal || 0)?.toFixed(2) %></td>
                                            <td class="table_cell text-center"><%= Number(item?.discount || 0)?.toFixed(2) %></td>
                                            <td class="table_cell text-center"><%= Number(item?.vat || 0)?.toFixed(2) %></td>
                                            <td class="table_cell text-center"><%= Number(item?.total || 0)?.toFixed(2) %></td>
                                        </tr>
                                    <% } %>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- table summary section  -->
                        <div class="summary_container">
                            <div class="summary_item">
                                <span class="title font_10">الاصناف</span>
                                <span class="text bold_500"><%= Number(data?.total_item_qty || 0)?.toFixed(2) %></span>
                            </div>
                            <div class="summary_item">
                                <span class="title font_10">المجموع الفرعي</span>
                                <span class="text bold_500"><%= Number(data?.total_subtotal || 0)?.toFixed(2) %></span>
                            </div>
                            <div class="summary_item">
                                <span class="title font_10">الخصم الاجمالي</span>
                                <span class="text bold_500"><%= Number(data?.total_discount || 0)?.toFixed(2) %></span>
                            </div>
                            <div class="summary_item">
                                <span class="title font_10">الضريبة الاجماليه</span>
                                <span class="text bold_500"><%= Number(data?.total_vat || 0)?.toFixed(2) %></span>
                            </div>
                            <div class="summary_item">
                                <span class="title font_10">الاجمالي</span>
                                <span class="text bold_600"><%= Number(data?.invoice_total || 0)?.toFixed(2) %></span>
                            </div>
                        </div>
                        
                        <div><img src=<%=data.qr_code %> alt="QR Code" class="qr_code"></div>
                    </div>
                </td>
            </tr>
        </tbody>
        <tfoot>
            <tr>
                <td>
                    <!-- footer section -->
                    <div class="footer_wrapper">
                        <div class="footer">
                            <div class="footer_item">
                                <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M2.41333 5.19333C3.37333 7.08 4.92 8.62667 6.80667 9.58667L8.27333 8.12C8.46 7.93333 8.72 7.88 8.95333 7.95333C9.7 8.2 10.5 8.33333 11.3333 8.33333C11.5101 8.33333 11.6797 8.40357 11.8047 8.5286C11.9298 8.65362 12 8.82319 12 9V11.3333C12 11.5101 11.9298 11.6797 11.8047 11.8047C11.6797 11.9298 11.5101 12 11.3333 12C8.32755 12 5.44487 10.806 3.31946 8.68054C1.19404 6.55513 0 3.67245 0 0.666667C0 0.489856 0.0702379 0.320286 0.195262 0.195262C0.320286 0.0702379 0.489856 0 0.666667 0H3C3.17681 0 3.34638 0.0702379 3.4714 0.195262C3.59643 0.320286 3.66667 0.489856 3.66667 0.666667C3.66667 1.5 3.8 2.3 4.04667 3.04667C4.12 3.28 4.06667 3.54 3.88 3.72667L2.41333 5.19333Z" fill="#FEFEFE" />
                                </svg>
                                <span><%- data.company_info.contact_person.phone? data.company_info.contact_person.phone: "-" %></span>
                            </div>
                            <div class="footer_item">
                                <svg width="15" height="10" viewBox="0 0 15 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M14.2 8.6V1.4C14.2 0.735997 13.664 0.199997 13 0.199997H1.792C1.128 0.199997 0.591995 0.735997 0.591995 1.4V8.6C0.591995 9.264 1.128 9.8 1.792 9.8H13C13.664 9.8 14.2 9.264 14.2 8.6ZM13.152 1.312C13.416 1.576 13.272 1.848 13.128 1.984L9.88 4.96L13 8.208C13.096 8.32 13.16 8.496 13.048 8.616C12.944 8.744 12.704 8.736 12.6 8.656L9.104 5.672L7.392 7.232L5.688 5.672L2.192 8.656C2.088 8.736 1.848 8.744 1.744 8.616C1.632 8.496 1.696 8.32 1.792 8.208L4.91199 4.96L1.664 1.984C1.52 1.848 1.376 1.576 1.64 1.312C1.904 1.048 2.176 1.176 2.4 1.368L7.392 5.4L12.392 1.368C12.616 1.176 12.888 1.048 13.152 1.312Z" fill="#FEFEFE" />
                                </svg>
                                <span><%- data.company_info.contact_person.email? data.company_info.contact_person.email:"-"%></span>
                            </div>
                            <div class="footer_item">
                                <svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M6.00001 0C4.54184 0.00172024 3.1439 0.581735 2.11282 1.61281C1.08174 2.64389 0.501726 4.04184 0.500006 5.5C0.498259 6.69161 0.887496 7.85089 1.60801 8.8C1.60801 8.8 1.75801 8.9975 1.78251 9.026L6.00001 14L10.2195 9.0235C10.2415 8.997 10.392 8.8 10.392 8.8L10.3925 8.7985C11.1127 7.84981 11.5017 6.69107 11.5 5.5C11.4983 4.04184 10.9183 2.64389 9.88719 1.61281C8.85612 0.581735 7.45817 0.00172024 6.00001 0ZM6.00001 7.5C5.60444 7.5 5.21776 7.3827 4.88887 7.16294C4.55997 6.94318 4.30362 6.63082 4.15225 6.26537C4.00087 5.89991 3.96126 5.49778 4.03844 5.10982C4.11561 4.72186 4.30609 4.36549 4.58579 4.08579C4.8655 3.80608 5.22186 3.6156 5.60983 3.53843C5.99779 3.46126 6.39992 3.50087 6.76537 3.65224C7.13082 3.80362 7.44318 4.05996 7.66294 4.38886C7.88271 4.71776 8.00001 5.10444 8.00001 5.5C7.99934 6.03023 7.78842 6.53855 7.41349 6.91348C7.03856 7.28841 6.53024 7.49934 6.00001 7.5Z" fill="#FEFEFE" />
                                </svg>
                                <span><%- data.company_info.address? data.company_info.address:"-"%></span>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        </tfoot>
    </table>
</body>

</html>

<% function formatDate(date=new Date()) { 
    const convertToDate = new Date(date); 
    const day = convertToDate.getDate().toString().padStart(2, '0'); 
    const month = (convertToDate.getMonth() + 1).toString().padStart(2, '0'); 
    const year = convertToDate.getFullYear();
    return \`\${year}/\${month}/\${day}\`;
}; %>`,
};
