import { serviceVersionSchema } from '../../modules/script-runner/schema/version.schema';
import { schemaVersion } from '../../modules/script-runner/database_scripts/version';

export class ServiceVersionSeeder {
  serviceVersionModel: any;

  constructor(private db: any) {
    this.db = db;
    this.serviceVersionModel = this.db.model(
      'serviceversions',
      serviceVersionSchema,
    );
  }

  async seed() {
    await this.serviceVersionModel.create({ version: schemaVersion.version });
  }
}
