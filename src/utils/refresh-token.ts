import * as jwt from 'jsonwebtoken';

export function generateToken(
  payload: object,
  secret: string,
  expiresIn: number,
) {
  return jwt.sign(payload, secret, {
    algorithm: 'HS256',
    expiresIn: `${expiresIn}s`,
  });
}

export function generateApiToken(payload: object, expiresIn: number) {
  return jwt.sign(payload, undefined, {
    algorithm: 'none',
    expiresIn: `${expiresIn}s`,
  });
}

export function validateToken(token: string, secret: string) {
  try {
    return jwt.verify(token, secret);
  } catch (error) {
    return false;
  }
}
