import { HttpException, HttpStatus } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
export type Services = 'accounting' | 'trade' | 'inventory' | 'tenant';
export interface IHttpExceptions extends Partial<HttpException> {
  responseData: string | object;
  statusCode: HttpStatus;
  timestamp: Date | string;
  path?: string;
  serviceTrace: Services[];
  exceptionCode: string;
  additionalData?: string | object;
}
export interface IRpcExceptions extends Partial<RpcException> {
  responseData: string | object;
  additionalData?: string | object;
  statusCode: HttpStatus;
  timestamp: Date | string;
  path?: string;
  serviceTrace: Services[];
  exceptionCode: string;
  response?: {
    response: any;
    exceptionCode: any;
  };
}
