import { BadRequestException } from '@nestjs/common';
import { ValidationError } from 'class-validator';
import { Types } from 'mongoose';

export const safeMongoIdTransformer = ({ value, property }) => {
  try {
    if (value === null || value === undefined) {
      return;
    }

    if (
      Types.ObjectId.isValid(value) &&
      new Types.ObjectId(value).toString() === String(value)
    ) {
      return new Types.ObjectId(value);
    }

    const error = new ValidationError();
    error.property = property;
    error.constraints = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      isMongoId: `${property} must be a valid MongoDB ObjectId`,
    };
    error.children = [];

    const response = {
      statusCode: 400,
      responseData: [error],
      exceptionCode: 'badRequest',
      message: `${property} must be a valid MongoDB ObjectId`,
    };

    throw new BadRequestException(response);
  } catch (error) {
    const response = {
      statusCode: 400,
      responseData: [
        {
          property,
          constraints: {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            isMongoId: `${property} must be a valid MongoDB ObjectId`,
          },
          children: [],
        },
      ],
      exceptionCode: 'badRequest',
      message: `${property} must be a valid MongoDB ObjectId`,
    };

    throw new BadRequestException(response);
  }
};
