import { NestFactory } from '@nestjs/core';
import { ScriptRunnerService } from '../modules/script-runner/script-runner.service';
import { AppModule } from '../app.module';

async function run() {
  const appContext = await NestFactory.createApplicationContext(AppModule);
  const scriptRunnerService = appContext.get(ScriptRunnerService);

  // write the following string to console in yollow font use --drop to drop the database --tenant 1 seed tenant_1 database
  console.log(
    '\x1b[33m%s\x1b[0m',
    'you can use the following arguments "npm run scriptRunner -- --tenant 1 "',
  );
  console.log(
    '\x1b[33m%s\x1b[0m',
    '--tenant id     run scriptRunner for tenant_id database',
  );
  console.log(
    '\x1b[33m%s\x1b[0m',
    '--multi     run scriptRunner for tenant 1 to 3',
  );

  // check if arugment --tenant 0 or --tenant 1 or --tenant 2 exist put the number in tenant varible
  let tenantId: number;
  process.argv.forEach((val, index) => {
    if (val === '--tenant') {
      tenantId = Number(process.argv[index + 1]).valueOf();
    }
  });

  if (process.argv.includes('--multi')) {
    await scriptRunnerService.executeForAll();
  } else {
    const i: number = tenantId || 1;
    await scriptRunnerService.executeDatabaseScripts(i);
  }
  await appContext.close();
  process.exit(0);
}

run();
