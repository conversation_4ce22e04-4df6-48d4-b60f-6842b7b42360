import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';

export function hasOneDefaultValue(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'hasOneDefaultValue',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        validate(value: any, args: ValidationArguments) {
          if (!Array.isArray(value)) return false; // Ensure it's an array
          const defaultUnits = value.filter((unit) => unit.is_default === true);
          return defaultUnits.length <= 1; // Allow only one or none
        },
        defaultMessage(args: ValidationArguments) {
          return `The '${args.property}' array can have at most one unit with is_default set to true.`;
        },
      },
    });
  };
}
