import {
  registerDecorator,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';

@ValidatorConstraint({ async: false })
export class IsTwoDecimalPlacesConstraint
  implements ValidatorConstraintInterface
{
  validate(value: any) {
    if (typeof value !== 'number') {
      return false;
    }
    const regex = /^\d+(\.\d{1,2})?$/;
    return regex.test(value.toString());
  }

  defaultMessage() {
    return 'Value must be a number with up to two decimal places';
  }
}

export function isTwoDecimalPlaces(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsTwoDecimalPlacesConstraint,
    });
  };
}
