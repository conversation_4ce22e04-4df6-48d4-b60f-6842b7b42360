import { pick } from 'lodash';
import * as QRCode from 'qrcode';

export class TLV {
  base64 = '';
  qrCode1 = '';
  sellersName: string;
  vatNumber: string;
  invoiceAmount: string;
  vatAmount: string;
  timestamp: string;
  invoiceHash?: string;
  ecdsaSig?: Buffer;
  ecdsaPub?: Buffer;
  certSig?: Buffer;

  constructor(
    sellersName: string,
    vatNumber: string,
    invoiceAmount: string,
    vatAmount: string,
    timestamp: string,
    invoiceHash?: string,
    ecdsaSig?: Buffer,
    ecdsaPub?: Buffer,
    certSig?: Buffer,
  ) {
    this.sellersName = sellersName;
    this.vatNumber = vatNumber;
    this.invoiceAmount = invoiceAmount;
    this.vatAmount = vatAmount;
    this.timestamp = timestamp;
    this.invoiceHash = invoiceHash;
    this.ecdsaSig = ecdsaSig;
    this.ecdsaPub = ecdsaPub;
    this.certSig = certSig;
  }

  // Helper to encode each TLV field
  encodeTLVField(tag: number, value: string | Buffer): Buffer {
    const tagBuff = Buffer.from([tag]);
    let valueBuff: Buffer;

    // Handle strings and buffers differently
    if (typeof value === 'string') {
      valueBuff = Buffer.from(value, 'utf-8');
    } else {
      valueBuff = value;
    }

    // Dynamically allocate buffer for length depending on value size
    let lenBuff;
    const len = valueBuff.length;
    if (len < 256) {
      lenBuff = Buffer.alloc(1);
      lenBuff.writeUInt8(len);
    } else {
      lenBuff = Buffer.alloc(2); // 2 bytes for larger lengths
      lenBuff.writeUInt16BE(len);
    }

    return Buffer.concat([tagBuff, lenBuff, valueBuff]);
  }

  // Method to generate base64 TLV
  toTlvB64() {
    const result = [];
    const obj = pick(this, [
      'sellersName',
      'vatNumber',
      'timestamp',
      'invoiceAmount',
      'vatAmount',
      'invoiceHash',
      'ecdsaSig',
      'ecdsaPub',
      'certSig',
    ]);

    // Loop over the object and encode each field as TLV
    Object.keys(obj).forEach((key, index) => {
      const value = this[key];
      if (value) {
        result.push(this.encodeTLVField(index + 1, value));
      }
    });

    // Concatenate the final buffer and convert to base64
    const base64buff = Buffer.concat(result);
    this.base64 = base64buff.toString('base64');
    return this;
  }

  // Method to generate QR code from the base64 TLV
  async toQrCode(): Promise<string> {
    if (!this.base64) {
      this.toTlvB64(); // Ensure TLV is encoded before generating QR code
    }
    return QRCode.toDataURL(this.base64);
  }
}
