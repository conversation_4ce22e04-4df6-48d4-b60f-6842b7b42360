import Big from 'big.js';

export class Calculator {
  private result: Big;

  constructor(number = 0) {
    this.result = new Big(number);
  }

  public plus(number) {
    this.result = this.result.plus(number);
    return this;
  }

  public minus(number) {
    this.result = this.result.minus(number);
    return this;
  }

  public multiply(number) {
    this.result = this.result.mul(number);
    return this;
  }

  public divide(number) {
    this.result = this.result.div(number);
    return this;
  }

  public static sum(number1, number2) {
    return new Calculator(number1).plus(number2);
  }

  public static subtract(number1, number2) {
    return new Calculator(number1).minus(number2);
  }

  public static mul(number1, number2) {
    return new Calculator(number1).multiply(number2);
  }

  public static div(number1, number2) {
    return new Calculator(number1).divide(number2);
  }

  public static abs(number) {
    return new Calculator(number).abs();
  }

  public abs() {
    this.result = this.result.abs();
    return this;
  }

  public round() {
    this.result = this.result.round(2, 1);
    return this;
  }

  public number() {
    return Number(this.result.valueOf());
  }

  public valueOf() {
    return this.number();
  }

  public clear() {
    this.result = new Big(0);
  }
}
