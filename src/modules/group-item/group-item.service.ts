import { HttpException, Injectable, Scope } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ObjectId, Types } from 'mongoose';
import { CreateGroupItemDto } from './dto/create-group-item.dto';
import { UpdateGroupItemDto } from './dto/update-group-item.dto';
import { ItemGroup } from './schema/item-group.schema';
import { nameOf } from '../../utils/object-key-name';
import { GetGroupItemDto } from './dto/get-group-item.dto';
import { GetItemGroupDto } from './dto/get-item-group.dto';
import { usersExceptionCode } from '../../exceptions/exception-code.users';
import { paginate } from '../../utils/dto/pagination.dto';

@Injectable({ scope: Scope.REQUEST, durable: true })
export class GroupItemService {
  constructor(
    @InjectModel(ItemGroup.name)
    private readonly itemGroupModel: Model<ItemGroup>,
  ) {}

  async create(createGroupItemDto: CreateGroupItemDto) {
    if (typeof createGroupItemDto.code == 'string') {
      await this.checkCode(createGroupItemDto.code);
    }
    return new this.itemGroupModel(createGroupItemDto).save();
  }

  async createBulk(createGroupItemDtos: CreateGroupItemDto[]) {
    const bulkOps = createGroupItemDtos.map((dto) => ({
      updateOne: {
        filter: { id: dto.id },
        update: { $set: dto },
        upsert: true,
      },
    }));

    return this.itemGroupModel.bulkWrite(bulkOps);
  }

  async findAll(query: GetGroupItemDto) {
    const { page, limit, sortBy, sortType } = query;
    const sort = { [sortBy]: sortType };

    const filter: any = {};

    const result = await this.itemGroupModel
      .find(filter, undefined, { sort })
      .skip((page - 1) * limit)
      .limit(limit);
    const meta = await paginate(limit, page, this.itemGroupModel, {});
    return { result, meta };
  }

  async abstract() {
    const filter: any = {};

    const result = await this.itemGroupModel
      .find(filter)
      .select('id code name')
      .lean()
      .exec();

    const transformedGroups = result.map((group) => ({
      ...group,
      id: String(group.id),
    }));

    return { result: transformedGroups };
  }

  async findOne(dto: GetItemGroupDto) {
    return await this.itemGroupModel.findOne({ id: dto.id });
  }

  async update(_id: ObjectId, updateGroupItemDto: UpdateGroupItemDto) {
    const groupItem = await this.itemGroupModel.findOne({
      _id,
    });
    if (!groupItem) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.itemGroupNotFound,
        ),
        usersExceptionCode.itemGroupNotFound,
      );
    }
    if (typeof updateGroupItemDto.code == 'string') {
      await this.checkCode(updateGroupItemDto.code, groupItem._id);
    }

    return await this.itemGroupModel.updateOne(
      { _id: _id },
      updateGroupItemDto,
    );
  }

  async delete(ids: number[]) {
    const result = await this.itemGroupModel.deleteMany({
      id: { $nin: ids },
    });
    return result;
  }

  private async checkCode(code: string, excludeId?: Types.ObjectId) {
    const query = { code };
    if (excludeId) {
      query['_id'] = { $ne: excludeId };
    }

    const codeExists = await this.itemGroupModel.findOne(query).exec();

    if (codeExists) {
      throw new HttpException(
        nameOf(usersExceptionCode, (exception) => exception.numberDuplicated),
        usersExceptionCode.numberDuplicated,
      );
    }
  }
}
