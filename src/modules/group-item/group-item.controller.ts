import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { GroupItemService } from './group-item.service';
import { CreateGroupItemDto } from './dto/create-group-item.dto';
import {
  Api<PERSON>earerA<PERSON>,
  ApiBody,
  ApiHeader,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { UpdateGroupItemDto } from './dto/update-group-item.dto';
import { ObjectId } from 'mongoose';
import { CaslGuard } from '../casl/guards/casl.guard';
import { Abilities } from '../casl/guards/abilities.decorator';
import { GetGroupItemDto } from './dto/get-group-item.dto';
import { GetItemGroupDto } from './dto/get-item-group.dto';
import { authType } from '../auth/guards/auth-type.decorator';
import { DeleteExceptIdsDto } from '../../utils/dto/delete-except-ids.dto';

@Controller('inventory/group-item')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@ApiTags('item_group')
export class GroupItemController {
  constructor(private readonly groupItemService: GroupItemService) {}

  @Post()
  @authType('apiKey')
  @ApiBearerAuth('TOKEN')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'create', subject: 'item_group' })
  @ApiBody({ type: [CreateGroupItemDto] })
  create(@Body() createGroupItemDto: CreateGroupItemDto[]) {
    return this.groupItemService.createBulk(createGroupItemDto);
  }

  @Get()
  @authType('apiKey')
  @ApiBearerAuth('TOKEN')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'item_group' })
  findAll(@Query() query: GetGroupItemDto) {
    return this.groupItemService.findAll(query);
  }

  @Get('abstract')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'abstract_item_groups' })
  async abstract() {
    return await this.groupItemService.abstract();
  }

  @Get('one')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'read', subject: 'item_group' })
  async findOne(@Query() query: GetItemGroupDto) {
    return await this.groupItemService.findOne(query);
  }

  @ApiParam({ name: 'id' })
  @UseGuards(CaslGuard)
  @Abilities({ action: 'edit', subject: 'item_group' })
  @Patch(':id')
  update(
    @Param('id') id: ObjectId,
    @Body() updateStoreDto: UpdateGroupItemDto,
  ) {
    return this.groupItemService.update(id, updateStoreDto);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'delete', subject: 'item_group' })
  @authType('apiKey')
  @ApiBearerAuth('TOKEN')
  @Delete()
  delete(@Body() dto: DeleteExceptIdsDto) {
    return this.groupItemService.delete(dto.ids);
  }
}
