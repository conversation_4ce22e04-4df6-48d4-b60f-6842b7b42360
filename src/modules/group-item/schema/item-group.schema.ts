import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Name } from '../../../utils/schemas';

@Schema()
export class ItemGroup {
  @Prop({ type: Number, required: true, unique: true })
  id: number;

  @Prop({ type: String, required: true })
  code: string;

  @Prop({ required: true })
  name: Name;
}

export const itemGroupSchema = SchemaFactory.createForClass(ItemGroup);
