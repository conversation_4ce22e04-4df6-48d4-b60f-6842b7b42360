import { Modu<PERSON> } from '@nestjs/common';
import { GroupItemService } from './group-item.service';
import { GroupItemController } from './group-item.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { ItemGroup, itemGroupSchema } from './schema/item-group.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ItemGroup.name, schema: itemGroupSchema },
    ]),
  ],
  controllers: [GroupItemController],
  providers: [GroupItemService],
  exports: [GroupItemService],
})
export class GroupItemModule {}
