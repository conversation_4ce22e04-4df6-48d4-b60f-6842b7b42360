import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  MongooseOptionsFactory,
  MongooseModuleOptions,
} from '@nestjs/mongoose';

@Injectable()
export class CommonDbConfigService implements MongooseOptionsFactory {
  constructor(private readonly configService: ConfigService) {}

  createDbUrl(): string {
    let baseUrl = this.configService.get('DB_URL')?.trim() || ''; // e.g., 'mongodb://host1:27017,host2:27017/'
    const prefix = this.configService.get('PREFIX_DB') || '';
    const dbName = this.configService.get<string>('COMMON_DB') || '';

    if (baseUrl.endsWith('/')) {
      baseUrl = baseUrl.slice(0, -1);
    }

    const fullDbName = `${prefix}${dbName}`;
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const params = new URLSearchParams({ authSource: 'admin' });

    return `${baseUrl}/${fullDbName}?${params.toString()}`;
  }

  createMongooseOptions(): MongooseModuleOptions {
    const uri = this.createDbUrl();
    return {
      uri,
      connectionFactory: (connection) => {
        // connection.set('debug', 'true');
        // connection.set('debug', { shell: true });
        // connection.once('open', () => {
        //   console.error(`Mongoose just open a connection to: ${uri}`);
        // });
        // connection.once('close', () => {
        //   console.error(`Mongoose just closed a connection to: ${uri}`);
        // });
        // // If connection is already open, log immediately
        // if (connection.readyState === 1)
        //   console.error(`Mongoose already connected to: ${uri}`);
        const topology = connection.client.topology;
        console.log('MongoDB topology:', topology?.description?.type); // Should be "ReplicaSetWithPrimary"

        return connection;
      },
    };
  }
}
