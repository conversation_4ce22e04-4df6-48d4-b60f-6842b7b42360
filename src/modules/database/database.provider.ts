import { Connection, Mongoose } from 'mongoose';

class DataBases extends Mongoose {
  private databases: { [key: string]: Connection } = {};
  private getConnectionUri = (companyName = '') => companyName;

  public async getConnection(companyName = ''): Promise<Connection> {
    const connection = this.databases[companyName];
    return connection ? connection : await this.createDataBase(companyName);
  }
  private async createDataBase(comapnyName = ''): Promise<Connection> {
    //  create new connection and if the database not exists just create new one
    const newConnection = this.createConnection(
      this.getConnectionUri(comapnyName),
    );
    this.databases[comapnyName] = newConnection;
    return newConnection;
  }
}
export default new DataBases();
