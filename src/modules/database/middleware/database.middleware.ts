import { Injectable, NestMiddleware } from '@nestjs/common';
import * as jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class DatabaseMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    if (req.headers['cookie']) {
      const accessToken: string = req.headers['cookie'].split('=')[1];

      jwt.decode(accessToken.slice(0, accessToken.indexOf(';')));
    }
    next();
  }
}
