import { Inject, Injectable, Scope } from '@nestjs/common';
import {
  MongooseOptionsFactory,
  MongooseModuleOptions,
} from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';
import { CONTEXT } from '@nestjs/microservices';
import { ContextPayload } from '../auth/interfaces/contextPayload.interface';

@Injectable({ scope: Scope.REQUEST, durable: true })
export class MongooseConfigService implements MongooseOptionsFactory {
  constructor(
    @Inject(CONTEXT) private readonly contextId: ContextPayload,
    private readonly configService: ConfigService,
  ) {}

  createDbUrl(database?: number): string {
    let baseUrl = this.configService.get('DB_URL')?.trim(); // e.g., 'mongodb://host1:27017,host2:27017'
    const prefix = this.configService.get('PREFIX_DB') || '';
    const commonDb = this.configService.get('COMMON_DB');

    if (baseUrl.endsWith('/')) {
      baseUrl = baseUrl.slice(0, -1);
    }
    const dbName =
      typeof database === 'number' && database >= 0
        ? `${prefix}tenant_${database}`
        : `${prefix}${commonDb}`;

    const params = new URLSearchParams({
      // eslint-disable-next-line @typescript-eslint/naming-convention
      authSource: 'admin',
    });

    return `${baseUrl}/${dbName}?${params.toString()}`;
  }

  async createMongooseOptions(): Promise<MongooseModuleOptions> {
    const uri = this.createDbUrl(this.contextId.tenantId);
    return {
      uri,
      connectionFactory: (connection) => {
        // connection.set('debug', 'true');
        // connection.set('debug', { shell: true });
        // connection.once('open', () => {
        //   console.error(`Mongoose just open a connection to: ${uri}`);
        // });
        // connection.once('close', () => {
        //   console.error(`Mongoose just closed a connection to: ${uri}`);
        // });
        // If connection is already open, log immediately
        // if (connection.readyState === 1)
        //   console.error(`Mongoose already connected to: ${uri}`);
        const topology = connection.client.topology;
        console.log('MongoDB topology:', topology?.description); // Should be "ReplicaSetWithPrimary"

        return connection;
      },
    };
  }
}
