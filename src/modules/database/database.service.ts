import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class DatabaseService {
  constructor(private readonly configService: ConfigService) {}

  private buildMongoUri(dbName: string): string {
    let baseUrl = this.configService.get<string>('DB_URL')?.trim() || '';
    const prefix = this.configService.get<string>('PREFIX_DB') || '';

    if (baseUrl.endsWith('/')) {
      baseUrl = baseUrl.slice(0, -1);
    }

    const fullDbName = `${prefix}${dbName}`;
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const params = new URLSearchParams({ authSource: 'admin' });

    return `${baseUrl}/${fullDbName}?${params.toString()}`;
  }

  async createDbUrl(): Promise<string> {
    const dbName = this.configService.get<string>('MAIN_DB') || '';
    return this.buildMongoUri(dbName);
  }

  async createCommonDbUrl(): Promise<string> {
    const dbName = this.configService.get<string>('COMMON_DB') || '';
    return this.buildMongoUri(dbName);
  }
}
