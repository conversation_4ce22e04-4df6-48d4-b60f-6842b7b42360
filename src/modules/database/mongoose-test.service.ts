import { Inject, Injectable, Scope } from '@nestjs/common';
import {
  MongooseOptionsFactory,
  MongooseModuleOptions,
} from '@nestjs/mongoose';
import { REQUEST } from '@nestjs/core';
import { Request } from '@nestjs/common';
import * as jwt from 'jsonwebtoken';
import { ConfigService } from '@nestjs/config';

const parseCookie = (str) =>
  str
    .split(';')
    .map((v) => v.split('='))
    .reduce((acc, v) => {
      acc[decodeURIComponent(v[0].trim())] = decodeURIComponent(v[1].trim());
      return acc;
    }, {});
@Injectable({ scope: Scope.REQUEST })
export class MongooseConfigServiceTest implements MongooseOptionsFactory {
  constructor(
    @Inject(REQUEST) private readonly request: Request,
    private readonly configService: ConfigService,
  ) {}
  createDbUrl(database): string {
    const db = database;
    const mongoDbUrl = new URL(db, this.configService.get('DB_URL'));
    mongoDbUrl.searchParams.set('authSource', 'admin');
    //}
    return mongoDbUrl.href;
  }

  createMongooseOptions(): MongooseModuleOptions {
    if (
      this.request.headers['cookie'] &&
      this.request.headers['cookie'].access_token
    ) {
      const accessToken: string = parseCookie(
        this.request.headers['cookie'],
      ).access_token;

      const decoded = jwt.decode(accessToken);
      //TODO: get username and pass from MainDb

      return {
        uri: this.createDbUrl('test_tenant_' + decoded['code']),
      };
    } else {
      return {
        uri: this.createDbUrl(undefined),
      };
    }
  }
}
