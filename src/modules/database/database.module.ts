import { Module } from '@nestjs/common';
import { DatabaseService } from './database.service';
import { MongooseConfigService } from './mongoose.service';
import { ConfigModule } from '@nestjs/config';
import { CommonDbConfigService } from './common-db-config.service';

@Module({
  imports: [ConfigModule],
  controllers: [],
  providers: [DatabaseService, MongooseConfigService, CommonDbConfigService],
  exports: [MongooseConfigService, DatabaseService, CommonDbConfigService],
})
export class DatabaseModule {}
