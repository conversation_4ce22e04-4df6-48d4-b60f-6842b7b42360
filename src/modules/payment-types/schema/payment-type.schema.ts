import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { NameDto } from '../../../utils/dto/name.dto';

export type PaymentTypesDocument = HydratedDocument<PaymentTypes>;

@Schema({ timestamps: true })
export class PaymentTypes {
  @Prop({ type: Number, required: true })
  id: number;

  @Prop({ type: NameDto, required: true })
  name: NameDto;

  @Prop({ type: String, required: true, unique: true })
  code: string;
}

export const paymentTypeSchema = SchemaFactory.createForClass(PaymentTypes);
