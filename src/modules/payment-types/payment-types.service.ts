import { HttpException, Injectable, Scope } from '@nestjs/common';
import { CreatePaymentTypeDto } from './dto/create-payment-type.dto';
import { InjectModel } from '@nestjs/mongoose';
import { PaymentTypes } from './schema/payment-type.schema';
import { Model, Types } from 'mongoose';
import {
  GetPaymentTypePaginationDto,
  GetPaymentTypesResponseDto,
} from './dto/get-payment-type.dto';
import { paginate } from '../../utils/dto/pagination.dto';
import { RequestWithUser } from '../auth/interfaces/authorize.interface';
import { usersExceptionCode } from '../../exceptions/exception-code.users';
import { nameOf } from '../../utils/object-key-name';

@Injectable({ scope: Scope.REQUEST, durable: true })
export class PaymentTypesService {
  constructor(
    @InjectModel(PaymentTypes.name)
    private readonly paymentType: Model<PaymentTypes>,
  ) {}
  async create(
    request: RequestWithUser,
    createPaymentTypeDto: CreatePaymentTypeDto,
  ): Promise<PaymentTypes> {
    createPaymentTypeDto.code = String(+createPaymentTypeDto.code);
    await this.checkCode(createPaymentTypeDto.code);
    return new this.paymentType(createPaymentTypeDto).save();
  }

  async createBulk(createPaymentTypeDto: CreatePaymentTypeDto[]) {
    const bulkOps = createPaymentTypeDto.map((dto) => ({
      updateOne: {
        filter: { id: dto.id },
        update: { $set: { ...dto } },
        upsert: true,
      },
    }));

    return this.paymentType.bulkWrite(bulkOps);
  }

  async findAll(
    request: RequestWithUser,
    getPaymentTypePaginationDto: GetPaymentTypePaginationDto,
  ): Promise<GetPaymentTypesResponseDto> {
    const { queries, page, limit, ...rest } = getPaymentTypePaginationDto;

    const search: any = { ...rest };

    if (queries) {
      const searchfield = { $regex: new RegExp(queries), $options: 'i' };
      search.$or = [{ 'name.en': searchfield }, { 'name.ar': searchfield }];
    }
    const result = await this.paymentType.find(search);
    const meta = await paginate(limit, page, this.paymentType, search);
    return { result, meta };
  }

  async abstract(request: RequestWithUser, query) {
    const result = await this.paymentType
      .find(query)
      .select('id name type code commission_account payment_account')
      .lean();

    return {
      result: result.map((payment) => ({
        ...payment,
        id: payment.id.toString(),
      })),
    };
  }

  async findOne(query: {
    id: string;
    //comes from url
    // eslint-disable-next-line @typescript-eslint/naming-convention
    only_receiving?: boolean;
  }): Promise<PaymentTypes> {
    //comes from url
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const { id, only_receiving } = query;
    const searchQuery = {} as any;
    searchQuery.id = id;
    if (typeof only_receiving === 'boolean') {
      searchQuery.only_receiving = only_receiving;
    }
    const rs = await this.paymentType.findOne(searchQuery, null, {
      lean: true,
    });

    return rs;
  }

  async findOnePayment(id: string): Promise<PaymentTypes> {
    return await this.paymentType.findOne({ id });
  }
  async findOnePaymentCode(code: string): Promise<PaymentTypes> {
    return await this.paymentType.findOne({ code });
  }

  async remove(ids: number[]) {
    const result = await this.paymentType.deleteMany({
      id: { $nin: ids },
    });
    return result;
  }

  private async checkCode(code: string, excludeId?: Types.ObjectId | string) {
    const query = { code: code };
    if (excludeId) {
      query['_id'] = { $ne: excludeId };
    }

    const codeExists = await this.paymentType.findOne(query).exec();

    if (codeExists) {
      throw new HttpException(
        nameOf(usersExceptionCode, (exception) => exception.numberDuplicated),
        usersExceptionCode.numberDuplicated,
      );
    }
  }
}
