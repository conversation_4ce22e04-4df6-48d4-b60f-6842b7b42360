import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  UseGuards,
  Req,
} from '@nestjs/common';
import { PaymentTypesService } from './payment-types.service';
import { CreatePaymentTypeDto } from './dto/create-payment-type.dto';
import {
  AbstractDto,
  GetPaymentTypePaginationDto,
} from './dto/get-payment-type.dto';
import { ApiBearerAuth, ApiBody, ApiHeader, ApiTags } from '@nestjs/swagger';
import { CaslGuard } from '../casl/guards/casl.guard';
import { Abilities } from '../casl/guards/abilities.decorator';
import { RequestWithUser } from '../auth/interfaces/authorize.interface';
import { authType } from '../auth/guards/auth-type.decorator';
import { DeleteExceptIdsDto } from '../../utils/dto/delete-except-ids.dto';

@ApiTags('payment-type')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@Controller('users/payment-types')
export class PaymentTypesController {
  constructor(private readonly paymentTypesService: PaymentTypesService) {}

  @UseGuards(CaslGuard)
  @Abilities({ action: 'create', subject: 'payment_type' })
  @Post()
  @authType('both')
  @ApiBearerAuth('TOKEN')
  @ApiBody({ type: [CreatePaymentTypeDto] })
  create(@Body() createPaymentTypeDto: CreatePaymentTypeDto[]) {
    return this.paymentTypesService.createBulk(createPaymentTypeDto);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'read', subject: 'payment_type' })
  @Get()
  @authType('both')
  @ApiBearerAuth('TOKEN')
  findAll(
    @Req() request: RequestWithUser,
    @Query() getPaymentTypePaginationDto: GetPaymentTypePaginationDto,
  ) {
    return this.paymentTypesService.findAll(
      request,
      getPaymentTypePaginationDto,
    );
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'abstract_payment_types' })
  @Get('abstract')
  async abstract(
    @Req() request: RequestWithUser,
    @Query() getPaymentTypeAbstractDto: AbstractDto,
  ) {
    return await this.paymentTypesService.abstract(
      request,
      getPaymentTypeAbstractDto,
    );
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'read', subject: 'payment_type' })
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.paymentTypesService.findOne({
      id: id,
    });
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'delete', subject: 'payment_type' })
  @authType('apiKey')
  @ApiBearerAuth('TOKEN')
  @Delete()
  remove(@Body() dto: DeleteExceptIdsDto) {
    return this.paymentTypesService.remove(dto.ids);
  }
}
