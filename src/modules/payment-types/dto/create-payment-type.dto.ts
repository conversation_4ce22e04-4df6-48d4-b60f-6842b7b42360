import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString, Validate } from 'class-validator';
import {
  NameDto,
  validateEnLanguage,
  validateLanguage,
} from '../../../utils/dto/name.dto';
import { Transform } from 'class-transformer';
import { toLowerCaseObjectKeys } from '../../../utils/tolowerCaseObjectKeys';
export class CreatePaymentTypeDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  id: number;

  @ApiProperty({ type: NameDto })
  @Transform((param) => toLowerCaseObjectKeys(param.value))
  @Validate(validateLanguage)
  @Validate(validateEnLanguage)
  name: NameDto;

  @ApiProperty({ type: String, example: '01' })
  @IsString()
  code: string;
}
