import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { PaginationMetaType } from 'src/utils/dto/pagination.dto';
import { Type } from 'class-transformer';
import { PaymentTypes } from '../schema/payment-type.schema';
export class GetPaymentTypeDto {
  @ApiProperty({ type: String, example: '01', required: false })
  @IsString()
  @IsOptional()
  code: string;
}

export class GetPaymentTypePaginationDto extends GetPaymentTypeDto {
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  public page?: number;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  public limit?: number;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  queries?: string;
}

export class GetPaymentTypesResponseDto {
  @ApiProperty()
  result: PaymentTypes[];

  @ApiProperty()
  meta: PaginationMetaType;
}

export class AbstractDto {}
