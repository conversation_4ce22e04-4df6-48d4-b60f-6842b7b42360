import { HttpException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { extend } from 'lodash';
import { Model, Types } from 'mongoose';
import { nameOf } from '../../utils/object-key-name';
import { CreateSalesRepresentativeDto } from './dto/create-sales-representative.dto';
import { UpdateSalesRepresentativeDto } from './dto/update-sales-representative.dto';
import { SalesRepresentative } from './schema/sales-representative.schema';
import { FindAllSalesRepresentativeDto } from './dto/find-all-sales-representative.dto';
import { RequestWithUser } from '../auth/interfaces/authorize.interface';
import { paginate } from '../../utils/dto/pagination.dto';
import { usersExceptionCode } from '../../exceptions/exception-code.users';

@Injectable()
export class SalesRepresentativeService {
  constructor(
    @InjectModel(SalesRepresentative.name)
    private readonly salesRepresentativeModel: Model<SalesRepresentative>,
  ) {}

  async create(
    req: RequestWithUser,
    createSalesRepresentativeDto: CreateSalesRepresentativeDto,
  ) {
    if (typeof createSalesRepresentativeDto.code == 'string') {
      await this.checkCode(createSalesRepresentativeDto.code);
    }

    return await new this.salesRepresentativeModel(
      createSalesRepresentativeDto,
    ).save();
  }

  async createBulk(
    createSalesRepresentativeDto: CreateSalesRepresentativeDto[],
  ) {
    const bulkOps = createSalesRepresentativeDto.map((dto) => ({
      updateOne: {
        filter: { id: dto.id },
        update: { $set: { ...dto } },
        upsert: true,
      },
    }));

    return this.salesRepresentativeModel.bulkWrite(bulkOps);
  }

  async findAll(findAllSalesRepresentativeDto: FindAllSalesRepresentativeDto) {
    const { limit, page, queries, sortBy, sortType, ...rest } =
      findAllSalesRepresentativeDto;
    const sort = { [sortBy]: sortType };
    let search = { ...rest };
    if (queries) {
      const searchfield = { $regex: new RegExp(queries), $options: 'i' };
      search = {
        ...search,
        $or: [{ name: searchfield }, { code: searchfield }],
      } as any;
    }
    let result = null;
    result = await this.salesRepresentativeModel
      .find(search, null, { sort })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean();
    const meta = await paginate(
      limit,
      page,
      this.salesRepresentativeModel,
      queries,
    );

    return { meta, result };
  }

  async abstract() {
    const result = await this.salesRepresentativeModel
      .find({ active: true })
      .select('id name number popular_name')
      .lean();
    return { result: result };
  }

  async findOne(_id: Types.ObjectId | string) {
    return await this.salesRepresentativeModel.findOne({ _id }).exec();
  }

  async update(
    _id: Types.ObjectId | string,
    updateSalesRepresentativeDto: UpdateSalesRepresentativeDto,
  ) {
    const salesRepresentative = await this.findOne(_id);

    if (!salesRepresentative) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.salesRepresentativeNotFound,
        ),
        usersExceptionCode.salesRepresentativeNotFound,
      );
    }
    if (typeof updateSalesRepresentativeDto.code == 'string') {
      await this.checkCode(
        updateSalesRepresentativeDto.code,
        salesRepresentative._id,
      );
    }
    extend(salesRepresentative, updateSalesRepresentativeDto);
    return await salesRepresentative.save();
  }

  async remove(ids: number[]) {
    const result = await this.salesRepresentativeModel.deleteMany({
      id: { $nin: ids },
    });
    return result;
  }

  private async checkCode(code: string, excludeId?: Types.ObjectId | string) {
    const query = { code };
    if (excludeId) {
      query['_id'] = { $ne: excludeId };
    }

    const codeExists = await this.salesRepresentativeModel
      .findOne(query)
      .exec();

    if (codeExists) {
      throw new HttpException(
        nameOf(usersExceptionCode, (exception) => exception.numberDuplicated),
        usersExceptionCode.numberDuplicated,
      );
    }
  }
}
