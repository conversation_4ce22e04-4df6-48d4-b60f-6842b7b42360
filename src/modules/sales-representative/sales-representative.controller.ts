import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, Api<PERSON>ody, ApiHeader, ApiTags } from '@nestjs/swagger';
import { CreateSalesRepresentativeDto } from './dto/create-sales-representative.dto';
import { UpdateSalesRepresentativeDto } from './dto/update-sales-representative.dto';
import { SalesRepresentativeService } from './sales-representative.service';
import { CaslGuard } from '../casl/guards/casl.guard';
import { FindAllSalesRepresentativeDto } from './dto/find-all-sales-representative.dto';
import { Abilities } from '../casl/guards/abilities.decorator';
import { authType } from '../auth/guards/auth-type.decorator';
import { DeleteExceptIdsDto } from '../../utils/dto/delete-except-ids.dto';

@ApiTags('sales-representative')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@Controller('trade/sales-representative')
export class SalesRepresentativeController {
  constructor(
    private readonly salesRepresentativeService: SalesRepresentativeService,
  ) {}

  @UseGuards(CaslGuard)
  @Abilities({ action: 'create', subject: 'sales_representative' })
  @Post()
  @authType('both')
  @ApiBearerAuth('TOKEN')
  @ApiBody({ type: [CreateSalesRepresentativeDto] })
  create(@Body() createSalesRepresentativeDto: CreateSalesRepresentativeDto[]) {
    return this.salesRepresentativeService.createBulk(
      createSalesRepresentativeDto,
    );
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'sales_representative' })
  @Get()
  @authType('apiKey')
  @ApiBearerAuth('TOKEN')
  findAll(
    @Query() findAllSalesRepresentativeDto: FindAllSalesRepresentativeDto,
  ) {
    return this.salesRepresentativeService.findAll(
      findAllSalesRepresentativeDto,
    );
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'abstract_sales_representative' })
  @Get('/abstract')
  abstract() {
    return this.salesRepresentativeService.abstract();
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'read', subject: 'sales_representative' })
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.salesRepresentativeService.findOne(id);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'edit', subject: 'sales_representative' })
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateSalesRepresentativeDto: UpdateSalesRepresentativeDto,
  ) {
    return this.salesRepresentativeService.update(
      id,
      updateSalesRepresentativeDto,
    );
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'delete', subject: 'sales_representative' })
  @authType('apiKey')
  @ApiBearerAuth('TOKEN')
  @Delete()
  remove(@Body() dto: DeleteExceptIdsDto) {
    return this.salesRepresentativeService.remove(dto.ids);
  }
}
