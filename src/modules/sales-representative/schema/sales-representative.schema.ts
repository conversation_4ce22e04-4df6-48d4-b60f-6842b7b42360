import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';

@Schema({ timestamps: true })
export class SalesRepresentative {
  @Prop({ type: Number, required: true, unique: true })
  id: number;

  @Prop({ type: String, required: true, unique: true })
  code: string;

  @Prop()
  name: string;

  @Prop()
  popular_name: string;

  @Prop({ type: Boolean })
  active: boolean;
}

export const salesRepresentativeSchema =
  SchemaFactory.createForClass(SalesRepresentative);
