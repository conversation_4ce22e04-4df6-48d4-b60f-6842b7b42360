import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class CreateSalesRepresentativeDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Sales Representative number',
    example: '1',
    required: true,
  })
  @IsString()
  code: string;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  popular_name: string;

  @ApiProperty()
  @IsBoolean()
  active: boolean;
}
