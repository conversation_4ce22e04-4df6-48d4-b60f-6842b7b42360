import { Module } from '@nestjs/common';
import { SalesRepresentativeService } from './sales-representative.service';
import { SalesRepresentativeController } from './sales-representative.controller';
import { MongooseModule } from '@nestjs/mongoose';
import {
  SalesRepresentative,
  salesRepresentativeSchema,
} from './schema/sales-representative.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: SalesRepresentative.name, schema: salesRepresentativeSchema },
    ]),
  ],
  controllers: [SalesRepresentativeController],
  providers: [SalesRepresentativeService],
})
export class SalesRepresentativeModule {}
