import { Inject, Injectable } from '@nestjs/common';
import { BufferedFile } from './file.model';
import * as crypto from 'crypto';
import { Duplex } from 'stream';
import { Client } from 'minio';

@Injectable()
export class MinioClientService {
  constructor(@Inject('MINIO') private readonly minioClient: Client) {}

  public get client() {
    return this.minioClient;
  }

  public async upload(
    file: BufferedFile,
    baseBucket: string,
  ): Promise<Record<'name', string>> {
    const ext = file.originalname.substring(
      file.originalname.lastIndexOf('.'),
      file.originalname.length,
    );

    const name = file.originalname.replace(/\.[^/.]+$/, '');
    const fileName = new Date().toISOString() + '_' + name + ext;
    const fileBuffer = file.buffer;
    await this.minioClient.putObject(baseBucket, fileName, fileBuffer);

    return {
      name: `${fileName}`,
    };
  }

  async createName(file): Promise<string> {
    const tempFilename = Date.now().toString();
    const hashedFileName = crypto
      .createHash('md5')
      .update(tempFilename)
      .digest('hex');
    const ext = file.originalname.substring(
      file.originalname.lastIndexOf('.'),
      file.originalname.length,
    );
    const filename = hashedFileName + ext;

    return `${filename}`;
  }

  public async getFile(fileName: string, baseBucket: string): Promise<string> {
    return this.client.presignedGetObject(baseBucket, fileName, 24 * 60 * 60);
  }

  async removeFile(bucketName, objectName): Promise<void> {
    return await this.client.removeObject(bucketName, objectName);
  }

  async putObject(
    bucketName: string,
    fileName: string,
    stream: Duplex,
  ): Promise<any> {
    return await this.client.putObject(bucketName, fileName, stream);
  }
}
