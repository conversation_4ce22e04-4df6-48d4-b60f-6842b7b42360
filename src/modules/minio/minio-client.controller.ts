import {
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBody,
  ApiConsumes,
  ApiCreatedResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';

import { FileInterceptor } from '@nestjs/platform-express';
import { BufferedFile } from './file.model';
import { MinioClientService } from './minio-client.service';
import {
  MinioDto,
  ResponseMinioGetFileDto,
  ResponseUploadFileDto,
  sectorEnum,
} from './minio-client.dto';
import { extname } from 'path';
import { CaslGuard } from '../casl/guards/casl.guard';

@ApiTags('minio')
@UseGuards(CaslGuard)
@Controller('minio')
export class MinioClientController {
  constructor(private readonly minioClientService: MinioClientService) {}

  @Post('/file')
  @ApiOperation({ summary: 'Upload file' })
  @ApiCreatedResponse({ type: ResponseUploadFileDto })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(
    FileInterceptor('file', {
      limits: {
        files: 1,
        fileSize: 20 * 1000 * 1000, // 20 mb in bytes
      },
      fileFilter: (req: any, file: any, cb: any) => {
        if (
          file.mimetype.match(`pdf.*|image.*|excel.*|sheet|word.*`) &&
          file.originalname.match(/\.(pdf|jpg|jpeg|png|xlsx|xls|docx)$/g)
        ) {
          // Allow storage of file
          cb(null, true);
        } else {
          // Reject file
          cb(
            new HttpException(
              `Unsupported file type ${extname(file.originalname)}`,
              HttpStatus.BAD_REQUEST,
            ),
            false,
          );
        }
      },
    }),
  )
  async uploadFile(
    @Query() minioDto: MinioDto,
    @UploadedFile() file: BufferedFile,
  ) {
    const { sector } = minioDto;
    const bucket = await this.getBucketName(sector);
    const upload = await this.minioClientService.upload(file, bucket);
    return {
      success: true,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      fileName: upload.name,
    };
  }

  @Get('/file/:fileName')
  @ApiOperation({ summary: 'Get file by its name.' })
  @ApiCreatedResponse({ type: ResponseMinioGetFileDto })
  async getFile(
    @Param('fileName') fileName: string,
    @Query() minioDto: MinioDto,
  ) {
    const { sector } = minioDto;
    const bucket = await this.getBucketName(sector);

    const filePath = await this.minioClientService.getFile(fileName, bucket);

    return {
      name: fileName,
      url: filePath,
    };
  }

  @Delete('/file/:fileName')
  @ApiOperation({ summary: 'Delete file by fileName.' })
  async removeFile(
    @Param('fileName') fileName: string,
    @Query() minioDto: MinioDto,
  ) {
    const { sector } = minioDto;
    const bucket = await this.getBucketName(sector);

    return await this.minioClientService.removeFile(bucket, fileName);
  }

  async getBucketName(sector) {
    let bucket = null;
    if (sector === sectorEnum.logo) {
      bucket = process.env.S3_BUCKET_LOGO;
    }

    return bucket;
  }
}
