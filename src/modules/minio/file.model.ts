export interface BufferedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: AppMimeType;
  size: number;
  buffer: Buffer;
}

export interface StoredFile extends HasFile, StoredFileMetadata {}

export interface HasFile {
  file: Buffer | string;
}

export interface StoredFileMetadata {
  id: string;
  name: string;
  encoding: string;
  size: number;
  updatedAt: Date;
  fileSrc?: string;
}

export type AppMimeType = 'image/png' | 'image/jpeg' | 'xls';

export enum appMimeTypeTset {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'image/png',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'image/jpeg',
  'xls',
}
