import { IsEnum, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum sectorEnum {
  logo = 'logo',
}

export class MinioDto {
  @IsNotEmpty()
  @ApiProperty({ enum: sectorEnum })
  @IsEnum(sectorEnum)
  readonly sector: string;
}

export class ResponseUploadFileDto {
  @IsNotEmpty()
  @ApiProperty()
  readonly success: boolean;

  @IsNotEmpty()
  @ApiProperty()
  // eslint-disable-next-line @typescript-eslint/naming-convention
  readonly fileName: string;
}

export class ResponseMinioGetFileDto {
  @IsNotEmpty()
  @ApiProperty()
  readonly name: string;

  @IsNotEmpty()
  @ApiProperty()
  readonly url: string;
}

export class ResponseMinioBlobGetFileDto {
  @IsNotEmpty()
  @ApiProperty()
  readonly file: any;
}
