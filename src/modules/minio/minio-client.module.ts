import { Module } from '@nestjs/common';
import { MinioClientService } from './minio-client.service';
import * as Minio from 'minio';
import { MinioClientController } from './minio-client.controller';
import { ConfigService } from '@nestjs/config';

@Module({
  imports: [],
  providers: [
    {
      provide: 'MINIO',
      inject: [ConfigService],
      useFactory: (configService: ConfigService) =>
        new Minio.Client({
          // eslint-disable-next-line @typescript-eslint/naming-convention
          endPoint: configService.get('S3_ENDPOINT'),
          port: +configService.get('S3_PORT'),
          // eslint-disable-next-line @typescript-eslint/naming-convention
          useSSL:
            JSON.parse(configService.get('S3_SSL') || null) === false
              ? false
              : true,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          accessKey: configService.get('S3_ACCESS_KEY'),
          // eslint-disable-next-line @typescript-eslint/naming-convention
          secretKey: configService.get('S3_SECRET_KEY'),
        }),
    },
    MinioClientService,
  ],
  controllers: [MinioClientController],
  exports: [MinioClientService],
})
export class MinioClientModule {}
