import { Ability, AbilityBuilder, AbilityClass } from '@casl/ability';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { User } from '../users/schemas/user.schema';
import { Model, Types } from 'mongoose';
import { UserBranchesRolesService } from '../user-branches-roles/user-branches-roles.service';

export enum action {
  create = 'create',
  read = 'read',
  update = 'update',
  delete = 'delete',
}

type Subjects = 'all';

export type AppAbility = Ability<[action, Subjects]>;

@Injectable({})
export class CaslAbilityFactory {
  constructor(
    @InjectModel(User.name)
    private readonly userModel: Model<User>,
    private readonly userBranchesRolesService: UserBranchesRolesService,
  ) {}

  async createForUser(
    user_id: Types.ObjectId,
    branch: Types.ObjectId | string,
  ): Promise<Ability<[action, Subjects]>> {
    const { can, build } = new AbilityBuilder<Ability<[action, Subjects]>>(
      Ability as AbilityClass<AppAbility>,
    );
    const user = await this.userModel.findById(user_id).exec();

    const permissions = await this.userBranchesRolesService.getUserPermissions(
      user._id,
      branch,
    );

    permissions.map((permission) => {
      can(permission.action, permission.subject);
    });

    return build();
  }
}
