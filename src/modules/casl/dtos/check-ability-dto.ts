import { Types } from 'mongoose';
import { IsArray, IsNotEmpty } from 'class-validator';
import { action } from '../casl-ability.factory';
import { InferSubjects } from '@casl/ability/dist/types';
import { CodeValidatorDto } from '../../../utils/dto/code.dto';
import { Type } from 'class-transformer';
export class CheckAbilityDto {
  user_id: Types.ObjectId;

  branch: Types.ObjectId | string;

  @IsArray()
  permissions: Array<{ action: action; subject: InferSubjects<any> }>;
}

export class CaslDto extends CodeValidatorDto {
  @Type(() => CheckAbilityDto)
  @IsNotEmpty()
  data: CheckAbilityDto;
}
