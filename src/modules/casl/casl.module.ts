import { forwardRef, Global, Module } from '@nestjs/common';
import { UsersModule } from '../users/users.module';
import { CaslAbilityFactory } from './casl-ability.factory';
import { CaslGuard } from './guards/casl.guard';
import { UserBranchesRolesModule } from '../user-branches-roles/user-branch-role.module';
import { MongooseModule } from '@nestjs/mongoose';
import { User, User_Schema } from '../users/schemas/user.schema';
import { CaslService } from './casl.service';
import { CaslController } from './casl.controller';

@Global()
@Module({
  imports: [
    MongooseModule.forFeature([{ name: User.name, schema: User_Schema }]),
    forwardRef(() => UsersModule),
    UserBranchesRolesModule,
  ],
  providers: [CaslGuard, CaslAbilityFactory, CaslService],
  controllers: [CaslController],
  exports: [CaslAbilityFactory],
})
export class CaslModule {}
