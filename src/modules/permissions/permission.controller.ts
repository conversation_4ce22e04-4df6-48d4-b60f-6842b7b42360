import { Controller, Get, UseGuards } from '@nestjs/common';
import { PermissionService } from './permission.service';
import { ApiHeader, ApiTags } from '@nestjs/swagger';
import { Abilities } from '../casl/guards/abilities.decorator';
import { CaslGuard } from '../casl/guards/casl.guard';

@ApiTags('permissions')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  example: '64956ef5f506e9edf21df4e8',
  required: true,
})
@Controller('users/permissions')
export class PermissionController {
  constructor(private readonly permissionService: PermissionService) {}
  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'permissions' })
  @Get()
  async get() {
    return await this.permissionService.getNames();
  }
}
