import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { IsString } from 'class-validator';
import { Type } from 'class-transformer';

export class PermissionsDto {
  @IsString()
  action: string;

  @IsString()
  subject: string;
}

@Schema({ timestamps: true })
export class Permission {
  @Prop({
    required: true,
    type: String,
  })
  name: string;

  @Prop({
    required: true,
  })
  @Type(() => PermissionsDto)
  privileges: PermissionsDto[];
}

export const permissionSchema = SchemaFactory.createForClass(Permission);
