import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Permission } from './schema/permisstions.schema';

@Injectable()
export class PermissionService {
  constructor(
    @InjectModel(Permission.name)
    private readonly permissionModel: Model<Permission>,
  ) {}

  public async findAll() {
    const permissionsData = await this.permissionModel
      .find()
      .select('_id privileges')
      .exec();
    const flattenedPermissions = permissionsData.reduce(
      (acc, permissionObj) => {
        return acc.concat(permissionObj.privileges);
      },
      [],
    );

    const actions = [
      ...new Set(flattenedPermissions.map((permission) => permission.action)),
    ] as const;
    const subjects = [
      ...new Set(flattenedPermissions.map((permission) => permission.subject)),
    ] as const;

    return { actions, subjects };
  }

  public async getNames() {
    return await this.permissionModel.find().select('_id name').exec();
  }
}
