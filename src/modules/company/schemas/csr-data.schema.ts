import { Prop, Schema } from '@nestjs/mongoose';
@Schema({ _id: false })
export class CompanyCsrData {
  @Prop({ type: String, required: true })
  common_name: string;

  @Prop({ type: String, required: true })
  organization_identifier: string;

  @Prop({ type: String, required: true })
  organization_name: string;

  @Prop({ type: String, required: true })
  location: string;

  @Prop({ type: String, required: true })
  egs_serial: string;

  @Prop({ type: String, required: true })
  organization_unit_name: string;

  @Prop({ type: String, required: true })
  invoice_type: string;

  @Prop({ type: String, required: true })
  industry: string;

  @Prop({ type: String, required: true })
  email_address: string;

  @Prop({ type: String, required: true })
  country_name: string;

  @Prop({ type: String, required: true })
  state: string;

  @Prop({ type: String, required: true })
  locality_name: string;
}
