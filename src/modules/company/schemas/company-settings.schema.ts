import { Prop, Schema } from '@nestjs/mongoose';

export enum level {
  store = 'store',
  branch = 'branch',
  company = 'company',
}

export enum pricingLevel {
  branch = 'branch',
  company = 'company',
}

export enum documentPolicy {
  adjustment = 'adjustment',
  override = 'override',
}

export enum documentPolicyStatus {
  enable = 'enable',
  disable = 'disable',
}

@Schema({ _id: false })
export class CompanySettings {
  @Prop({ type: Boolean, default: true })
  isMultiBranch: boolean;

  @Prop({ required: true, enum: pricingLevel, default: pricingLevel.company })
  pricing_level: pricingLevel;

  @Prop({ required: true, enum: level, default: level.company })
  average_cost_level: level;

  @Prop({ type: Number, default: 2 })
  depth: number;

  @Prop({ type: Number, default: 2 })
  displayed_decimals: number;

  @Prop({
    required: true,
    enum: documentPolicy,
    default: documentPolicy.adjustment,
  })
  document_policy: documentPolicy;

  @Prop({
    required: true,
    enum: documentPolicyStatus,
    default: documentPolicyStatus.enable,
  })
  document_policy_status: documentPolicyStatus;
}
