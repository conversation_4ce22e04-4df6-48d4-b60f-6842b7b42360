import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { CompanySettings } from './company-settings.schema';
import { ContactPerson } from './contant-person.schema';
import { NameDto } from '../../../utils/dto/name.dto';
import { CompanyCsr } from './csr.schema';
import { CompanyCsrData } from './csr-data.schema';
import { NationalAddress } from './national-address.schema';

export type CompanyDocument = Company & Document;

@Schema({ timestamps: true })
export class Company {
  @Prop({ type: NameDto, required: true })
  name: NameDto;

  @Prop({ type: String, default: '' })
  short_name: string;

  @Prop({ type: String, default: '' })
  logo: string;

  @Prop({ default: '' })
  website: string;

  @Prop({ type: ContactPerson, default: () => ({}) })
  contact_person: Contact<PERSON>erson;

  @Prop({ type: NationalAddress, default: () => ({}) })
  national_address: NationalAddress;

  @Prop({ type: CompanySettings, default: () => ({}) })
  settings: CompanySettings;

  @Prop({ type: String, select: false })
  public_key: string;

  @Prop({ type: String, select: false })
  private_key: string;

  @Prop({ type: CompanyCsr })
  csr_data: CompanyCsr;

  @Prop({ type: CompanyCsrData })
  csr_onboarding_data: CompanyCsrData;
}

export const companySchema = SchemaFactory.createForClass(Company);
