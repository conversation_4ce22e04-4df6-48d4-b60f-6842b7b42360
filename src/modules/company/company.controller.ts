import {
  Controller,
  Get,
  Body,
  Patch,
  Post,
  Req,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { CompanyService } from './company.service';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { ApiHeader, ApiTags } from '@nestjs/swagger';
import { RequestWithUser } from '../auth/interfaces/authorize.interface';
import { CreateCompanyDto } from './dto/create-company.dto';
import { CaslGuard } from '../casl/guards/casl.guard';
import { Abilities } from '../casl/guards/abilities.decorator';
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  example: '64956ef5f506e9edf21df4e8',
  required: true,
})
@ApiTags('company-info')
@Controller('users/company-info')
export class CompanyController {
  branchService: any;

  constructor(private readonly companyService: CompanyService) {}

  @UseGuards(CaslGuard)
  @Post()
  create(@Body() body: CreateCompanyDto) {
    return this.companyService.create(body);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'read', subject: 'company_information' })
  @Get()
  get() {
    return this.companyService.get();
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'edit', subject: 'company_information' })
  @Patch()
  update(@Body() body: UpdateCompanyDto, @Req() req: RequestWithUser) {
    return this.companyService.update(req?.user?.code, body);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'edit', subject: 'company_information' })
  @Delete('delete-csr')
  deleteCsr() {
    return this.companyService.deleteCsr();
  }
}
