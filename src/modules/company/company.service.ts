import { HttpException, Injectable } from '@nestjs/common';
import { extend } from 'lodash';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model } from 'mongoose';
import { Company } from './schemas/company.schema';
import { nameOf } from '../../utils/object-key-name';
import { usersExceptionCode } from '../../exceptions/exception-code.users';
import { HttpStatusCode } from 'axios';
import { CreateCompanyDto } from './dto/create-company.dto';

@Injectable()
export class CompanyService {
  constructor(
    @InjectModel(Company.name)
    private readonly companyModel: Model<Company>,
  ) {}

  async create(data: CreateCompanyDto) {
    const company = await this.companyModel.findOne();
    if (company) {
      throw new HttpException('company exist', HttpStatusCode.Forbidden);
    }
    return await this.companyModel.create(data);
  }

  async get(): Promise<
    Company & {
      _id: mongoose.Types.ObjectId;
    }
  > {
    const one = await this.companyModel.findOne();
    if (one === null) {
      // create One
      try {
        const rs = await new this.companyModel({
          name: 'Untitled',
        }).save();
        return rs;
      } catch (error) {
        console.error('failed to create the company info', error);
        throw error;
      }
    }
    return one;
  }

  async update(
    code: number,
    query: UpdateCompanyDto,
  ): Promise<
    Company & {
      _id: mongoose.Types.ObjectId;
    }
  > {
    const company = await this.companyModel.findOne();
    if (!company) {
      throw new HttpException(
        nameOf(usersExceptionCode, (x) => x.companyNotFound),
        usersExceptionCode.companyNotFound,
      );
    }
    extend(company, query);
    await company.save();
    const companyDate = await this.companyModel.findOne().lean();
    return companyDate;
  }

  async getCompanySettings() {
    const companySettings = (await this.companyModel.findOne())?.settings;
    return companySettings;
  }

  async getCompanyDataCsr() {
    return await this.companyModel
      .findOne()
      .select('public_key private_key country city name email csr_data');
  }

  async getCompanyDataCsId() {
    const rs = await this.companyModel
      .findOne()
      .select(
        'public_key private_key country city name email csr_onboarding_data csr_data national_address',
      );

    return rs;
  }

  async onBoardingStatus() {
    const rs = await this.companyModel.findOne().select('csr_data');
    return {
      csid_onboard:
        !!rs?.csr_data?.requestID &&
        !!rs?.csr_data?.binarySecurityToken &&
        !!rs.csr_data.secret,
    };
  }

  async deleteCsr() {
    const rs = await this.companyModel.findOneAndUpdate(
      {},
      {
        $unset: {
          private_key: '',
          public_key: '',
          csr_data: '',
          csr_onboarding_data: '',
        },
      },
      { new: true },
    );
    return rs;
  }
}
