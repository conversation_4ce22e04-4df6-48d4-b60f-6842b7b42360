import {
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
  IsObject,
  Validate,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import {
  NameDto,
  validateLanguage,
  validateEnLanguage,
} from '../../../utils/dto/name.dto';
import { ContactPersonDto } from './contant-person.dto';
import { NationalAddressDto } from './national-address.dto';
import { CompanySettingsDto } from './company-settings.dto';

export class CreateCompanyDto {
  @ApiProperty({ type: NameDto })
  @Validate(validateLanguage)
  @Validate(validateEnLanguage)
  name: NameDto;

  @IsString()
  @IsOptional()
  short_name: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  website?: string;

  @ApiProperty({ type: ContactPersonDto })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => ContactPersonDto)
  contact_person?: ContactPersonDto;

  @ApiProperty({ type: NationalAddressDto })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => NationalAddressDto)
  national_address?: NationalAddressDto;

  @ApiProperty({ type: CompanySettingsDto })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => CompanySettingsDto)
  settings: CompanySettingsDto;

  @ApiProperty()
  @IsOptional()
  @IsNotEmpty()
  logo: string;
}
