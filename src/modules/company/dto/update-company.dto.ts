import { IsNotEmpty } from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty, PartialType } from '@nestjs/swagger';
import { Company } from '../schemas/company.schema';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';
import { Types } from 'mongoose';
import { CreateCompanyDto } from './create-company.dto';

export class GetCompanyRespDto {
  @ApiProperty()
  result: Company;
}

export class UpdateCompanyDto extends PartialType(CreateCompanyDto) {}

export class UploadLogo {
  @ApiProperty({
    description: '_id of company',
    example: 'dfg745idsf723hrskdfh39',
    required: true,
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  _id: Types.ObjectId;
}
