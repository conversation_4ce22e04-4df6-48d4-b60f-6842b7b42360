import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsBoolean,
  IsEnum,
  IsInt,
  IsNumber,
  IsOptional,
  Max,
  Min,
} from 'class-validator';
import {
  documentPolicy,
  documentPolicyStatus,
  level,
  pricingLevel,
} from '../schemas/company-settings.schema';

export class CompanySettingsDto {
  @ApiProperty()
  @Transform(({ value }) => value === true)
  @IsBoolean()
  @IsOptional()
  isMultiBranch?: boolean;

  @ApiProperty({
    enum: pricingLevel,
    default: pricingLevel.company,
  })
  @IsEnum(pricingLevel)
  pricing_level: pricingLevel;

  @ApiProperty({
    enum: level,
    default: level.store,
  })
  @IsEnum(level)
  average_cost_level: level;

  @ApiProperty()
  @IsInt()
  depth: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Min(2)
  @Max(6)
  displayed_decimals: number;

  @ApiProperty({
    enum: documentPolicy,
    default: documentPolicy.adjustment,
  })
  @IsEnum(documentPolicy)
  document_policy: documentPolicy;

  @ApiHideProperty()
  document_policy_status: documentPolicyStatus;
}
