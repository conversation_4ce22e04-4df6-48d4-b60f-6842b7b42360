import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Delete,
  Req,
  Query,
  Param,
  Inject,
  UseGuards,
} from '@nestjs/common';
import { UsersService } from './users.service';
import {
  CreateUserDto,
  GetUserPaginationDto,
  UpdateUserDto,
} from './dto/users.dto';
import { RequestWithUser } from '../auth/interfaces/authorize.interface';
import { IdValidatorDto } from '../../utils/dto/general.dto';
import { ApiHeader, ApiTags } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { MongooseConfigService } from '../database/mongoose.service';
import { CaslGuard } from '../casl/guards/casl.guard';
import { Abilities } from '../casl/guards/abilities.decorator';

@ApiTags('user')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  example: '64956ef5f506e9edf21df4e8',
  required: true,
})
@Controller('users/user')
export class UserController {
  constructor(
    private readonly userService: UsersService,
    @Inject(MongooseConfigService)
    private readonly mongooseConfigService: MongooseConfigService,
  ) {}

  @UseGuards(CaslGuard)
  @Abilities({ action: 'create', subject: 'users' })
  @Post()
  async create(
    @Req() request: RequestWithUser,
    @Body() createUserDto: CreateUserDto,
  ) {
    return await this.userService.create(request, createUserDto);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'users' })
  @Get()
  findAll(@Query() query: GetUserPaginationDto) {
    return this.userService.findAll(query);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'read', subject: 'users' })
  @Get(':id')
  findOne(@Param('id') _id: string) {
    return this.userService.findOne({ _id: new Types.ObjectId(_id) }, []);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'edit', subject: 'users' })
  @Patch()
  update(@Req() request: RequestWithUser, @Body() body: UpdateUserDto) {
    return this.userService.update(request, body);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'delete', subject: 'users' })
  @Delete()
  delete(@Query() query: IdValidatorDto) {
    return this.userService.delete(query);
  }
}
