import { PartialType } from '@nestjs/mapped-types';
import { Transform, Type } from 'class-transformer';
import {
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
} from '@nestjs/class-validator';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { ObjectId, Types } from 'mongoose';
import {
  IsArray,
  IsBoolean,
  IsEmail,
  IsEnum,
  IsMongoId,
  IsNumber,
  Min,
  ValidateNested,
} from 'class-validator';
import { allowCustomPrice } from '../schemas/enums';

export class PositionsDto {
  @IsArray()
  @ApiProperty({
    description: 'roles of the user',
    example: ['634ffef6643b5956440637e1'],
    required: true,
  })
  public roles: Types.ObjectId[];

  @IsArray()
  @ApiProperty({
    description: 'branches of the user',
    example: ['634ffef6643b5956440637e1'],
    required: true,
  })
  public branches: Types.ObjectId[];
}

export class setting {
  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  override_profit_margin: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  allow_price_under_cost: boolean;

  @ApiProperty()
  @IsOptional()
  @IsEnum(allowCustomPrice)
  allow_custom_price: allowCustomPrice;
}

export class CreateUserDto {
  @ApiProperty({
    example: 'ruzi or روزيه',
  })
  @IsString()
  @IsNotEmpty()
  public name: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: 'password of the user',
    example: 'a12345679',
    required: true,
  })
  public password: string;

  @IsNotEmpty()
  @IsEmail()
  @Transform(({ value }: { value: string }) => value.toLowerCase())
  @ApiProperty({
    description: 'emial of the user',
    example: '<EMAIL>',
    required: true,
  })
  public email: string;

  @IsArray()
  @ApiProperty({
    description: 'roles and branches as positions of the user',
    example: [
      {
        roles: ['659e9004255ee82bb3610466', '659e9004255ee82bb3610467'],
        branches: ['64956ef5f506e9edf21df4e8', '64956ef5f506e9edf21df4e9'],
      },
    ],
    required: true,
  })
  public positions: PositionsDto[];

  @IsBoolean()
  @IsOptional()
  active: boolean;

  @IsString()
  @IsOptional()
  default_payment_type?: string;

  @IsString()
  @IsOptional()
  default_store?: string;

  @IsNumber()
  @Type(() => Number)
  @Min(0)
  @IsOptional()
  @ApiProperty({
    description: 'default quantity',
    example: 50,
    required: true,
  })
  default_quantity?: number;

  @ApiProperty({ type: setting })
  @IsOptional()
  @ValidateNested()
  @Type(() => setting)
  setting: setting;

  @ApiProperty()
  @IsOptional()
  logo?: string;

  @ApiHideProperty()
  last_activity?: Date;

  @ApiHideProperty()
  active_branch?: Types.ObjectId;
}
export class UpdateUserDto extends PartialType(CreateUserDto) {
  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  public _id: ObjectId;

  @IsOptional()
  @IsEmail()
  @Transform(({ value }: { value: string }) => value.toLowerCase())
  @ApiProperty({
    description: 'emial of the user',
    example: '<EMAIL>',
    required: false,
  })
  public email: string;
}
export class GetUserDto extends PartialType(CreateUserDto) {
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  public _id?: string;
}
export class GetUserPaginationDto extends GetUserDto {
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  public page?: number;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  public limit?: number;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  queries?: string;

  @IsString()
  @IsMongoId()
  @IsOptional()
  public role?: string;
}
