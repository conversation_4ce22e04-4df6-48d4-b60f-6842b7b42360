import { ApiProperty } from '@nestjs/swagger';
import { PaginationMetaType } from 'src/utils/dto/pagination.dto';
import { Role } from '../../roles/schemas/role.schema';
import { Branch } from '../../branch/schemas/branch.schema';

export class ResultDto {
  public name: string;

  public email: string;

  public roles?: Role[];

  public branches?: Branch[];
}

export class GetUserResponseDto {
  @ApiProperty({ isArray: true })
  result: ResultDto;

  @ApiProperty({ isArray: false })
  meta: PaginationMetaType;
}
