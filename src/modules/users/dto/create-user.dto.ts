import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { <PERSON>Array, IsEmail, IsNotEmpty, IsString } from 'class-validator';
import { Types } from 'mongoose';

export class CreateUserDto {
  @ApiProperty({
    example: 'ruzi or روزيه',
  })
  @IsString()
  public name: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: 'password of the user',
    example: 'a12345679',
    required: true,
  })
  public password: string;

  @IsNotEmpty()
  @Transform(({ value }: { value: string }) => value.toLowerCase())
  @IsEmail()
  @ApiProperty({
    description: 'emial of the user',
    example: '<EMAIL>',
    required: true,
  })
  public email: string;

  @IsArray()
  @ApiProperty({
    description: 'roles of the user',
    example: ['634ffef6643b5956440637e1'],
    required: true,
  })
  public roles?: Types.ObjectId[];

  @IsArray()
  @ApiProperty({
    description: 'branches of the user',
    example: ['634ffef6643b5956440637e1'],
    required: true,
  })
  public branches?: Types.ObjectId[];
}
