import { Types } from 'mongoose';
import { CodeValidatorDto } from '../../../utils/dto/code.dto';
import { Transform } from 'class-transformer';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';
import { IsNotEmpty } from 'class-validator';

export class GetUserRpc extends CodeValidatorDto {
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  @IsNotEmpty()
  id: Types.ObjectId;
}
