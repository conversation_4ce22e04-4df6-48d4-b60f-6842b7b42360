import { IsArray, IsNotEmpty } from 'class-validator';
import { CodeValidatorDto } from '../../../utils/dto/code.dto';
import { Types } from 'mongoose';
import { Transform } from 'class-transformer';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';

export class DropUserBranchRpcDto extends CodeValidatorDto {
  @IsArray()
  @IsNotEmpty()
  ids: Types.ObjectId[];
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  @IsNotEmpty()
  branch: Types.ObjectId;
}
