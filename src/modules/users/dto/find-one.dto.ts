import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { Types } from 'mongoose';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';
import { IsOptional } from 'class-validator';

export class userFindOneDto {
  @ApiProperty({
    description: '_id of the user',
    example: 'das4d64df984er8t46d1gdfgr4t64',
    required: false,
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  _id?: Types.ObjectId;

  @ApiProperty({
    description: 'name of the user',
    example: 'ruzi or روزيه',
    required: false,
  })
  name?: string;

  // TODO Why we have password in findone ?
  @ApiProperty({
    description: 'password of the user',
    example: 'das4d64df984er8t46d1gdfgr4t64',
    required: false,
  })
  password?: string;

  @ApiProperty({
    description: 'emial of the user',
    example: '<EMAIL>',
    required: false,
  })
  email?: string;

  @ApiProperty({
    description: ' email_verified of the user',
    example: true,
    required: false,
  })
  email_verified?: boolean;

  @ApiProperty({
    description: 'status  of the user',
    example: false,
    required: false,
  })
  active?: boolean;
}
