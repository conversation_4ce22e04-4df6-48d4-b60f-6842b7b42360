import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import * as argon2 from 'argon2';
import * as paginate from 'mongoose-paginate-v2';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { PositionsDto } from '../dto/users.dto';
import mongoose, { HydratedDocument } from 'mongoose';
import { Branch } from '../../branch/schemas/branch.schema';
import { allowCustomPrice } from './enums';
import { randomPass } from '../../../utils/random-str';

export type UserDocument = HydratedDocument<User>;
@Schema()
export class otp {
  @Prop({
    required: true,
  })
  value: string;

  @Prop({ default: 0 })
  retries?: number;

  @Prop({ type: Date })
  generated_timestamp?: Date;

  @Prop()
  period: number;
}

export class setting {
  @Prop()
  override_profit_margin: boolean;

  @Prop()
  allow_price_under_cost: boolean;

  @Prop()
  allow_custom_price: allowCustomPrice;
}
@Schema({ timestamps: true })
export class User {
  @Prop({
    required: true,
  })
  @ApiProperty()
  @IsNotEmpty()
  name: string;

  @Prop({ required: true, select: false })
  password: string;

  @ApiProperty()
  @Prop({ required: true, unique: true, lowercase: true, trim: true })
  email: string;

  @ApiProperty()
  @Prop({ default: false })
  email_verified: boolean;

  @ApiProperty()
  @Prop({ default: false })
  active: boolean;

  @Prop()
  otp: otp;

  @ApiProperty()
  @Prop()
  positions?: PositionsDto[];

  @Prop()
  default_payment_type?: number;

  @Prop()
  default_store?: string;

  @Prop({ type: Number })
  default_quantity?: number;

  @Prop({ type: Date })
  last_activity?: Date;

  @ApiProperty()
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: Branch.name,
    required: false,
  })
  active_branch?: mongoose.Types.ObjectId;

  @Prop()
  setting: setting;

  @Prop({
    type: String,
    default: () => randomPass(16),
    select: false,
  })
  secret: string;
  // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
  comparePassword: Function;

  @Prop({ type: String, default: '' })
  logo: string;
}

const userSchema = SchemaFactory.createForClass(User);

userSchema.pre('save', async function (next: any) {
  try {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const user = this;
    if (!user.isModified('password')) return next();
    user.password = await argon2.hash(user.password);
    user.secret = randomPass(16);
    return next();
  } catch (error) {
    console.log(error);
  }
});
userSchema.methods.comparePassword = async function (candidatePassword) {
  const result = await argon2.verify(this.password, candidatePassword);
  return result;
};
userSchema.plugin(paginate);
export { userSchema as User_Schema };
