import { SendMailOptions } from 'nodemailer';
export declare type TextEncoding = 'quoted-printable' | 'base64';

export interface Address {
  name: string;
  address: string;
}
export interface AttachmentLikeObject {
  path: string;
}
export interface ISendMailOptions extends SendMailOptions {
  to: string | Address | Array<string | Address>;
  cc: string | Address | Array<string | Address>;
  bcc?: string | Address | Array<string | Address>;
  replyTo?: string | Address;
  inReplyTo?: string | Address;
  from: string | Address;
  subject: string;
  text?: string | Buffer | AttachmentLikeObject;
  html?: string | Buffer;
  sender?: string | Address;
  raw?: string | Buffer;
  textEncoding?: TextEncoding;
  references?: string | string[];
  encoding?: string;
  date?: Date | string;
  headers?: Headers;
  context?: {
    [name: string]: any;
  };
  transporterName?: string;
  template?: string;
  attachments?: {
    filename: string;
    content?: any;
    path?: string;
    contentType?: string;
    cid?: string;
    encoding?: string;
    contentDisposition?: 'attachment' | 'inline' | undefined;
    href?: string;
  }[];
}
