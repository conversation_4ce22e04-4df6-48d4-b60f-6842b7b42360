import { HttpException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Connection, Model, Types } from 'mongoose';
import { CreateUserDto } from './dto/create-user.dto';
import { userFindOneDto } from './dto/find-one.dto';
import { forwardRef, Inject } from '@nestjs/common';
import { IdValidatorDto } from '../../utils/dto/general.dto';
import { TenantServices } from '../tenants/tenant.service';
import {
  CreateUserDto as CreateDto,
  GetUserPaginationDto,
  UpdateUserDto,
} from './dto/users.dto';
import { userCriticalProps } from './schemas/enums';
import { User, User_Schema, UserDocument } from './schemas/user.schema';
import { paginate } from '../../utils/dto/pagination.dto';
import { extend } from 'lodash';
import { nameOf } from '../../utils/object-key-name';
import { usersExceptionCode } from '../../exceptions/exception-code.users';
import { UserBranchesRolesService } from '../user-branches-roles/user-branches-roles.service';
import { PaymentTypesService } from '../payment-types/payment-types.service';
import { RequestWithUser } from '../auth/interfaces/authorize.interface';
import { BranchService } from '../branch/branch.service';
import { StoreService } from '../store/store.service';
import { Role } from '../roles/schemas/role.schema';
import * as argon2 from 'argon2';
import { randomPass } from '../../utils/random-str';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User.name) private userModel: Model<User>,
    @InjectModel(Role.name) private roleModel: Model<Role>,
    @Inject(forwardRef(() => TenantServices))
    private readonly tenantService: TenantServices,
    @Inject(forwardRef(() => PaymentTypesService))
    private readonly paymentTypeService: PaymentTypesService,
    @Inject(forwardRef(() => BranchService))
    private readonly branchService: BranchService,
    private readonly userBranchesRolesService: UserBranchesRolesService,
    private readonly storeService: StoreService,
  ) {}
  async create(request: RequestWithUser, createUserDto: CreateDto) {
    const tenant = request.user.code as any;
    const tenantUserData = await this.tenantService.getTenantUser2(
      createUserDto as any,
    );
    if (tenantUserData) {
      throw new HttpException(
        nameOf(usersExceptionCode, (x) => x.emailExist),
        usersExceptionCode.emailExist,
      );
    }
    const userData = await this.userModel.findOne({
      email: createUserDto.email,
    });

    if (userData) {
      throw new HttpException(
        nameOf(usersExceptionCode, (x) => x.emailExist),
        usersExceptionCode.emailExist,
      );
    }

    if (createUserDto.default_payment_type) {
      const paymentType = await this.paymentTypeService.findOnePayment(
        String(createUserDto.default_payment_type),
      );
      if (!paymentType) {
        throw new HttpException(
          nameOf(usersExceptionCode, (x) => x.paymentTypeNotFound),
          usersExceptionCode.paymentTypeNotFound,
        );
      }
      if (createUserDto.default_store) {
        const store = await this.storeService.findone({
          _id: createUserDto.default_store,
        });
        if (!store) {
          throw new HttpException(
            nameOf(usersExceptionCode, (x) => x.storeDoesNotExist),
            usersExceptionCode.storeDoesNotExist,
          );
        }
      }
    }

    const rs = await this.tenantService.createTenantUser(
      [createUserDto],
      tenant,
    );

    //extract unique roles and branches from positions
    if (createUserDto.positions.length > 0) {
      const extractUnique = (key) => {
        return [
          ...new Set(createUserDto.positions.flatMap((item) => item[key])),
        ];
      };
      const uniqueRoles = extractUnique('roles');
      const uniqueBranches = extractUnique('branches');

      await this.userBranchesRolesService.create({
        user: String(rs[0]._id),
        roles: uniqueRoles,
        branches: uniqueBranches,
      });
    }
    return rs[0];
  }

  async findOne(
    query: userFindOneDto,
    critical: userCriticalProps[] = [],
    populate: string = '',
  ): Promise<User> {
    const selectString = critical.map((data) => `+${data}`).join(' ');
    const user = await this.userModel
      .findOne(query)
      .populate(populate)
      .select(selectString)
      .lean();

    if (user?.positions !== undefined && user?.positions.length > 0) {
      (user.positions as any) = await this.populatePositions(user.positions);
    }
    return user;
  }

  async findOneUser(
    query: userFindOneDto,
    critical: userCriticalProps[] = [],
    populate: string = '',
  ): Promise<UserDocument> {
    const selectString = critical.map((data) => `+${data}`).join(' ');
    return (await this.userModel
      .findOne(query)
      .populate(populate)
      .select(selectString)) as UserDocument;
  }

  async findOneWithCustomConnection(
    query: userFindOneDto,
    critical: userCriticalProps[] = [],
    connection: Connection,
  ): Promise<User> {
    const selectString = ['email', 'name', 'active'];
    selectString.push(...critical);
    const userModel =
      connection.models.User ||
      (connection.model<User>(User.name, User_Schema) as any);
    return await userModel
      .findOne(query)
      //.populate(populate)
      .select(selectString);
  }

  async findAll(query: GetUserPaginationDto) {
    const { page, limit, queries, ...rest } = query;
    let search: any = rest ?? {};
    if (queries) {
      const searchfield = { $regex: new RegExp(queries), $options: 'i' };
      search = {
        ...search,
        $or: [{ name: searchfield }, { email: searchfield }],
      } as any;
    }

    if (rest.role) {
      search['positions'] = {
        $elemMatch: {
          roles: new Types.ObjectId(rest.role),
        },
      };
      delete rest.role;
    }

    const usersQuery = await this.userModel
      .find(search)
      .skip((query.page - 1) * query.limit)
      .limit(query.limit)
      .lean();

    const users = usersQuery.map(async (user) => {
      if (user?.positions !== undefined && user?.positions.length > 0) {
        (user.positions as any) = await this.populatePositions(user.positions);
      }

      return {
        ...user,
      };
    });

    const result = await Promise.all(users);

    const meta = await paginate(limit, page, this.userModel, search);
    return { result, meta };
  }

  async update(request: RequestWithUser, updateUserDto: UpdateUserDto) {
    //comes from url
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const { _id, ...rest } = updateUserDto;
    const userData = await this.userModel.findOne({ _id: _id }).lean();
    if (!userData) {
      throw new HttpException(
        nameOf(usersExceptionCode, (x) => x.userNotFound),
        usersExceptionCode.userNotFound,
      );
    }
    if (rest.email) {
      await this.tenantService.updateTenantUser(
        { user_email: userData.email },
        { user_email: rest.email },
      );
    }

    if (updateUserDto.default_payment_type) {
      const paymentType = await this.paymentTypeService.findOnePayment(
        updateUserDto.default_payment_type,
      );
      console.log('this');
      if (!paymentType) {
        throw new HttpException(
          nameOf(usersExceptionCode, (x) => x.paymentTypeNotFound),
          usersExceptionCode.paymentTypeNotFound,
        );
      }
    }

    if (rest.password) {
      rest.password = await argon2.hash(rest.password);
      userData.secret = randomPass(16);
    }
    const setting = { ...userData.setting };
    extend(setting, updateUserDto.setting);
    extend(userData, rest);
    if (updateUserDto.positions?.length) {
      userData.positions = updateUserDto.positions;
    }
    if (updateUserDto.setting) {
      userData.setting = setting;
    }
    const savedDate = await this.userModel.findOneAndUpdate(
      { _id: _id },
      userData,
      {
        new: true,
        runValidators: true,
      },
    );

    if (updateUserDto.positions?.length) {
      const extractUnique = (key) => {
        return [
          ...new Set(updateUserDto.positions.flatMap((item) => item[key])),
        ];
      };
      const uniqueRoles = extractUnique('roles');
      const uniqueBranches = extractUnique('branches');

      await this.userBranchesRolesService.create({
        user: String(userData._id),
        roles: uniqueRoles,
        branches: uniqueBranches,
      });
    }
    return savedDate;
  }

  async delete(query: IdValidatorDto): Promise<User> {
    //comes from url
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const { _id } = query;
    const userData = await this.userModel.findOneAndDelete({ _id });
    if (userData) {
      await this.tenantService.deleteTenantUser({ user_email: userData.email });
      return userData;
    }
    throw new HttpException(
      nameOf(usersExceptionCode, (x) => x.userNotFound),
      usersExceptionCode.userNotFound,
    );
  }
  async dropUserBranch(ids: Array<Types.ObjectId>, branch: Types.ObjectId) {
    await this.userModel.updateMany(
      { _id: { $in: ids } },
      {
        $pull: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          'positions.$[].branches': {
            $in: [new Types.ObjectId(branch), branch.toString()],
          }, // Branch to remove
        },
      },
    );
    return true;
  }
  async aggregate(query: any[]): Promise<any> {
    return this.userModel.aggregate(query);
  }

  async insertMany(users: CreateUserDto[]): Promise<UserDocument[]> {
    const result: UserDocument[] = [];
    for (const user of users) {
      result.push(await new this.userModel(user).save());
    }
    return result;
  }

  private populateBranch(branches: any, branchId: Types.ObjectId) {
    const branch = branches.find(
      (branch) => branch._id.toString() === String(branchId),
    );
    return branch
      ? { _id: branchId, name: branch.general_information.name }
      : null;
  }

  private populateRole(roles: any, roleId: Types.ObjectId) {
    const role = roles.find((role) => role._id.toString() === String(roleId));
    return role ? { _id: roleId, name: role.name } : null;
  }

  private async populatePositions(positions: any) {
    const extractUnique = (key) => {
      return [...new Set(positions.flatMap((item) => item[key]))];
    };
    const uniqueRoles = extractUnique('roles');
    const uniqueBranches = extractUnique('branches');

    const branches = await this.branchService.findByIds(uniqueBranches as any);

    const roles = await this.roleModel
      .find({ _id: { $in: uniqueRoles } })
      .select('_id name');

    return positions.map((position) => ({
      roles: position.roles
        .map((roleId) => this.populateRole(roles, roleId))
        .filter((role) => role !== null),

      branches: position.branches
        .map((branchId) => this.populateBranch(branches, branchId))
        .filter((branch) => branch !== null),
    }));
  }

  async findOneLimited(_id) {
    return await this.userModel.findOne({ _id }).select('_id secret email');
  }
}
