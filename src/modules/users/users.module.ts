import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TenantModule } from '../tenants/tenant.module';
import { User, User_Schema } from './schemas/user.schema';
import { UserController } from './users.controller';
import { UsersService } from './users.service';
import { UserBranchesRolesModule } from '../user-branches-roles/user-branch-role.module';
import { PaymentTypesModule } from '../payment-types/payment-types.module';
import { BranchModule } from '../branch/branch.module';
import { Role, roleSchema } from '../roles/schemas/role.schema';
import { DatabaseModule } from '../database/database.module';
import { StoreModule } from '../store/store.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: User.name, schema: User_Schema }]),
    MongooseModule.forFeature([{ name: Role.name, schema: roleSchema }]),
    DatabaseModule,
    forwardRef(() => TenantModule),
    StoreModule,
    UserBranchesRolesModule,
    PaymentTypesModule,
    BranchModule,
  ],
  controllers: [UserController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
