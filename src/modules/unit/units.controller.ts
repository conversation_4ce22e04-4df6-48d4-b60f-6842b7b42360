import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CreateUnitDto } from './dto/create-unit.dto';
import { GetUnitsPaginationDto } from './dto/get-units-pagination.dto';
import { UnitsService } from './units.service';
import { CaslGuard } from '../casl/guards/casl.guard';
import { Abilities } from '../casl/guards/abilities.decorator';
import { ApiBearerAuth, ApiBody, ApiHeader, ApiTags } from '@nestjs/swagger';
import { authType } from '../auth/guards/auth-type.decorator';
import { DeleteExceptIdsDto } from '../../utils/dto/delete-except-ids.dto';

@Controller('inventory/units')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@ApiTags('units')
export class UnitsController {
  constructor(private readonly unitsService: UnitsService) {}

  @UseGuards(CaslGuard)
  @Abilities({ action: 'create', subject: 'unit' })
  @Post()
  @authType('apiKey')
  @ApiBearerAuth('TOKEN')
  @ApiBody({ type: [CreateUnitDto] })
  create(@Body() createDto: CreateUnitDto[]) {
    return this.unitsService.createBulk(createDto);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'abstract_units' })
  @Get('abstract')
  async abstract(@Query() query: GetUnitsPaginationDto) {
    return await this.unitsService.abstract(query);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'unit' })
  @authType('apiKey')
  @ApiBearerAuth('TOKEN')
  @Get()
  findAll(@Query() query: GetUnitsPaginationDto) {
    return this.unitsService.findAll(query);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'delete', subject: 'unit' })
  @authType('apiKey')
  @ApiBearerAuth('TOKEN')
  @Delete()
  delete(@Body() dto: DeleteExceptIdsDto) {
    return this.unitsService.delete(dto.ids);
  }
}
