import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { NameDto } from '../../../utils/dto/name.dto';
import { itemType } from '../../inventory-item/types/item-types.enum';

@Schema({ timestamps: true })
export class Unit {
  @Prop({ type: Number, required: true, unique: true })
  id: number;

  @ApiProperty()
  @Prop({ type: String, required: true })
  code: string;

  @ApiProperty()
  @Prop({ type: NameDto, required: true, unique: true })
  name: NameDto;

  @Prop({ type: String, enum: itemType })
  type: itemType;
}

export const unitSchema = SchemaFactory.createForClass(Unit);
unitSchema.index({ id: 1, type: 1 }, { unique: true });
