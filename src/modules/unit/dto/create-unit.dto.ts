import { Validate } from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsNumber, IsOptional } from 'class-validator';
import {
  NameDto,
  validateEnLanguage,
  validateLanguage,
} from '../../../utils/dto/name.dto';
import { toLowerCaseObjectKeys } from '../../../utils/tolowerCaseObjectKeys';
import { itemType } from '../../inventory-item/types/item-types.enum';

export class CreateUnitDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  id: number;

  @ApiProperty({
    description: ' code of item unit',
    example: '1234',
    required: true,
  })
  @IsNumber()
  @IsOptional()
  Code: string;

  @ApiProperty({
    description: 'name of unit in second language',
    type: NameDto,
    required: true,
  })
  @ApiProperty()
  @Transform((param) => toLowerCaseObjectKeys(param.value))
  @Validate(validateLanguage)
  @Validate(validateEnLanguage)
  public name: NameDto;

  @ApiProperty({
    description: 'group type',
    required: false,
    enum: itemType,
  })
  @IsNotEmpty()
  @IsEnum(itemType)
  type: itemType;
}
