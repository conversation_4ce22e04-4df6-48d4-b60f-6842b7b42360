import { IsEnum, <PERSON>NotEmpty, <PERSON>Optional, IsString } from 'class-validator';
import { PaginationDto } from '../../../utils/dto/pagination.dto';
import { itemType } from '../../inventory-item/types/item-types.enum';

export class GetUnitsPaginationDto extends PaginationDto {
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  public _id?: string;

  @IsOptional()
  @IsEnum(itemType)
  type?: itemType;
}
