import { IsNotEmpty, IsString } from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum } from 'class-validator';
import { itemType } from '../../inventory-item/types/item-types.enum';

export class GetItemDto {
  @ApiProperty({ type: 'string', required: true })
  @IsString()
  @IsNotEmpty()
  public _id: string;

  @IsNotEmpty()
  @IsEnum(itemType)
  type: itemType;
}
