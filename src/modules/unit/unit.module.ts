import { Module } from '@nestjs/common';
import { Unit, unitSchema } from './schema/unit.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { UnitsController } from './units.controller';
import { UnitsService } from './units.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Unit.name, schema: unitSchema }]),
  ],
  controllers: [UnitsController],
  providers: [UnitsService],
  exports: [UnitsService],
})
export class UnitModule {}
