import { Injectable, Scope } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model } from 'mongoose';
import { paginate } from '../../utils/dto/pagination.dto';
import { CreateUnitDto } from './dto/create-unit.dto';
import { GetUnitsPaginationDto } from './dto/get-units-pagination.dto';
import { UnitDto } from './dto/unit.dto';
import { Unit } from './schema/unit.schema';

@Injectable({ scope: Scope.REQUEST, durable: true })
export class UnitsService {
  constructor(
    @InjectModel(Unit.name)
    private readonly unitsModel: Model<Unit>,
  ) {}
  async createBulk(createSalesRepresentativeDto: CreateUnitDto[]) {
    const bulkOps = createSalesRepresentativeDto.map((dto) => ({
      updateOne: {
        filter: { id: dto.id },
        update: { $set: dto },
        upsert: true,
      },
    }));

    return this.unitsModel.bulkWrite(bulkOps);
  }

  async findOne(_id: string | mongoose.ObjectId): Promise<UnitDto> {
    return this.unitsModel.findOne({ _id }).exec();
  }

  async abstract(dto: GetUnitsPaginationDto) {
    const filter: any = {};
    if (dto.type) {
      filter.type = dto.type;
    }

    const result = await this.unitsModel
      .find(filter)
      .select('_id name number')
      .exec();
    return { result: result };
  }

  async findAll(dto: GetUnitsPaginationDto) {
    //comes from request
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const { page, limit, _id, sortBy, sortType } = dto;
    const sort = { [sortBy]: sortType };
    if (_id) {
      const result = this.unitsModel.findOne({ _id }).exec();
      return {
        result: [result],
      };
    }

    const filter: any = {};

    if (dto.type) {
      filter.type = dto.type;
    }

    const result = await this.unitsModel
      .find(filter, undefined, { sort })
      .skip((page - 1) * limit)
      .limit(limit);
    const meta = await paginate(limit, page, this.unitsModel, {});
    return { result, meta };
  }

  async findByIds(ids: Array<string>): Promise<Array<UnitDto>> {
    return this.unitsModel.find({ id: { $in: ids } });
  }

  async delete(ids: number[]) {
    const result = await this.unitsModel.deleteMany({
      id: { $nin: ids },
    });
    return result;
  }
}
