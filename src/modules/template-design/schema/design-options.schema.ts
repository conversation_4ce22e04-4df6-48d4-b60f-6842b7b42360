import { Prop, Schema } from '@nestjs/mongoose';

export enum titleWeight {
  bold = 'bold',
  regular = 'regular',
}

export enum textAlign {
  right = 'right',
  left = 'left',
}

@Schema()
export class DesignOptions {
  @Prop()
  logo_in_background: boolean;

  @Prop({ required: true, enum: titleWeight })
  title_weight: titleWeight;

  @Prop({ required: true, enum: textAlign })
  text_align: textAlign;
}
