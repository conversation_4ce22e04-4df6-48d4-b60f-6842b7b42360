import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { SendOptions } from './send-options.schema';
import { templateTypeEnum } from '../dto/create-template-design.dto';
import { NameDto } from '../dto/name.dto';
import { ControlOptions } from './control-options.schema';
import { DesignOptions } from './design-options.schema';
import { LogoSettings } from './logo-settings.schema';
import { HydratedDocument } from 'mongoose';

export type TemplateDesignDocument = HydratedDocument<TemplateDesign>;

@Schema({ timestamps: true })
export class TemplateDesign {
  @Prop({ type: NameDto, default: ' ' })
  template: NameDto;

  @Prop({ type: String, enum: templateTypeEnum, unique: true })
  type: templateTypeEnum;

  @Prop({ type: ControlOptions })
  control_options: ControlOptions;

  @Prop({ type: LogoSettings })
  logo_settings: LogoSettings;

  @Prop({ type: DesignOptions })
  design_options: DesignOptions;

  @Prop({ type: SendOptions })
  send_options: SendOptions;
}

export const templateDesignSchema =
  SchemaFactory.createForClass(TemplateDesign);
