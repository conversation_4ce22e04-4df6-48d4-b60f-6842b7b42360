import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  TemplateDesign,
  templateDesignSchema,
} from './schema/template-design.schema';
import { TemplateDesignController } from './template-design.controller';
import { TemplateDesignService } from './template-design.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: TemplateDesign.name, schema: templateDesignSchema },
    ]),
  ],
  controllers: [TemplateDesignController],
  providers: [TemplateDesignService],
  exports: [TemplateDesignService],
})
export class TemplateDesignModule {}
