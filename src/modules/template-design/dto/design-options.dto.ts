import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsBoolean, IsEnum } from 'class-validator';
import { textAlign, titleWeight } from '../schema/design-options.schema';

export class DesignOptionsDto {
  @ApiProperty()
  @IsBoolean()
  logo_in_background: boolean;

  @Transform((param) => param.value.toLowerCase())
  @ApiProperty({ enum: titleWeight })
  @IsEnum(titleWeight)
  title_weight: titleWeight;

  @Transform((param) => param.value.toLowerCase())
  @ApiProperty({ enum: textAlign })
  @IsEnum(textAlign)
  text_align: textAlign;
}
