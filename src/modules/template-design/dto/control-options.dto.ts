import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsString } from 'class-validator';

export class ControlOptionsDto {
  @ApiProperty()
  @IsBoolean()
  invoice_title: boolean;

  @ApiProperty()
  @IsBoolean()
  tax_number: boolean;

  @ApiProperty()
  @IsBoolean()
  payment_method: boolean;

  @ApiProperty()
  @IsBoolean()
  store_address: boolean;

  @ApiProperty()
  @IsBoolean()
  vat: boolean;

  @ApiProperty()
  @IsBoolean()
  footer: boolean;

  @ApiProperty()
  @IsString()
  footer_text: string;
}
