import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEnum, IsNumber } from 'class-validator';
import { logoAlign } from '../schema/logo-settings.schema';

export class LogoSettingsDto {
  @Transform((param) => param.value.toLowerCase())
  @ApiProperty({ enum: logoAlign })
  @IsEnum(logoAlign)
  align: logoAlign;

  @ApiProperty()
  @IsNumber()
  logo_size: number;
}
