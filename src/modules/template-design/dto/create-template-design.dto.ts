import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, Validate } from 'class-validator';
import { NameDto, validateEnLanguage, validateLanguage } from './name.dto';
import { ValidateNested } from '@nestjs/class-validator';
import { Type } from 'class-transformer';
import { ControlOptionsDto } from './control-options.dto';
import { LogoSettingsDto } from './logo-settings.dto';
import { DesignOptionsDto } from './design-options.dto';
import { SendOptionsDto } from './send-options.dto';
export enum templateTypeEnum {
  tax_invoice = 'tax_invoice',
  simplified_tax_invoice = 'simplified_tax_invoice',
  thermal_invoice = 'thermal_invoice',
  purchase_invoice = 'purchase_invoice',
  purchase_return_invoice = 'purchase_return_invoice',
  partner_payment_voucher = 'partner_payment_voucher',
  general_payment_voucher = 'general_payment_voucher',
  partner_receipt_voucher = 'partner_receipt_voucher',
  general_receipt_voucher = 'general_receipt_voucher',
  account_journal = 'account_journal',
  debit_credit_memo = 'debit_credit_memo',
  sales_return = 'sales_return',
}
export class CreateTemplateDesignDto {
  @ApiProperty()
  @Validate(validateLanguage)
  @Validate(validateEnLanguage)
  template: NameDto;
  @ApiProperty()
  @IsEnum(templateTypeEnum)
  type: templateTypeEnum;

  @ApiProperty()
  @ValidateNested()
  @Type(() => ControlOptionsDto)
  control_options: ControlOptionsDto;

  @ApiProperty()
  @ValidateNested()
  @Type(() => LogoSettingsDto)
  logo_settings: LogoSettingsDto;

  @ApiProperty()
  @ValidateNested()
  @Type(() => DesignOptionsDto)
  design_options: DesignOptionsDto;

  @ApiProperty()
  @ValidateNested()
  @Type(() => SendOptionsDto)
  send_options: SendOptionsDto;
}

export class CreateStaticTemplateDesignDto {
  @ApiProperty()
  @Validate(validateLanguage)
  @Validate(validateEnLanguage)
  template: NameDto;
  @ApiProperty()
  @IsEnum(templateTypeEnum)
  type: templateTypeEnum;
}
