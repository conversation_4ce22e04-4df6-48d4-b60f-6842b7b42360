import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { TemplateDesignService } from './template-design.service';
import {
  CreateTemplateDesignDto,
  templateTypeEnum,
} from './dto/create-template-design.dto';
import { UpdateTemplateDesignDto } from './dto/update-template-design.dto';
import { ApiHeader, ApiTags } from '@nestjs/swagger';
import { testTemplate } from './dto/test-template.dto';
import { CaslGuard } from '../casl/guards/casl.guard';
import { Abilities } from '../casl/guards/abilities.decorator';
import { renderTemplate } from '../../utils/render-template';

@ApiTags('template design')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@Controller('trade/template-design')
export class TemplateDesignController {
  constructor(private readonly templateDesignService: TemplateDesignService) {}

  @UseGuards(CaslGuard)
  @Abilities({ action: 'create', subject: 'invoice_template' })
  @Post()
  create(@Body() createTemplateDesignDto: CreateTemplateDesignDto) {
    return this.templateDesignService.create(createTemplateDesignDto);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'invoice_template' })
  @Get()
  findAll() {
    return this.templateDesignService.findAll();
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'read', subject: 'invoice_template' })
  @Get(':type')
  findOne(@Param('type') type: templateTypeEnum) {
    return this.templateDesignService.findOneByType(type);
  }

  @Post('test/:_id')
  async testTemplate(@Param('_id') _id: string, @Body() body: testTemplate) {
    const templateData = await this.templateDesignService.findOne(_id);
    return renderTemplate(templateData.template.en, body.data, {});
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'edit', subject: 'invoice_template' })
  @Patch(':type')
  update(
    @Param('type') type: templateTypeEnum,
    @Body() updateTemplateDesignDto: UpdateTemplateDesignDto,
  ) {
    return this.templateDesignService.updateWithType(
      type,
      updateTemplateDesignDto,
    );
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'delete', subject: 'invoice_template' })
  @Delete(':_id')
  remove(@Param('_id') _id: string) {
    return this.templateDesignService.remove(_id);
  }
}
