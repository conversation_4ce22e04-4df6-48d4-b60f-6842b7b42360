import { HttpException, Injectable, Scope } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { extend } from 'lodash';
import { Model } from 'mongoose';
import {
  CreateStaticTemplateDesignDto,
  CreateTemplateDesignDto,
  templateTypeEnum,
} from './dto/create-template-design.dto';
import { UpdateTemplateDesignDto } from './dto/update-template-design.dto';
import {
  TemplateDesign,
  TemplateDesignDocument,
} from './schema/template-design.schema';
import { nameOf } from '../../utils/object-key-name';
import { renderTemplate } from '../../utils/render-template';
import { simplifiedTaxInvoiceTemplate } from '../../utils/seeders/simplified-tax-invoice';
import { taxInvoiceTemplate } from '../../utils/seeders/tax-invoice';
import { usersExceptionCode } from '../../exceptions/exception-code.users';

@Injectable({ scope: Scope.REQUEST, durable: true })
export class TemplateDesignService {
  constructor(
    @InjectModel(TemplateDesign.name)
    private readonly templateDesignModel: Model<TemplateDesign>,
  ) {}
  async create(
    createTemplateDesignDto: CreateTemplateDesignDto,
  ): Promise<TemplateDesign> {
    return await new this.templateDesignModel(createTemplateDesignDto).save();
  }

  async findAll(): Promise<TemplateDesign[]> {
    return await this.templateDesignModel.find({});
  }

  async findOne(_id: string): Promise<TemplateDesignDocument> {
    console.log('_id', _id);

    return await this.templateDesignModel.findOne({ _id });
  }
  async findOneByType(type: templateTypeEnum): Promise<TemplateDesignDocument> {
    let templateData = await this.templateDesignModel.findOne({ type });
    if (!templateData) {
      if (type == templateTypeEnum.simplified_tax_invoice) {
        await new this.templateDesignModel(simplifiedTaxInvoiceTemplate).save();
      }
      if (type == templateTypeEnum.tax_invoice) {
        await new this.templateDesignModel(taxInvoiceTemplate).save();
      }
      templateData = await this.templateDesignModel.findOne({ type });
    }
    return templateData;
  }
  async update(_id: string, updateTemplateDesignDto: UpdateTemplateDesignDto) {
    const templates = await this.findOne(_id);
    if (!templates) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.templateDoesNotExist,
        ),
        usersExceptionCode.templateDoesNotExist,
      );
    }
    extend(templates, updateTemplateDesignDto);
    return await templates.save();
  }
  async updateWithType(
    type: templateTypeEnum,
    updateTemplateDesignDto: UpdateTemplateDesignDto,
  ) {
    const templates = await this.findOneByType(type);
    if (!templates) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.templateDoesNotExist,
        ),
        usersExceptionCode.templateDoesNotExist,
      );
    }
    extend(templates, updateTemplateDesignDto);
    return await templates.save();
  }
  remove(_id: string) {
    return this.templateDesignModel.deleteOne({ _id });
  }

  async renderTemplateByType(
    templateType: templateTypeEnum,
    data,
    lang = 'en',
  ): Promise<string> {
    const templateData = (await this.findOneByType(templateType))?.toObject();

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { template, type, ...rest } = templateData;
    if (!template) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.templateDoesNotExist,
        ),
        usersExceptionCode.templateDoesNotExist,
      );
    }
    return renderTemplate(template[lang], { ...data, ...rest }, {});
  }
  async renderStaticTemplateByType(
    staticTemplate: CreateStaticTemplateDesignDto,
    data,
    lang = 'en',
  ): Promise<string> {
    const templateData = staticTemplate;

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { type, template, ...rest } = templateData;
    return renderTemplate(template[lang], { ...data, ...rest }, {});
  }
}
