import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { HydratedDocument, Types } from 'mongoose';
import { ItemUnitPrice, itemUnitPriceSchema } from './item-units-prices.schema';
import { NameDto } from '../../../utils/dto/name.dto';
import { itemType } from '../types/item-types.enum';
import { Store } from '../../store/schema/store.schema';

export type ItemDocument = HydratedDocument<Item>;

@Schema({ timestamps: true })
export class Item {
  @Prop({ type: Number, required: true })
  id: number;

  @ApiProperty()
  @Prop({ type: NameDto, required: true })
  name: NameDto;

  @ApiProperty()
  @Prop({ type: String, required: true })
  code: string;

  @ApiProperty()
  @Prop({ required: true, enum: itemType, default: itemType.goods })
  type: itemType;

  @ApiProperty()
  @Prop({
    type: String,
    ref: Store.name,
    required: true,
  })
  store: Types.ObjectId;

  @ApiProperty()
  @Prop({
    type: Number,
    required: false,
  })
  item_group?: number;

  @ApiProperty()
  @Prop({ type: Boolean, default: true })
  active: boolean;

  @ApiProperty()
  @Prop({ default: true })
  tax_on_sales: boolean; //true

  @ApiProperty({
    type: [ItemUnitPrice],
  })
  @Prop({
    type: [itemUnitPriceSchema],
    required: false,
    default: [],
  })
  units: ItemUnitPrice[];

  @ApiProperty()
  @Prop({ type: Number, default: 0 })
  average_cost_in_base_unit: number;

  @ApiProperty()
  @Prop({ type: Number, default: 0 })
  max_quantity_in_base_unit: number;
}

export const itemSchema = SchemaFactory.createForClass(Item);
itemSchema.index({ id: 1, store: 1 }, { unique: true });
