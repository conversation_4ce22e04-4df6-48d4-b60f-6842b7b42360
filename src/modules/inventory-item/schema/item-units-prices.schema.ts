import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';

@Schema()
export class ItemUnitPrice {
  @ApiProperty()
  @Prop({
    type: Number,
    ref: 'Unit',
    required: true,
  })
  unit: number;

  @ApiProperty()
  @Prop({ required: false, default: false })
  is_default: boolean;

  @ApiProperty()
  @Prop({ required: false, default: '' })
  barcode1: string;

  @ApiProperty()
  @Prop({ required: false, default: '' })
  barcode2: string;

  @ApiProperty()
  @Prop({ type: Number, required: false, default: 0 })
  sales_price: number;

  @ApiProperty()
  @Prop({ type: Number, required: false, default: 0 })
  wholesale_price: number;

  @ApiProperty()
  @Prop({ type: Number, required: false, default: 0 })
  special_price: number;

  @Prop({ type: Number, default: 0 })
  quantity: number;

  @Prop({ type: Number, default: 0 })
  avg_cost: number;
}

export const itemUnitPriceSchema = SchemaFactory.createForClass(ItemUnitPrice);
