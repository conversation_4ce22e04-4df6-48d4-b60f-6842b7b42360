import { HttpException, Injectable, Scope } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { extend } from 'lodash';
import { FilterQuery, Model, PipelineStage, Types } from 'mongoose';
import { nameOf } from '../../utils/object-key-name';
import { CreateItemDto } from './dto/create-item.dto';
import { GetItemsPaginationDto } from './dto/get-items.dto';
import { ItemsListResponseDto } from './dto/items-list-response.dto';
import { UpdateItemDto } from './dto/update-item.dto';
import { Item, ItemDocument } from './schema/item.schema';
import { ItemGroup } from '../group-item/schema/item-group.schema';
import { RequestWithUser } from '../auth/interfaces/authorize.interface';
import { usersExceptionCode } from '../../exceptions/exception-code.users';
import { findMaxNumber } from '../../utils/auto-increment-number';
import { aggregatePaginate, paginate } from '../../utils/dto/pagination.dto';
import { GetAbstractItemsPaginationDto } from './dto/get-abstract-items.dto';
import { Unit } from '../unit/schema/unit.schema';
import { searchTypeEnum } from './types/search-types.enum';
import { ItemExplorerQueryDto } from '../item-explorer/dto/item-explorer.dto';
import * as _ from 'lodash';
import { availabilityEnum } from '../item-explorer/enums/availability.enum';

@Injectable({ scope: Scope.REQUEST, durable: true })
export class ItemsService {
  constructor(
    @InjectModel(Item.name)
    private readonly inventoryItemModel: Model<Item>,
    @InjectModel(ItemGroup.name)
    private readonly itemGroupModel: Model<ItemGroup>,
    @InjectModel(Unit.name)
    private readonly unitModel: Model<Unit>,
  ) {}

  async create(request, createInventoryDto: CreateItemDto) {
    const codeExist = await this.inventoryItemModel.findOne({
      code: createInventoryDto.code,
      type: createInventoryDto.type,
    });
    if (codeExist) {
      throw new HttpException(
        nameOf(usersExceptionCode, (exception) => exception.numberDuplicated),
        usersExceptionCode.numberDuplicated,
      );
    }

    const item = await new this.inventoryItemModel(createInventoryDto).save();

    return await this.inventoryItemModel
      .findOne({ _id: item._id })
      .populate({
        path: 'item_group',
        model: 'ItemGroup',
        localField: 'item_group',
        foreignField: 'id',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        justOne: true,
      })
      .exec();
  }

  async createBulk(createInventoryDtos: CreateItemDto[]) {
    const bulkOps = createInventoryDtos.map((dto) => ({
      updateOne: {
        filter: { id: dto.id, store: dto.store },
        update: { $set: dto as any },
        upsert: true,
      },
    }));

    return this.inventoryItemModel.bulkWrite(bulkOps);
  }

  async findMaxNumber() {
    return findMaxNumber(this.inventoryItemModel);
  }

  async findAll(
    request,
    query: GetItemsPaginationDto,
  ): Promise<ItemsListResponseDto> {
    const { limit, page, queries, sortBy, sortType, code, type, ...rest } =
      query;
    const sort = { [sortBy]: sortType };
    let search: any = rest ?? {};

    if (type) {
      search.type = type;
    }

    if (queries) {
      const searchText = {
        $regex: new RegExp(`(^|\\s)${queries}`),
        $options: 'i',
      };

      search = {
        ...search,
        $or: [
          {
            'name.en': searchText,
          },
          {
            'name.ar': searchText,
          },
          {
            code: searchText,
          },
        ],
      } as any;
    }
    if (code) {
      const otherSearchfield = { $regex: new RegExp(code), $options: 'i' };
      search = {
        ...search,
        $or: [
          { 'name.ar': otherSearchfield },
          { 'name.en': otherSearchfield },
          { code: otherSearchfield },
          // eslint-disable-next-line @typescript-eslint/naming-convention
          { 'units.barcode1': otherSearchfield },
          // eslint-disable-next-line @typescript-eslint/naming-convention
          { 'units.barcode2': otherSearchfield },
        ],
      } as any;
    }

    const result = await this.inventoryItemModel
      .find(search, null, { sort })
      .skip((page - 1) * limit)
      .limit(limit);

    const meta = await paginate(limit, page, this.inventoryItemModel, search);
    return { meta, result };
  }

  async abstract(query: GetAbstractItemsPaginationDto) {
    let searchText = undefined;
    if (query.queries) {
      searchText = query.queries;
    }
    if (searchText !== undefined) {
      //item select for sales invoice
      const item = await this.inventoryItemModel
        .findOne({
          $or: [
            { code: searchText, store: query.store },
            {
              units: {
                $elemMatch: {
                  $or: [{ barcode1: searchText }, { barcode2: searchText }],
                },
              },
            },
          ],
        })
        .select('id type')
        .lean();

      if (!item) {
        throw new HttpException(
          nameOf(usersExceptionCode, (exceptions) => exceptions.itemNotFound),
          usersExceptionCode.itemNotFound,
        );
      }
      const result = await this.findOne(item.code, item.type, query.store);
      return {
        result: result,
      };
    } else {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.invalidSearchQuery,
        ),
        usersExceptionCode.invalidSearchQuery,
      );
    }
  }

  async findOne(
    id: string,
    type: string,
    store: string | Types.ObjectId,
  ): Promise<Item> {
    const item: any = await this.inventoryItemModel
      .findOne({ id, type, store })
      .populate({
        path: 'item_group',
        model: 'ItemGroup',
        localField: 'item_group',
        foreignField: 'id',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        justOne: true,
      })
      .lean();

    if (!item) {
      throw new HttpException(
        nameOf(usersExceptionCode, (exceptions) => exceptions.itemNotFound),
        usersExceptionCode.itemNotFound,
      );
    }
    return item;
  }

  async getById(store: string, id: number): Promise<Item> {
    const item: any = await this.inventoryItemModel
      .findOne({ id, store })
      .populate({
        path: 'item_group',
        model: 'ItemGroup',
        localField: 'item_group',
        foreignField: 'id',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        justOne: true,
      })
      .populate({
        path: 'units.unit',
        model: Unit.name,
        localField: 'units.unit',
        foreignField: 'id',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        justOne: true,
      })
      .lean();

    if (!item) {
      throw new HttpException(
        nameOf(usersExceptionCode, (exceptions) => exceptions.itemNotFound),
        usersExceptionCode.itemNotFound,
      );
    }
    if (Array.isArray(item.units)) {
      item.units = item.units.map((u) => {
        const populatedUnit = u.unit;
        if (populatedUnit && typeof populatedUnit === 'object') {
          const { id, number, name, type, ...restUnit } = populatedUnit;
          return {
            ...u,
            unit: id, // replace populated object with its ID
            number,
            name,
            type,
            ...restUnit,
          };
        }
        return u;
      });
    }
    return item;
  }

  async searchById(store: string, id: string): Promise<any> {
    const searchText = id;
    if (searchText !== undefined) {
      //item select for sales invoice
      const item = await this.inventoryItemModel
        .findOne({
          $or: [
            { code: searchText, store },
            {
              units: {
                $elemMatch: {
                  $or: [{ barcode1: searchText }, { barcode2: searchText }],
                },
              },
            },
          ],
        })
        .select('id type')
        .lean();
      if (!item) {
        throw new HttpException(
          nameOf(usersExceptionCode, (exceptions) => exceptions.itemNotFound),
          usersExceptionCode.itemNotFound,
        );
      }
      const fullItem = await this.getById(store, item.id);
      return {
        ...fullItem,
        id: item.id.toString(),
        units: fullItem.units.map((unit) => ({
          ...unit,
          unit: unit.unit.toString(),
        })),
      };
    } else {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.invalidSearchQuery,
        ),
        usersExceptionCode.invalidSearchQuery,
      );
    }
  }

  async update(
    request: RequestWithUser,
    id: number,
    updateInventoryDto: UpdateItemDto,
  ): Promise<ItemDocument> {
    const itemData = await this.inventoryItemModel.findOne({
      id,
      type: updateInventoryDto.type,
    });
    if (!itemData) throw new Error('item not found');

    await this.checkItemGroup(updateInventoryDto);

    extend(itemData, updateInventoryDto);

    await itemData.save();

    const result = this.inventoryItemModel
      .findOne({ id })
      .populate({
        path: 'item_group',
        model: 'ItemGroup',
        localField: 'item_group',
        foreignField: 'id',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        justOne: true,
      })
      .exec();

    return result;
  }

  async remove(ids: number[]): Promise<any> {
    return await this.inventoryItemModel.deleteMany({
      id: { $nin: ids },
    });
  }

  async findByIds(
    ids: Array<string>,
    store: Types.ObjectId,
  ): Promise<Array<Item>> {
    return this.inventoryItemModel.find({ id: { $in: ids }, store });
  }

  private async checkItemGroup(itemDto: CreateItemDto | UpdateItemDto) {
    if (itemDto.item_group) {
      const itemServiceGroup = await this.itemGroupModel.findOne({
        _id: itemDto.item_group,
      });

      if (!itemServiceGroup) {
        throw new HttpException(
          nameOf(usersExceptionCode, (exception) => exception.invalidItemGroup),
          usersExceptionCode.invalidItemGroup,
        );
      }
    }
  }

  async itemExplorer(query: ItemExplorerQueryDto) {
    let databaseQuery: any = {};
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const { sortBy, sortType, from_price, to_price, store } = query;
    const sortTypeData = sortType === 'asc' ? 1 : -1; // Convert 'asc' or 'desc' to 1 or -1

    const filter: Partial<FilterQuery<Item>> = _.pick(query, ['item_group']);
    if (query.type) filter.type = query.type;
    databaseQuery = { ...databaseQuery, ...filter };
    if (query.queries) {
      const escapedQuery = query.queries.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

      let regex;
      switch (query.search_type) {
        case searchTypeEnum.start_with:
          regex = new RegExp(`^${escapedQuery}`, 'i'); // Starts with
          break;
        case searchTypeEnum.contains:
          regex = new RegExp(`${escapedQuery}`, 'i'); // Contains
          break;
        case searchTypeEnum.end_with:
          regex = new RegExp(`${escapedQuery}$`, 'i'); // Ends with
          break;
        case searchTypeEnum.exact_match:
          regex = new RegExp(`^${escapedQuery}$`, 'i'); // Exact match
          break;
        default:
          throw new Error('Invalid search type');
      }
      databaseQuery.$or = [
        { code: { $regex: regex } },
        { 'name.ar': { $regex: regex } },
        { 'name.en': { $regex: regex } },
      ];
    }
    if (store) {
      databaseQuery.store = String(store);
    }
    if (query.date_added) {
      const startOfDay = new Date(query.date_added);
      startOfDay.setHours(0, 0, 0, 0); // Set to 00:00:00.000

      const endOfDay = new Date(query.date_added);
      endOfDay.setHours(23, 59, 59, 999); // Set to 23:59:59.999

      databaseQuery.createdAt = { $gte: startOfDay, $lt: endOfDay };
    }

    const pipeline: PipelineStage[] = [];

    // Add initial $match stage
    if (Object.keys(databaseQuery).length > 0) {
      pipeline.push({
        $match: databaseQuery as FilterQuery<any>,
      });
    }
    pipeline.push({ $unwind: '$units' });
    pipeline.push({
      $addFields: {
        'units.unitAsNumber': {
          $convert: {
            input: '$units.unit',
            to: 'long', // use "long" for 64-bit integers
          },
        },
      },
    });

    pipeline.push({
      $lookup: {
        from: 'units',
        localField: 'units.unitAsNumber',
        foreignField: 'id',
        as: 'units.unit_data',
      },
    });
    pipeline.push({
      $unset: 'units.unitAsNumber',
    });

    pipeline.push({
      $unwind: {
        path: '$units.unit_data',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        preserveNullAndEmptyArrays: true, // Optional: In case unit is missing
      },
    });

    // Grouping stage to calculate base factor
    pipeline.push({
      $group: {
        _id: '$_id',
        item_group: { $first: '$item_group' },
        code: { $first: '$code' },
        name: { $first: '$name' },
        createdAt: { $first: '$createdAt' },
        units: { $push: '$units' },
        type: { $first: '$type' },
      },
    });

    pipeline.push({
      $addFields: {
        selected_unit: {
          $arrayElemAt: [
            {
              $filter: {
                input: '$units',
                as: 'unit',
                cond: {
                  $and: [
                    { $eq: ['$$unit.is_default', true] }, // base_factor condition
                    ...(from_price !== undefined
                      ? [{ $gte: ['$$unit.sales_price', from_price] }]
                      : []), // from_price condition
                    ...(to_price !== undefined
                      ? [{ $lte: ['$$unit.sales_price', to_price] }]
                      : []), // to_price condition
                  ],
                },
              },
            },
            0, // Select the first matching unit
          ],
        },
      },
    });

    pipeline.push({
      $match: {
        selected_unit: { $exists: true, $ne: null, $not: { $size: 0 } },
      },
    });
    pipeline.push({
      $lookup: {
        from: 'itemgroups', // The collection that contains item group data
        localField: 'item_group', // The field in your current collection (e.g., item_group_id)
        foreignField: 'id', // The field in the 'item_groups' collection
        as: 'item_group', // The field name where the populated data will be stored
      },
    });

    //  Add the availability logic based on unitCost
    pipeline.push({
      $addFields: {
        availability: {
          $cond: {
            if: {
              $in: [
                { $type: '$selected_unit.quantity' },
                ['int', 'double', 'decimal'],
              ], // Check if total_quantity_in_base_unit exists and is a number
            },
            then: {
              $cond: {
                if: {
                  $lte: ['$selected_unit.quantity', 0], // Check if quantity is zero
                },
                then: availabilityEnum.out_of_stock,
                else: {
                  $cond: {
                    if: {
                      $gt: ['$selected_unit.quantity', 0], // Check if quantity is low
                    },
                    then: 'available',
                    else: availabilityEnum.available,
                  },
                },
              },
            },
            else: availabilityEnum.out_of_stock, // Default if no snapshot
          },
        },
      },
    });

    if (query.availability !== availabilityEnum.all_stock) {
      pipeline.push({
        $match: {
          availability: query.availability, // Match the availability based on the query
        },
      });
    }

    // After the $lookup, use $arrayElemAt to convert the array into a single object
    pipeline.push({
      $addFields: {
        item_group: {
          $arrayElemAt: ['$item_group', 0], // Select the first element of the array
        },
      },
    });
    // Project final fields
    pipeline.push({
      $project: {
        _id: 1,
        item_group: 1,
        code: 1,
        name: 1,
        createdAt: 1,
        base_factor: 1,
        selected_unit: 1,
        min_quantity: 1,
        max_quantity: 1,
        type: 1,
        store_inventory: 1,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        unitCost: 1, // Include the unitCost data
        availability: 1, // Include the availability data
      },
    });

    pipeline.push({ $sort: { [sortBy]: sortTypeData } });
    const result = await this.inventoryItemModel.aggregate([
      ...pipeline,
      { $skip: (query.page - 1) * query.limit },
      { $limit: query.limit },
    ]);
    const meta = await aggregatePaginate(
      query.limit,
      query.page,
      this.inventoryItemModel,
      pipeline,
    );

    return { meta, result };
  }
}
