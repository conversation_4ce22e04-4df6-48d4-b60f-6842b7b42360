import { Item, itemSchema } from './schema/item.schema';
import { ItemsController } from './items.controller';
import { ItemsService } from './items.service';
import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { StoreModule } from '../store/store.module';
import { GroupItemModule } from '../group-item/group-item.module';
import { Store, storeSchema } from '../store/schema/store.schema';
import {
  ItemGroup,
  itemGroupSchema,
} from '../group-item/schema/item-group.schema';
import { Unit, unitSchema } from '../unit/schema/unit.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Item.name, schema: itemSchema },
      { name: ItemGroup.name, schema: itemGroupSchema },
      { name: Store.name, schema: storeSchema },
      { name: Unit.name, schema: unitSchema },
    ]),
    GroupItemModule,
    forwardRef(() => StoreModule),
  ],
  controllers: [ItemsController],
  providers: [ItemsService],
  exports: [ItemsService],
})
export class ItemsModule {}
