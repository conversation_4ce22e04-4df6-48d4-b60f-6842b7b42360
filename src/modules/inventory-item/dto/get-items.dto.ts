import { PartialType } from '@nestjs/mapped-types';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { CreateItemDto } from './create-item.dto';
import { PaginationDto } from '../../../utils/dto/pagination.dto';
import { itemType } from '../types/item-types.enum';

export class GetItemDto extends PartialType(CreateItemDto) {
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  public id: number;
}

export class GetItemsPaginationDto extends PaginationDto {
  @IsString()
  @IsOptional()
  code?: string;

  @IsEnum(itemType)
  @IsOptional()
  type?: itemType = itemType.goods;
}
