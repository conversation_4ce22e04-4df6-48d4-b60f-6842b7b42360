import { IsNotEmpty, IsString } from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { itemType } from '../types/item-types.enum';
import { Transform } from 'class-transformer';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';

export class GetItemDto {
  @ApiProperty({ type: 'string', required: true })
  @IsString()
  @IsNotEmpty()
  public id: string;

  @IsEnum(itemType)
  @IsOptional()
  type?: itemType = itemType.goods;

  @ApiProperty({
    description: 'store of item ',
    example: '1',
    required: true,
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  store: string;
}
