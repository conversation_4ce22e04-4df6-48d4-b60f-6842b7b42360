import { IsNotEmpty } from 'class-validator';
import { PaginationDto } from '../../../utils/dto/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';

export class GetAbstractItemsPaginationDto extends PaginationDto {
  @ApiProperty({
    description: 'store of item ',
    example: '1',
    required: true,
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  store: string;
}
