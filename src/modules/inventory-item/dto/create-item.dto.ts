import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  Validate,
  ValidateNested,
} from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsBoolean, IsNumber } from '@nestjs/class-validator';
import { UnitAndPriceDto } from './unit-price.dto';
import {
  NameDto,
  validateLanguage,
  validateEnLanguage,
} from '../../../utils/dto/name.dto';
import { toLowerCaseObjectKeys } from '../../../utils/tolowerCaseObjectKeys';
import { itemType } from '../types/item-types.enum';
import { hasOneDefaultValue } from '../../../utils/decorators/has-one-default-value';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';
import { Types } from 'mongoose';

export class CreateItemDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'unique code for the new item',
    example: 't-12',
    required: true,
  })
  @IsString()
  code: string;

  @ApiProperty({ type: NameDto })
  @Transform((param) => toLowerCaseObjectKeys(param.value))
  @Validate(validateLanguage)
  @Validate(validateEnLanguage)
  name: NameDto;

  @ApiProperty({
    description: 'item type',
    required: true,
    enum: itemType,
  })
  @IsOptional()
  @IsEnum(itemType)
  type?: itemType = itemType.goods;

  @ApiProperty({
    description: 'store of item ',
    example: '1',
    required: true,
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  store: Types.ObjectId;

  @ApiProperty({
    description: 'item group id for this item ',
    example: 1,
    required: false,
  })
  @IsString()
  @IsOptional()
  item_group?: number;

  @ApiProperty({
    description: 'units of item ',
    example: [
      {
        unit: 1,
        is_default: false,
        barcode1: '123',
        barcode2: '',
        sales_price: 0,
        wholesale_price: 0,
        special_price: 0,
        quantity: 10,
        avg_cost: 0.5,
      },
    ],
    required: true,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => UnitAndPriceDto)
  @hasOneDefaultValue({
    message: 'Only one unit can have is_default set to true.',
  })
  units?: UnitAndPriceDto[];

  @ApiProperty({
    description: 'effect on sale',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === true)
  tax_on_sales: boolean;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  average_cost_in_base_unit: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  max_quantity_in_base_unit: number;
}
