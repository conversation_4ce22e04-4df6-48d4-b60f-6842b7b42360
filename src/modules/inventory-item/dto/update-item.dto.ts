import { CreateItemDto } from './create-item.dto';
import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  IsBoolean,
  IsString,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { Types } from 'mongoose';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';

export class ItemVendorDto {
  @ApiProperty()
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  vendor_i?: Types.ObjectId;

  @ApiProperty()
  @IsString()
  catalog_part_no: string;

  @ApiProperty()
  @IsBoolean()
  @Transform(({ value }) => value === true)
  preferred: boolean;
}

export class UpdateItemDto extends PartialType(CreateItemDto) {
  @ApiProperty()
  @IsOptional()
  @ValidateNested()
  @Type(() => ItemVendorDto)
  vendors?: ItemVendorDto[];
}
