import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateIf,
} from 'class-validator';

export class UnitAndPriceDto {
  @ApiProperty({
    description: 'unit of inventory units',
    example: 1,
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  unit: number;

  @ApiProperty({ required: false })
  @ValidateIf((o) => o.active)
  @Transform(({ value }) => value === true)
  @IsBoolean()
  @IsOptional()
  is_default?: boolean = false;

  @ApiProperty()
  @IsString()
  barcode1: string = '';

  @ApiProperty({ required: false })
  @ValidateIf((o) => o.barcode2)
  @IsString()
  @IsOptional()
  barcode2?: string = '';

  @ApiProperty({ required: false })
  @ValidateIf((o) => o.sales_price)
  @IsOptional()
  @IsNumber()
  sales_price?: number;

  @ApiProperty({ required: false })
  @ValidateIf((o) => o.wholesale_price)
  @IsOptional()
  @IsNumber()
  wholesale_price?: number;

  @ApiProperty({ required: false })
  @ValidateIf((o) => o.special_price)
  @IsOptional()
  @IsNumber()
  special_price?: number;

  @ApiProperty({ required: false })
  @ValidateIf((o) => o.quantity)
  @IsOptional()
  @IsNumber()
  quantity: number;

  @ApiProperty({ required: false })
  @ValidateIf((o) => o.avg_cost)
  @IsOptional()
  @IsNumber()
  avg_cost: number;
}
