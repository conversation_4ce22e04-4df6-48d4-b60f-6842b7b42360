import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiHeader,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { CreateItemDto } from './dto/create-item.dto';
import { GetItemsPaginationDto } from './dto/get-items.dto';
import { GetItemDto } from './dto/get-item.dto';
import { ItemsListResponseDto } from './dto/items-list-response.dto';
import { UpdateItemDto } from './dto/update-item.dto';
import { Item } from './schema/item.schema';
import { ItemsService } from './items.service';
import { RequestWithUser } from '../auth/interfaces/authorize.interface';
import { CaslGuard } from '../casl/guards/casl.guard';
import { Abilities } from '../casl/guards/abilities.decorator';
import { authType } from '../auth/guards/auth-type.decorator';
import { GetAbstractItemsPaginationDto } from './dto/get-abstract-items.dto';
import { DeleteExceptIdsDto } from '../../utils/dto/delete-except-ids.dto';

@ApiTags('items')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@Controller('inventory/items')
export class ItemsController {
  constructor(private readonly inventoryService: ItemsService) {}

  @Post()
  @authType('apiKey')
  @ApiBearerAuth('TOKEN')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'create', subject: 'item' })
  @ApiBody({ type: [CreateItemDto] })
  create(@Body() createInventoryDto: CreateItemDto[]): Promise<any> {
    return this.inventoryService.createBulk(createInventoryDto);
  }

  @Get()
  @authType('apiKey')
  @ApiBearerAuth('TOKEN')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'item' })
  findAll(
    @Req() request: RequestWithUser,
    @Query() query: GetItemsPaginationDto,
  ): Promise<ItemsListResponseDto> {
    return this.inventoryService.findAll(request, query);
  }

  @Get('abstract')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'abstract_items' })
  abstract(@Query() query: GetAbstractItemsPaginationDto) {
    return this.inventoryService.abstract(query);
  }

  @Get('max_number')
  maxNumber() {
    return this.inventoryService.findMaxNumber();
  }

  @Get('one')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'read', subject: 'item' })
  findOne(@Query() query: GetItemDto): Promise<Item & { id: number }> {
    return this.inventoryService.findOne(query.id, query.type, query.store);
  }

  @Patch(':id')
  @ApiParam({ name: 'id' })
  @UseGuards(CaslGuard)
  @Abilities({ action: 'edit', subject: 'item' })
  update(
    @Req() request: RequestWithUser,
    @Param('id') id: number,
    @Body() updateInventoryDto: UpdateItemDto,
  ): Promise<Item & { id: number }> {
    return this.inventoryService.update(request, id, updateInventoryDto);
  }

  @Delete()
  @UseGuards(CaslGuard)
  @Abilities({ action: 'delete', subject: 'item' })
  remove(@Body() dto: DeleteExceptIdsDto): Promise<any> {
    return this.inventoryService.remove(dto.ids);
  }
}
