import { HttpException, Inject, Injectable } from '@nestjs/common';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import { User, UserDocument } from 'src/modules/users/schemas/user.schema';
import { ForgetPasswordDto, ValidateOtpDto } from './dto/forgot-password.dto';
import { LoginDto } from './dto/login.dto';
import { Response } from 'express';
import { TenantServices } from '../tenants/tenant.service';
import { ConfigService } from '@nestjs/config';
import { cookieOptionsCreator } from './constants';
import { userCriticalProps } from '../users/schemas/enums';
import { BranchService } from '../branch/branch.service';
import { Connection, Types } from 'mongoose';
import { Tenant } from '../tenants/schemas/tenant.schema';
import { MongooseConfigService } from '../database/mongoose.service';
import DataBases from '../database/database.provider';
import { nameOf } from '../../utils/object-key-name';
import { usersExceptionCode } from '../../exceptions/exception-code.users';
import { generalExceptionCode } from '../../exceptions/exception-code.general';
import { RequestWithUser } from './interfaces/authorize.interface';
import * as jwt from 'jsonwebtoken';
import { parseCookie } from './strategies/aggregate-by-tenant-context-id.strategy';
import { UserBranchesRolesService } from '../user-branches-roles/user-branches-roles.service';
import { generateToken } from '../../utils/refresh-token';
import { CompanyService } from '../company/company.service';

export interface UserCustom extends UserDocument {
  tenant: Tenant;
}

@Injectable()
export class AuthService {
  constructor(
    private readonly usersService: UsersService,
    private readonly configService: ConfigService,
    private tenantService: TenantServices,
    private jwtService: JwtService,
    private companyService: CompanyService,
    @Inject(BranchService) private branchService: BranchService,
    @Inject(MongooseConfigService)
    private readonly mongooseConfigService: MongooseConfigService,
    private readonly userBranchesRolesService: UserBranchesRolesService,
  ) {}

  async getOneById(_id, allow = false): Promise<User> {
    const userCriticalPropsarr = [];
    if (allow) {
      userCriticalPropsarr.push(userCriticalProps.secret);
    }
    return await this.usersService.findOneUser({ _id }, userCriticalPropsarr);
  }

  async getOneByEmail(email: string): Promise<UserCustom> {
    const data = await this.tenantService.getTenantUser({ user_email: email });
    if (!data) {
      throw new HttpException(
        nameOf(usersExceptionCode, (x) => x.userNotFound),
        usersExceptionCode.userNotFound,
      );
    }

    const userData = await this.usersService.findOneUser(
      { email },
      [userCriticalProps.password, userCriticalProps.secret],
      'role',
    );

    const response = userData as any;
    return response;
  }

  async updateUserLastActivity(id: Types.ObjectId) {
    const user = await this.usersService.findOneUser({ _id: id });
    user.last_activity = new Date();
    await user.save();
  }

  async getOneByEmailTenant(email: string): Promise<any> {
    const data = await this.tenantService.getTenantUser({ user_email: email });
    if (!data) {
      throw new HttpException(
        nameOf(usersExceptionCode, (x) => x.userNotFound),
        usersExceptionCode.userNotFound,
      );
    }
    return data;
  }

  async getOneByEmailWithConnection(
    email: string,
    connection: Connection,
  ): Promise<UserCustom> {
    const userData = await this.usersService.findOneWithCustomConnection(
      { email },
      [userCriticalProps.password, userCriticalProps.secret],
      connection,
    );
    return userData as any;
  }

  async generateOtp(email: string): Promise<boolean> {
    const data = await this.tenantService.getTenantUser({ user_email: email });
    if (!data) {
      return false;
    }
    const dbConnection = await this.newDbConnection(data.tenant.code);
    const userData = await this.getOneByEmailWithConnection(
      email,
      dbConnection,
    );
    if (userData) {
      userData.otp = {
        value: String(Math.floor(100000 + Math.random() * 900000)),
        period: 12,
      };
      await userData.save();
      return true;
    }
    return false;
  }

  async forgetPassword(body: ForgetPasswordDto): Promise<boolean> {
    const { email, password, otp } = body;
    const data = await this.tenantService.getTenantUser({ user_email: email });

    const dbConnection = await this.newDbConnection(data.tenant.code);
    const userData = await this.getOneByEmailWithConnection(
      email,
      dbConnection,
    );
    if (userData.otp) {
      if (userData.otp?.retries > 5) {
        userData.otp = undefined;
        await userData.save();
        throw new HttpException(
          nameOf(usersExceptionCode, (x) => x.otpExpired),
          usersExceptionCode.otpExpired,
        );
      }
      if (userData.otp?.value == otp) {
        userData.password = password;
        userData.otp = undefined;
        await userData.save();
        return true;
      } else {
        userData.otp.retries = (userData.otp.retries || 0) + 1;
        await userData.save();
        throw new HttpException(
          nameOf(usersExceptionCode, (x) => x.invalidOtp),
          usersExceptionCode.invalidOtp,
        );
      }
    }
    throw new HttpException(
      nameOf(usersExceptionCode, (x) => x.invalidOtp),
      usersExceptionCode.invalidOtp,
    );
  }

  async verify(request: RequestWithUser) {
    let userId = null,
      userObj = null,
      branches = null;
    if (
      request.headers &&
      parseCookie(request.headers['cookie'])?.access_token
    ) {
      const accessToken: any = jwt.decode(
        parseCookie(request.headers['cookie']).access_token,
      );
      if (accessToken) {
        userId = accessToken.user_id;
      }
    }
    const user = await this.usersService.findOneUser({ _id: userId });
    if (user) {
      userObj = user.toObject();
      userObj.branches = await this.userBranchesRolesService.getByUser(
        user._id,
      );
      branches = userObj.branches.map((branch) => ({
        _id: branch._id,
        name: branch.general_information.name,
      }));
    } else {
      throw new HttpException(
        nameOf(generalExceptionCode, (x) => x.unauthorized),
        generalExceptionCode.unauthorized,
      );
    }

    return { user_obj: userObj, branches };
  }

  async mapperVerify(request: RequestWithUser, branch: Types.ObjectId) {
    const rs = await this.verify(request);
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const { user_obj, ...rest } = rs;

    let branchId;
    let userBranchesRoles = await this.userBranchesRolesService.findAll(
      rs.user_obj._id,
    );
    if (branch) {
      branchId = String(branch);
      await this.usersService.update(request, {
        _id: rs.user_obj._id,
        email: rs.user_obj.email,
        active_branch: branch,
      });
    } else if (rs.user_obj.active_branch) {
      branchId = rs.user_obj.active_branch;
    } else {
      branchId = userBranchesRoles[0]?.branch?._id;
    }
    userBranchesRoles = userBranchesRoles.filter(
      (userBranchesRole) =>
        String(userBranchesRole?.branch?._id) === String(branchId),
    );
    const mandatoryPromises = [
      this.branchService.findOne({ _id: branchId }),
      this.companyService.get(),
    ];
    const [activeBranch, companyInfo] = await Promise.all(mandatoryPromises);

    const onboarding = {
      branch: activeBranch !== null ? true : false,
      store: null,
      accounting_tree: null,
      company: !!Object.keys(companyInfo).length,
    };
    const userObj = { name: user_obj.name, _id: user_obj._id };
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const company_info = {
      name: (companyInfo as any).name,
      short_name: (companyInfo as any).short_name,
      settings: companyInfo.settings,
    };
    const activeBranchFiltered = {
      _id: activeBranch?._id,
      general_information: (activeBranch as any)?.general_information,
      document: (activeBranch as any)?.document,
    };

    return {
      ...rest,
      branch: activeBranchFiltered,
      company_info,
      onboarding,
      user_branches_roles: userBranchesRoles,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      userObj,
    };
  }

  async validateOtp(body: ValidateOtpDto): Promise<boolean | User> {
    const { email, otp } = body;
    const data = await this.tenantService.getTenantUser({ user_email: email });
    if (!data) {
      return false;
    }
    const dbConnection = await this.newDbConnection(data.tenant.code);
    const userData = await this.getOneByEmailWithConnection(
      email,
      dbConnection,
    );
    if (userData.otp) {
      if (userData.otp?.retries > 5) {
        userData.otp = undefined;
        await userData.save();
        throw new HttpException(
          nameOf(usersExceptionCode, (x) => x.otpExpired),
          usersExceptionCode.otpExpired,
        );
      }
      if (userData && userData.otp?.value == otp) {
        return userData;
      } else {
        userData.otp.retries = (userData.otp.retries || 0) + 1;
      }
      await userData.save();
      throw new HttpException(
        nameOf(usersExceptionCode, (x) => x.invalidOtp),
        usersExceptionCode.invalidOtp,
      );
    }
    throw new HttpException(
      nameOf(usersExceptionCode, (x) => x.invalidOtp),
      usersExceptionCode.invalidOtp,
    );
  }

  async tokenGenerator(
    user_email,
    userObj?: UserDocument,
    branch?: any[],
  ): Promise<{
    // the response of this function is token that we send to user
    // eslint-disable-next-line @typescript-eslint/naming-convention
    access_token: string;
    // eslint-disable-next-line @typescript-eslint/naming-convention
    refresh_token: string;
  }> {
    const tenantUser = await this.tenantService.getTenantUser({ user_email });

    return {
      access_token: this.jwtService.sign({
        tenant_user: tenantUser._id,
        user_id: userObj._id,
        code: tenantUser.tenant.code,
        branch,
      }),
      refresh_token: generateToken(
        {
          tenant_user: tenantUser._id,
          user_id: userObj._id,
          code: tenantUser.tenant.code,
        },
        userObj.secret,
        this.configService.get('REFRESH_TOKEN_EXPIRE'),
      ),
    };
  }

  private async newDbConnection(code): Promise<Connection> {
    const liveConnections = await DataBases.getConnection(
      this.mongooseConfigService.createDbUrl(code),
    );
    return liveConnections;
  }

  async login(userCriditionals: LoginDto): Promise<{
    // eslint-disable-next-line @typescript-eslint/naming-convention
    access_token: string;
    // eslint-disable-next-line @typescript-eslint/naming-convention
    refresh_token: string;
    // eslint-disable-next-line @typescript-eslint/naming-convention
    user_obj: UserDocument;
    branch: any;
  }> {
    try {
      const data = await this.tenantService.getTenantUser({
        user_email: userCriditionals.email,
      });

      if (!data) {
        throw new HttpException(
          nameOf(usersExceptionCode, (x) => x.userNotFound),
          usersExceptionCode.userNotFound,
        );
      }
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const DbConnection = await this.newDbConnection(data.tenant.code);
      let user: any = await this.getOneByEmailWithConnection(
        userCriditionals.email,
        DbConnection,
      );

      if (!user)
        throw new HttpException(
          nameOf(usersExceptionCode, (x) => x.userNotFound),
          usersExceptionCode.userNotFound,
        );
      user.tenant = data.tenant;

      if (user.tenant.activation_status === false) {
        throw new HttpException(
          nameOf(usersExceptionCode, (x) => x.tenantNotFound),
          usersExceptionCode.tenantNotFound,
        );
      }
      if (!user.active)
        throw new HttpException(
          nameOf(usersExceptionCode, (x) => x.accountNotFound),
          usersExceptionCode.accountNotFound,
        );
      const rs = await user.comparePassword(userCriditionals.password);

      if (rs) {
        user = user.toObject();
        const branchData =
          await this.branchService.localFindAllRawWithConnection(
            {},
            DbConnection,
          );
        const token = await this.tokenGenerator(user.email, user, branchData);
        // eslint-disable-next-line @typescript-eslint/naming-convention
        return { ...token, user_obj: user, branch: branchData };
      }
      throw new HttpException(
        nameOf(generalExceptionCode, (x) => x.unauthorized),
        generalExceptionCode.unauthorized,
      );
    } catch (error) {
      console.log(error);
      throw new HttpException(
        nameOf(generalExceptionCode, (x) => x.incorrectCredentials),
        generalExceptionCode.incorrectCredentials,
      );
    }
  }

  async mapperLogin(body: LoginDto) {
    const rs = await this.login(body);
    let branchId;

    const data = await this.tenantService.getTenantUser({
      user_email: body.email,
    });
    const code = data.tenant?.code;

    const userBranchesRoles = await this.userBranchesRolesService.findAll(
      rs.user_obj._id,
      code,
    );
    if (rs.user_obj.active_branch) {
      branchId = rs.user_obj.active_branch;
    } else {
      branchId = userBranchesRoles[0]?.branch?._id;
    }
    const mandatoryPromises = [
      this.branchService.findOneWithConnection({ _id: branchId }, code),
      this.companyService.get(),
    ];
    const [activeBranch, companyInfo] = await Promise.all(mandatoryPromises);

    const onboarding = {
      branch: activeBranch !== null ? true : false,
      store: null,
      accounting_tree: null,
      company: !!Object.keys(companyInfo).length,
    };
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const company_info = {
      name: (companyInfo as any).name,
      short_name: (companyInfo as any).short_name,
      settings: companyInfo.settings,
    };
    const activeBranchFiltered = {
      _id: activeBranch?._id,
      general_information: (activeBranch as any)?.general_information,
    };
    return {
      ...rs,
      active_branch: activeBranchFiltered,
      company_info,
      user_branches_roles: userBranchesRoles,
      onboarding,
    } as any;
  }

  async logOut(response: Response) {
    const domain = this.configService.get('BASE_URL')[0];
    const env = this.configService.get('NODE_ENV');
    const servicePath = this.configService.get('APP_URL_PREFIX');
    response.clearCookie(
      'access_token',
      cookieOptionsCreator(env, domain, true),
    );
    response.clearCookie('user', cookieOptionsCreator(env, domain, false));
    response.clearCookie(
      'refresh_token',
      cookieOptionsCreator(env, domain, true, servicePath),
    );
  }

  async getBranches(): Promise<any> {
    return await this.branchService.localFindAllRaw({});
  }

  /**
   * gets user id,role, permission and create a token for it
   */
  async authenticateServiceUser(body: {
    code: number;
    userId: Types.ObjectId;
  }): Promise<{ access: 'denied' | 'allowed' }> {
    const { userId } = body;

    const user = await this.usersService.findOneUser(
      {
        _id: userId,
      },
      [],
    );
    if (user) {
      return { access: 'allowed' };
    }
    return { access: 'denied' };
  }
}
