import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtAuthGuard } from './jwt-auth.guard';
import { ApiKeyAuthGuard } from './api-token.guard';
import { authTypeName } from './auth-type.decorator';

@Injectable()
export class CombinedAuthGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private jwtGuard: JwtAuthGuard,
    private apiKeyGuard: ApiKeyAuthGuard,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const authType = this.reflector.get<string>(
      authTypeName,
      context.getHandler(),
    );

    if (authType === 'jwt' || !authType) {
      return !!(await this.jwtGuard.canActivate(context));
    }

    if (authType === 'apiKey') {
      return !!(await this.apiKeyGuard.canActivate(context));
    }

    if (authType === 'both') {
      return (
        !!(await this.apiKeyGuard.canActivate(context)) ||
        !!(await this.jwtGuard.canActivate(context))
      );
    }
    return false;
  }
}
