import { ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';

@Injectable({})
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(private readonly reflector: Reflector) {
    super(reflector);
  }
  canActivate(context: ExecutionContext) {
    const isPublic = this.reflector.get<boolean>(
      'isPublic',
      context.getHandler(),
    );
    const isPublicRpc = this.reflector.get<boolean>(
      'isPublicRpc',
      context.getHandler(),
    );
    if (isPublic || isPublicRpc) {
      return true;
    }
    return super.canActivate(context);
  }
}
