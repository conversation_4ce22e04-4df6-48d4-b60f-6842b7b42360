import { Injectable, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';
import { authTypeName } from './auth-type.decorator';
@Injectable()
export class ApiKeyAuthGuard extends AuthGuard('api-key') {
  constructor(private reflector: Reflector) {
    super();
  }

  canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();
    const apiKey = request.headers['authorization'];
    const authType = this.reflector.get<string>(
      authTypeName,
      context.getHandler(),
    );

    if (!apiKey || authType === 'jwt') {
      return false;
    }

    return super.canActivate(context);
  }
}
