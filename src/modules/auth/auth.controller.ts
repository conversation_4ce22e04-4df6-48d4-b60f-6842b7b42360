import {
  Body,
  Controller,
  Get,
  HttpException,
  Post,
  Query,
  Req,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import { AuthService } from './auth.service';
import { ForgetPasswordDto, ValidateOtpDto } from './dto/forgot-password.dto';
import { OtpDto } from './dto/otp.dto';
import { ConfigService } from '@nestjs/config';
import { PublicRoute } from '../casl/guards/public-route.decorator';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { ServiceAuthenticationResponse } from './interfaces/serviceAuthentication.interface';
import { nameOf } from '../../utils/object-key-name';
import { usersExceptionCode } from '../../exceptions/exception-code.users';
import { RequestWithUser } from './interfaces/authorize.interface';
import { LoginDto } from './dto/login.dto';
import { LoginResponse } from './dto/login-response.dto';
import { cookieOptionsCreator } from './constants';
import { VerifyDto } from './dto/verify.dto';

@ApiTags('Auth')
@Controller('users/')
export class AuthClass {
  constructor(
    private authService: AuthService,
    private configService: ConfigService,
  ) {}

  @Post('login')
  @PublicRoute()
  async login(
    @Body() body: LoginDto,
    @Res({ passthrough: true }) response: Response,
  ): Promise<LoginResponse> {
    const rs = await this.authService.mapperLogin(body);
    const domain = this.configService.get('BASE_URL')[0];
    const env = this.configService.get('NODE_ENV');
    const servicePath = this.configService.get('APP_URL_PREFIX');
    response.cookie(
      'access_token',
      rs.access_token,
      cookieOptionsCreator(env, domain, true),
    );
    response.cookie(
      'user',
      {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        userObj: {
          name: rs.user_obj.name,
          email: rs.user_obj.email,
          _id: String(rs.user_obj._id),
        },
        branch: rs.branch,
      },
      cookieOptionsCreator(env, domain, false),
    );
    response.cookie(
      'refresh_token',
      rs.refresh_token,
      cookieOptionsCreator(env, domain, true, servicePath),
    );
    // eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/no-unused-vars
    const {
      // eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/no-unused-vars
      access_token,
      // eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/no-unused-vars
      refresh_token,
      branch,
      activeBranch,
      // eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/no-unused-vars
      user_obj,
      ...rest
    } = rs;
    const branches = branch.map((branch) => ({
      id: branch._id,
      name: branch.general_information.name,
    }));
    if (rs.user_obj.secret) {
      delete user_obj.secret;
    }
    if (rs.user_obj.password) {
      delete user_obj.password;
    }
    const userObj = { name: user_obj.name, _id: user_obj._id };

    const res = {
      ...rest,
      branch: activeBranch,
      branches,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      userObj,
    };

    return res;
  }

  @Get('logout')
  @PublicRoute()
  logout(@Res({ passthrough: true }) response: Response) {
    this.authService.logOut(response);
    return true;
  }

  @Get('verify')
  @PublicRoute()
  verify(@Req() request: RequestWithUser, @Query() verifyDto: VerifyDto) {
    return this.authService.mapperVerify(request, verifyDto.branchId);
  }

  @Post('forget-password')
  @PublicRoute()
  @ApiOkResponse({
    type: Boolean,
  })
  async forgetPassword(@Body() body: ForgetPasswordDto) {
    const rs = await this.authService.forgetPassword(body);
    if (!rs) {
      throw new HttpException(
        nameOf(usersExceptionCode, (x) => x.invalidOtp),
        usersExceptionCode.invalidOtp,
      );
    }
    return rs;
  }

  @Post('request-otp')
  @PublicRoute()
  async requestOtp(@Body() body: OtpDto) {
    await this.authService.generateOtp(body.email);
    return 'if you enter information correctly you should recived otp';
  }

  @Post('validate-otp')
  @PublicRoute()
  validateOtp(@Body() body: ValidateOtpDto) {
    return this.authService.validateOtp(body);
  }

  @ApiOkResponse({
    type: Boolean,
  })
  @Post('refresh-token')
  refreshToken() {
    return true;
  }

  @Post('service-auth')
  @PublicRoute()
  async serviceAuthentication(
    @Body() body,
  ): Promise<ServiceAuthenticationResponse> {
    return await this.authService.authenticateServiceUser(body);
  }
}
