import {
  HttpException,
  Injectable,
  Scope,
  UnauthorizedException,
} from '@nestjs/common';
import { UsersProfileService } from '../users-profile/users-profile.service';
import { RequestWithUser } from './interfaces/authorize.interface';
import * as jwt from 'jsonwebtoken';
import { generalExceptionCode } from '../../exceptions/exception-code.general';
import { nameOf } from '../../utils/object-key-name';

@Injectable({ scope: Scope.REQUEST })
export class ApiKeyValidator {
  constructor(private readonly usersProfileService: UsersProfileService) {}

  async validate(apiKey: string, req: RequestWithUser): Promise<any> {
    const user = jwt.decode(apiKey) as any;
    if (!user) {
      throw new HttpException(
        nameOf(generalExceptionCode, (x) => x.unauthorized),
        generalExceptionCode.unauthorized,
      );
    }

    const rs = await this.usersProfileService.validateApiToken(
      user.user_id,
      apiKey,
    );

    const jwtDec = apiKey.split('.');
    const jwtStr = `${jwtDec[0]}.${jwtDec[1]}.`;
    if (rs?.api_token !== jwtStr) {
      throw new UnauthorizedException();
    }

    req.user = user;
    return user;
  }
}
