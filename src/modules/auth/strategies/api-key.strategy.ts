import { Injectable, OnModuleInit } from '@nestjs/common';
import { RequestWithUser } from '../interfaces/authorize.interface';
import { HeaderAPIKeyStrategy } from 'passport-headerapikey';
import { ContextIdFactory, ModuleRef } from '@nestjs/core';
import { ApiKeyValidator } from '../api-key-validator.service';
import * as passport from 'passport';
import { Request } from 'express';

@Injectable()
export class HeaderApiKeyStrategy implements OnModuleInit {
  constructor(private readonly moduleRef: ModuleRef) {}
  onModuleInit() {
    passport.use(
      'api-key',
      new HeaderAPIKeyStrategy(
        { header: 'authorization', prefix: 'Bearer ' },
        true,
        async (
          apiKey: string,
          verified: (err: Error | null, user?: any) => void,
          req: Request,
        ) => {
          try {
            const request = req as unknown as RequestWithUser;
            const contextId = ContextIdFactory.getByRequest(request);
            const validator = await this.moduleRef.resolve(
              ApiKeyValidator,
              contextId,
            );
            const user = await validator.validate(apiKey, request);
            verified(null, user);
          } catch (error) {
            //dont touch this plz
            verified(error);
          }
        },
      ),
    );
  }
}
