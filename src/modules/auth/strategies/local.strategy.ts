import { HttpException, Injectable } from '@nestjs/common';
import { ContextIdFactory, ModuleRef } from '@nestjs/core';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';
import { AuthService } from '../auth.service';
import { nameOf } from '../../../utils/object-key-name';
import { generalExceptionCode } from '../../../exceptions/exception-code.general';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private moduleRef: ModuleRef) {
    super({ usernameField: 'email', passReqToCallback: true });
  }

  async validate(
    request: Request,
    email: string,
    password: string,
  ): Promise<any> {
    const contextId = ContextIdFactory.getByRequest(request);
    const authService = await this.moduleRef.resolve(AuthService, contextId);
    const user = await authService.login({
      email,
      password,
    });
    if (!user) {
      throw new HttpException(
        nameOf(generalExceptionCode, (x) => x.unauthorized),
        generalExceptionCode.unauthorized,
      );
    }
    return user;
  }
}
