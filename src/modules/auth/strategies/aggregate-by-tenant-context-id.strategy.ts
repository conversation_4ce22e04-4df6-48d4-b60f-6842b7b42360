import { HttpException } from '@nestjs/common';
import {
  HostComponentInfo,
  ContextId,
  ContextIdFactory,
  ContextIdStrategy,
} from '@nestjs/core';
import { Request } from 'express';
import * as jwt from 'jsonwebtoken';
import { generalExceptionCode } from '../../../exceptions/exception-code.general';
import { nameOf } from '../../../utils/object-key-name';

export enum requestType {
  http = 'http',
  rpc = 'rpc',
}

const tenants = new Map<string, ContextId>();
export const parseCookie = (str) => {
  try {
    return str
      .split(';')
      .map((v) => v.split('='))
      .reduce((acc, v) => {
        acc[decodeURIComponent(v[0].trim())] = decodeURIComponent(v[1].trim());
        return acc;
      }, {});
  } catch {
    return false;
  }
};

function getTokenFromRequest(req: Request): string | null {
  const authHeader = req.headers['authorization'];
  // Check if header exists
  if (!authHeader) {
    return null;
  }
  // Split by space and take the second part
  const parts = authHeader.split(' ');
  if (parts.length === 2 && parts[0] === 'Bearer') {
    return parts[1];
  }

  return null;
}

export class AggregateByTenantContextIdStrategy implements ContextIdStrategy {
  attach(contextId: ContextId, request: Request) {
    let code,
      type = requestType.http;
    if (request['context']) {
      type = requestType.rpc;
    }
    if (type === requestType.rpc && +(<any>request)?.data?.code >= 0) {
      code = (<any>request).data.code;
    } else if (type === requestType.http) {
      if (
        (request.headers &&
          parseCookie(request.headers['cookie'])?.access_token) ||
        getTokenFromRequest(request)
      ) {
        const accessToken: any = jwt.decode(
          parseCookie(request.headers['cookie']).access_token ||
            getTokenFromRequest(request),
        );
        if (accessToken) {
          code = accessToken.code;
        }
      } else {
        console.log('http request without token');
        code = -1;
      }
    } else {
      console.log('no context found');
      throw new HttpException(
        nameOf(generalExceptionCode, (x) => x.badRequest),
        generalExceptionCode.badRequest,
      );
    }
    console.log('reqest context: ', code);
    const tenantId = code;
    let tenantSubTreeId: ContextId;

    if (tenants.has(tenantId)) {
      tenantSubTreeId = tenants.get(tenantId);
    } else {
      tenantSubTreeId = ContextIdFactory.create();
      tenants.set(tenantId, tenantSubTreeId);
    }

    // If tree is not durable, return the original "contextId" object
    return {
      resolve: (info: HostComponentInfo) =>
        info.isTreeDurable ? tenantSubTreeId : contextId,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      payload: { tenantId },
    };
  }
}
