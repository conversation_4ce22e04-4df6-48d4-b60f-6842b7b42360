import { ApiProperty } from '@nestjs/swagger';
import { Branch } from 'src/modules/branch/schemas/branch.schema';
import { User } from 'src/modules/users/schemas/user.schema';

export class LoginResponse {
  @ApiProperty({})
  access_token: string;

  @ApiProperty({})
  refresh_token: string;

  @ApiProperty({ type: User })
  userObj: User;

  @ApiProperty({ type: Branch, isArray: true })
  branch: Branch[];
}
