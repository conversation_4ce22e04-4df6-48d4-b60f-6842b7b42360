import { IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';
import { Types } from 'mongoose';

export class VerifyDto {
  @ApiProperty({
    description: 'active branch id',
    example: '64956ef5f506e9edf21df4e8',
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  public branchId?: Types.ObjectId;
}
