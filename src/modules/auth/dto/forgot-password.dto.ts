import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEmail, IsNotEmpty, IsString } from 'class-validator';

export class ForgetPasswordDto {
  @IsNotEmpty()
  @Transform(({ value }: { value: string }) => value.toLowerCase())
  @IsEmail()
  @ApiProperty({
    description: 'emial of the user',
    example: '<EMAIL>',
    required: true,
  })
  public email: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'password of the user',
    example: 'asdadfdgdfgdg',
    required: true,
  })
  public password: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'otp code of the user',
    example: '858585',
    required: true,
  })
  public otp: string;
}

export class ValidateOtpDto {
  @IsEmail()
  @Transform(({ value }: { value: string }) => value.toLowerCase())
  @IsNotEmpty()
  @ApiProperty({
    description: 'emial of the user',
    example: '<EMAIL>',
    required: true,
  })
  public email: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'otp code of the user',
    example: '858585',
    required: true,
  })
  public otp: string;
}
