import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { BranchModule } from '../branch/branch.module';
import { UsersModule } from '../users/users.module';
import { AuthClass } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { DatabaseModule } from '../database/database.module';
import { UserBranchesRolesModule } from '../user-branches-roles/user-branch-role.module';
import { HeaderApiKeyStrategy } from './strategies/api-key.strategy';
import { UsersProfileModule } from '../users-profile/users-profile.module';
import { UsersProfileService } from '../users-profile/users-profile.service';
import { ApiTokenModule } from '../api-token/api-token.module';
import { CompanyModule } from '../company/company.module';
import { ApiKeyValidator } from './api-key-validator.service';
import { MinioClientModule } from '../minio/minio-client.module';

@Module({
  imports: [
    BranchModule,
    UsersModule,
    PassportModule,
    DatabaseModule,
    UsersProfileModule,
    ApiTokenModule,
    CompanyModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        return {
          privateKey: configService.get('JWT_PK'),
          publicKey: configService.get('JWT_PUBKEY'),
          signOptions: {
            expiresIn: `${configService.get('ACCESS_TOKEN_EXPIRE')}s`,
            issuer: 'authService',
            algorithm: 'RS256',
          },
        };
      },
    }),
    UserBranchesRolesModule,
    MinioClientModule,
  ],
  controllers: [AuthClass],
  providers: [
    AuthService,
    ApiKeyValidator,
    LocalStrategy,
    JwtStrategy,
    HeaderApiKeyStrategy,
    UsersProfileService,
  ],
  exports: [AuthService],
})
export class AuthModule {}
