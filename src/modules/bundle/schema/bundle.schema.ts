import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose, { Types } from 'mongoose';
import { NameDto } from '../../../utils/dto/name.dto';
import { bundleType } from '../types/bundle-types.enum';
import { BundleItem, bundleItemSchema } from './bundle-item.schema';

@Schema({ timestamps: true })
export class Bundle {
  @Prop({ type: Number, required: true, unique: true })
  id: number;

  @Prop({ type: String, required: true, unique: true })
  code: string;

  @Prop({ type: NameDto, required: true })
  name: NameDto;

  @Prop({ type: Boolean, default: false })
  nested_bundles: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: true })
  status: boolean;

  @ApiProperty()
  @Prop({ type: String, enum: bundleType, default: bundleType.simple })
  type: bundleType;

  @Prop({
    type: [bundleItemSchema],
    default: [],
  })
  bundle_items: BundleItem[];

  @Prop({ type: String, required: false })
  description?: string;

  @Prop({ type: Number, required: true, min: 0 })
  bundle_price: number;

  @Prop({ type: Number, required: true, min: 0 })
  original_price: number;

  @Prop({ type: Number, required: true, min: 0 })
  discount_percentage: number;
}

export const bundleSchema = SchemaFactory.createForClass(Bundle);
