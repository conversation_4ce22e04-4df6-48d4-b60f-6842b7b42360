import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose, { Types } from 'mongoose';

@Schema({ _id: false })
export class BundleItem {
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Item',
    required: true,
  })
  item: Types.ObjectId;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Unit',
    required: true,
  })
  unit: Types.ObjectId;

  @Prop({ type: Number, required: true, min: 1 })
  qty: number;

  @Prop({ type: Number, required: true, min: 0 })
  price: number;

  @Prop({ type: Number, required: true, min: 0 })
  total: number;
}

export const bundleItemSchema = SchemaFactory.createForClass(BundleItem);
