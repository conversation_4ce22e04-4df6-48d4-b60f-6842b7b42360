import { Validate } from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Min,
  ValidateNested,
} from 'class-validator';
import { Types } from 'mongoose';
import {
  NameDto,
  validateEnLanguage,
  validateLanguage,
} from '../../../utils/dto/name.dto';
import { toLowerCaseObjectKeys } from '../../../utils/tolowerCaseObjectKeys';
import { bundleType } from '../types/bundle-types.enum';
import { BundleItemDto } from './bundle-item.dto';

export class CreateBundleDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Code of bundle',
    example: 'BND001',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  code: string;

  @ApiProperty({
    description: 'Name of bundle in multiple languages',
    type: NameDto,
    required: true,
  })
  @Transform((param) => toLowerCaseObjectKeys(param.value))
  @Validate(validateLanguage)
  @Validate(validateEnLanguage)
  public name: NameDto;

  @ApiProperty({
    description: 'Whether this bundle can contain nested bundles',
    example: false,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === true)
  nested_bundles?: boolean = false;

  @ApiProperty({
    description: 'Status of the bundle',
    example: true,
    required: false,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value !== false)
  status?: boolean = true;

  @ApiProperty({
    description: 'Bundle type',
    required: false,
    enum: bundleType,
    default: bundleType.simple,
  })
  @IsOptional()
  @IsEnum(bundleType)
  type?: bundleType = bundleType.simple;

  @ApiProperty({
    description: 'Array of bundle items with item, unit, qty, price, and total',
    required: true,
    type: [BundleItemDto],
    example: [
      {
        item: '68504d110b01f0fce473de4e',
        unit: '68504d110b01f0fce473dd36',
        qty: 2,
        price: 25.5,
        total: 51.0,
      },
    ],
  })
  @IsNotEmpty({ message: 'At least one bundle item is required' })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one bundle item is required' })
  @ValidateNested({ each: true })
  @Type(() => BundleItemDto)
  bundle_items: BundleItemDto[];

  @ApiProperty({
    description: 'Description of the bundle',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Price for the bundle',
    example: 25.5,
    minimum: 0,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  bundle_price: number;

  @ApiProperty({
    description: 'original price summation for all bundle items ',
    example: 25.5,
    minimum: 0,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  original_price: number;

  @ApiProperty({
    description:
      'discount percentage for the bundle based on original price and bundle price',
    example: 25.5,
    minimum: 0,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  discount_percentage: number;
}
