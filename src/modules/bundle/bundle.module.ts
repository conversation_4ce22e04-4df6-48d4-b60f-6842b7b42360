import { Module } from '@nestjs/common';
import { Bundle, bundleSchema } from './schema/bundle.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { BundlesController } from './bundles.controller';
import { BundlesService } from './bundles.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Bundle.name, schema: bundleSchema }]),
  ],
  controllers: [BundlesController],
  providers: [BundlesService],
  exports: [BundlesService],
})
export class BundleModule {}
