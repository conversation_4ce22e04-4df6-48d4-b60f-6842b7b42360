import { Module } from '@nestjs/common';
import { Bundle, bundleSchema } from './schema/bundle.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { BundlesController } from './bundles.controller';
import { BundlesService } from './bundles.service';
import { Item, itemSchema } from '../inventory-item/schema/item.schema';
import { Unit, unitSchema } from '../unit/schema/unit.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Bundle.name, schema: bundleSchema },
      { name: Item.name, schema: itemSchema },
      { name: Unit.name, schema: unitSchema },
    ]),
  ],
  controllers: [BundlesController],
  providers: [BundlesService],
  exports: [BundlesService],
})
export class BundleModule {}
