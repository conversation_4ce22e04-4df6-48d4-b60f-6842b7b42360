import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CreateBundleDto } from './dto/create-bundle.dto';
import { UpdateBundleDto } from './dto/update-bundle.dto';
import { GetBundlesPaginationDto } from './dto/get-bundles-pagination.dto';
import { BundlesService } from './bundles.service';
import { Bundle } from './schema/bundle.schema';
import { CaslGuard } from '../casl/guards/casl.guard';
import { Abilities } from '../casl/guards/abilities.decorator';
import {
  ApiBearerAuth,
  ApiBody,
  ApiHeader,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { authType } from '../auth/guards/auth-type.decorator';
import { DeleteExceptIdsDto } from '../../utils/dto/delete-except-ids.dto';

@ApiTags('bundles')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@Controller('inventory/bundles')
export class BundlesController {
  constructor(private readonly bundlesService: BundlesService) {}

  @Post()
  @authType('apiKey')
  @ApiBearerAuth('TOKEN')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'create', subject: 'bundle' })
  @ApiBody({ type: CreateBundleDto })
  create(@Body() createDto: CreateBundleDto) {
    return this.bundlesService.create(createDto);
  }

  @Get('abstract')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'abstract_bundles' })
  abstract(@Query() query: GetBundlesPaginationDto) {
    return this.bundlesService.abstract(query);
  }

  @Get('max_number')
  maxNumber() {
    return this.bundlesService.findMaxNumber();
  }

  @Get()
  @authType('apiKey')
  @ApiBearerAuth('TOKEN')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'bundle' })
  findAll(@Query() query: GetBundlesPaginationDto) {
    return this.bundlesService.findAll(query);
  }

  @Get('one')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'read', subject: 'bundle' })
  findOne(@Query('id') id: string): Promise<Bundle> {
    return this.bundlesService.findOne(id);
  }

  @Patch(':id')
  @ApiParam({ name: 'id' })
  @UseGuards(CaslGuard)
  @Abilities({ action: 'edit', subject: 'bundle' })
  update(
    @Param('id') id: string,
    @Body() updateBundleDto: UpdateBundleDto,
  ): Promise<Bundle> {
    return this.bundlesService.update(id, updateBundleDto);
  }

  @Delete()
  @UseGuards(CaslGuard)
  @Abilities({ action: 'delete', subject: 'bundle' })
  remove(@Body() dto: DeleteExceptIdsDto): Promise<any> {
    return this.bundlesService.remove(dto.ids);
  }
}
