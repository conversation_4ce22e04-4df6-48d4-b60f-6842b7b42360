import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CreateBundleDto } from './dto/create-bundle.dto';
import { UpdateBundleDto } from './dto/update-bundle.dto';
import { GetBundlesPaginationDto } from './dto/get-bundles-pagination.dto';
import { BundlesService } from './bundles.service';
import { Bundle } from './schema/bundle.schema';
import { CaslGuard } from '../casl/guards/casl.guard';
import { Abilities } from '../casl/guards/abilities.decorator';
import {
  ApiBearerAuth,
  ApiBody,
  ApiHeader,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { authType } from '../auth/guards/auth-type.decorator';
import { DeleteExceptIdsDto } from '../../utils/dto/delete-except-ids.dto';
import { DeleteBundleDto } from './dto/delete-bundle.dto';

@Controller('inventory/bundles')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@ApiTags('bundles')
export class BundlesController {
  constructor(private readonly bundlesService: BundlesService) {}

  @UseGuards(CaslGuard)
  @Abilities({ action: 'create', subject: 'bundle' })
  @Post()
  @authType('both')
  @ApiBearerAuth('TOKEN')
  @ApiBody({ type: CreateBundleDto })
  create(@Body() createDto: CreateBundleDto) {
    return this.bundlesService.create(createDto);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'abstract_bundles' })
  @Get('abstract')
  async abstract(@Query() query: GetBundlesPaginationDto) {
    return await this.bundlesService.abstract(query);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'bundle' })
  @authType('both')
  @ApiBearerAuth('TOKEN')
  @Get()
  findAll(@Query() query: GetBundlesPaginationDto) {
    return this.bundlesService.findAll(query);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'read', subject: 'bundle' })
  @authType('both')
  @ApiBearerAuth('TOKEN')
  @Get(':id')
  @ApiParam({ name: 'id' })
  findOne(@Param('id') id: string): Promise<Bundle> {
    return this.bundlesService.findOne(id);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'edit', subject: 'bundle' })
  @authType('both')
  @ApiBearerAuth('TOKEN')
  @Patch(':id')
  @ApiParam({ name: 'id' })
  update(
    @Param('id') id: string,
    @Body() updateBundleDto: UpdateBundleDto,
  ): Promise<Bundle> {
    return this.bundlesService.update(id, updateBundleDto);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'delete', subject: 'bundle' })
  @authType('both')
  @ApiBearerAuth('TOKEN')
  @Delete()
  delete(@Body() dto: DeleteBundleDto) {
    return this.bundlesService.delete(dto.id);
  }
}
