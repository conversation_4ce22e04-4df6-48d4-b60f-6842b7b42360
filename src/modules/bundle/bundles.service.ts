import { HttpException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Bundle } from './schema/bundle.schema';
import { CreateBundleDto } from './dto/create-bundle.dto';
import { UpdateBundleDto } from './dto/update-bundle.dto';
import { GetBundlesPaginationDto } from './dto/get-bundles-pagination.dto';
import { paginate, PaginationMetaType } from '../../utils/dto/pagination.dto';
import { nameOf } from '../../utils/object-key-name';
import { usersExceptionCode } from '../../exceptions/exception-code.users';

@Injectable()
export class BundlesService {
  constructor(
    @InjectModel(Bundle.name)
    private readonly bundleModel: Model<Bundle>,
  ) {}

  async create(dto: CreateBundleDto): Promise<Bundle> {
    // Check if code already exists
    const codeExist = await this.bundleModel.findOne({
      code: dto.code,
    });
    if (codeExist) {
      throw new HttpException(
        nameOf(usersExceptionCode, (exception) => exception.numberDuplicated),
        usersExceptionCode.numberDuplicated,
      );
    }

    // Validate bundle items exist
    if (!dto.bundle_items || dto.bundle_items.length === 0) {
      throw new HttpException('At least one bundle item is required', 400);
    }

    // Validate bundle items calculations
    let totalBundleItemsPrice = 0;
    for (const bundleItem of dto.bundle_items) {
      const calculatedTotal = bundleItem.qty * bundleItem.price;
      if (Math.abs(bundleItem.total - calculatedTotal) > 0.01) {
        throw new HttpException(
          `Bundle item total ${bundleItem.total} does not match calculated total ${calculatedTotal} (qty: ${bundleItem.qty} * price: ${bundleItem.price})`,
          400,
        );
      }
      totalBundleItemsPrice += bundleItem.total;
    }

    // Validate original price equals sum of bundle items
    if (Math.abs(dto.original_price - totalBundleItemsPrice) > 0.01) {
      throw new HttpException(
        `Original price ${dto.original_price} does not match sum of bundle items total ${totalBundleItemsPrice}`,
        400,
      );
    }

    // Validate discount percentage calculation
    const expectedDiscountPercentage =
      ((dto.original_price - dto.bundle_price) / dto.original_price) * 100;
    if (Math.abs(dto.discount_percentage - expectedDiscountPercentage) > 0.01) {
      throw new HttpException(
        `Discount percentage ${dto.discount_percentage}% does not match calculated discount ${expectedDiscountPercentage.toFixed(2)}% based on original price ${dto.original_price} and bundle price ${dto.bundle_price}`,
        400,
      );
    }

    return this.bundleModel.create(dto);
  }

  async findAll(query: GetBundlesPaginationDto): Promise<{
    result: Bundle[];
    meta: PaginationMetaType;
  }> {
    const filter: any = {};

    if (query.type) {
      filter.type = query.type;
    }

    if (query.status !== undefined) {
      filter.status = query.status;
    }

    if (query.nested_bundles !== undefined) {
      filter.nested_bundles = query.nested_bundles;
    }

    if (query.queries) {
      filter.$or = [
        { 'name.en': { $regex: query.queries, $options: 'i' } },
        { 'name.ar': { $regex: query.queries, $options: 'i' } },
        { code: { $regex: query.queries, $options: 'i' } },
      ];
    }

    const result = await this.bundleModel
      .find(filter)
      .populate('bundle_items.item', 'id code name')
      .populate('bundle_items.unit', 'id number name')
      .limit(query.limit)
      .skip((query.page - 1) * query.limit)
      .exec();

    const meta = await paginate(
      query.limit,
      query.page,
      this.bundleModel,
      filter,
    );

    return { result, meta };
  }

  async abstract(
    query: GetBundlesPaginationDto,
  ): Promise<{ result: Bundle[] }> {
    const filter: any = {};

    if (query.type) {
      filter.type = query.type;
    }

    if (query.status !== undefined) {
      filter.status = query.status;
    } else {
      filter.status = true; // Only active bundles by default
    }

    const result = await this.bundleModel
      .find(filter)
      .select('_id id code name type')
      .exec();

    return { result };
  }

  async findOne(_id: string): Promise<Bundle> {
    return this.bundleModel
      .findOne({ _id })
      .populate('bundle_items.item', 'id code name')
      .populate('bundle_items.unit', 'id number name')
      .exec();
  }

  async update(_id: string, updateBundleDto: UpdateBundleDto): Promise<Bundle> {
    // Check if bundle exists
    const existingBundle = await this.bundleModel.findOne({
      _id: _id,
    });

    if (!existingBundle) {
      throw new HttpException('Bundle not found', 404);
    }

    // Validate code uniqueness if being updated
    if (updateBundleDto.code && updateBundleDto.code !== existingBundle.code) {
      const codeExist = await this.bundleModel.findOne({
        code: updateBundleDto.code,
        _id: { $ne: _id },
      });
      if (codeExist) {
        throw new HttpException(
          nameOf(usersExceptionCode, (exception) => exception.numberDuplicated),
          usersExceptionCode.numberDuplicated,
        );
      }
    }

    // Validate bundle items if being updated
    if (updateBundleDto.bundle_items !== undefined) {
      if (
        !updateBundleDto.bundle_items ||
        updateBundleDto.bundle_items.length === 0
      ) {
        throw new HttpException('At least one bundle item is required', 400);
      }

      // Validate bundle items calculations
      let totalBundleItemsPrice = 0;
      for (const bundleItem of updateBundleDto.bundle_items) {
        const calculatedTotal = bundleItem.qty * bundleItem.price;
        if (Math.abs(bundleItem.total - calculatedTotal) > 0.01) {
          throw new HttpException(
            `Bundle item total ${bundleItem.total} does not match calculated total ${calculatedTotal} (qty: ${bundleItem.qty} * price: ${bundleItem.price})`,
            400,
          );
        }
        totalBundleItemsPrice += bundleItem.total;
      }

      // Get the original price to validate against (use updated value if provided, otherwise existing)
      const originalPrice =
        updateBundleDto.original_price !== undefined
          ? updateBundleDto.original_price
          : existingBundle.original_price;

      // Validate original price equals sum of bundle items
      if (Math.abs(originalPrice - totalBundleItemsPrice) > 0.01) {
        throw new HttpException(
          `Original price ${originalPrice} does not match sum of bundle items total ${totalBundleItemsPrice}`,
          400,
        );
      }
    }

    // Validate pricing calculations if any pricing fields are being updated
    if (
      updateBundleDto.original_price !== undefined ||
      updateBundleDto.bundle_price !== undefined ||
      updateBundleDto.discount_percentage !== undefined
    ) {
      const originalPrice =
        updateBundleDto.original_price !== undefined
          ? updateBundleDto.original_price
          : existingBundle.original_price;

      const bundlePrice =
        updateBundleDto.bundle_price !== undefined
          ? updateBundleDto.bundle_price
          : existingBundle.bundle_price;

      const discountPercentage =
        updateBundleDto.discount_percentage !== undefined
          ? updateBundleDto.discount_percentage
          : existingBundle.discount_percentage;

      // Validate discount percentage calculation
      const expectedDiscountPercentage =
        ((originalPrice - bundlePrice) / originalPrice) * 100;
      if (Math.abs(discountPercentage - expectedDiscountPercentage) > 0.01) {
        throw new HttpException(
          `Discount percentage ${discountPercentage}% does not match calculated discount ${expectedDiscountPercentage.toFixed(2)}% based on original price ${originalPrice} and bundle price ${bundlePrice}`,
          400,
        );
      }
    }

    return this.bundleModel
      .findByIdAndUpdate(_id, updateBundleDto, { new: true })
      .populate('bundle_items.item', 'id code name')
      .populate('bundle_items.unit', 'id number name')
      .exec();
  }

  async delete(id: string | Types.ObjectId): Promise<any> {
    return this.bundleModel.deleteOne({ _id: id });
  }
}
