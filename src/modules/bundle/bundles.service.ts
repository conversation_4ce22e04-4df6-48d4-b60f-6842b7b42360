import { HttpException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Bundle } from './schema/bundle.schema';
import { CreateBundleDto } from './dto/create-bundle.dto';
import { UpdateBundleDto } from './dto/update-bundle.dto';
import { GetBundlesPaginationDto } from './dto/get-bundles-pagination.dto';
import { paginate, PaginationMetaType } from '../../utils/dto/pagination.dto';
import { nameOf } from '../../utils/object-key-name';
import { usersExceptionCode } from '../../exceptions/exception-code.users';
import { Calculator } from '../../utils/calculator';
import { findMaxNumber } from '../../utils/auto-increment-number';

@Injectable()
export class BundlesService {
  constructor(
    @InjectModel(Bundle.name)
    private readonly bundleModel: Model<Bundle>,
  ) {}

  async create(dto: CreateBundleDto): Promise<Bundle> {
    // Check if code already exists
    const codeExist = await this.bundleModel.findOne({
      code: dto.code,
    });
    if (codeExist) {
      throw new HttpException(
        nameOf(usersExceptionCode, (exception) => exception.duplicatedCode),
        usersExceptionCode.duplicatedCode,
      );
    }

    // Validate bundle items calculations
    const totalBundleItemsPrice = new Calculator(0);
    for (const bundleItem of dto.bundle_items) {
      const calculatedTotal = Calculator.mul(bundleItem.qty, bundleItem.price)
        .round()
        .number();
      if (calculatedTotal !== bundleItem.total) {
        throw new HttpException(
          nameOf(
            usersExceptionCode,
            (exception) => exception.bundleItemTotalWrong,
          ),
          usersExceptionCode.bundleItemTotalWrong,
        );
      }
      totalBundleItemsPrice.plus(bundleItem.price);
    }

    // Validate original price equals sum of bundle items
    const totalBundleItemsPriceNumber = totalBundleItemsPrice.round().number();
    if (dto.original_price !== totalBundleItemsPriceNumber) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exception) => exception.bundleOriginalPriceWrong,
        ),
        usersExceptionCode.bundleOriginalPriceWrong,
      );
    }

    // Validate discount percentage calculation
    const expectedDiscountPercentage = Calculator.div(
      Calculator.subtract(dto.original_price, dto.bundle_price).number(),
      dto.original_price,
    )
      .multiply(100)
      .round()
      .number();

    if (dto.discount_percentage !== expectedDiscountPercentage) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exception) => exception.bundleDiscountPercentageWrong,
        ),
        usersExceptionCode.bundleDiscountPercentageWrong,
      );
    }

    // Automatically set number_of_items
    const bundleData = {
      ...dto,
      number_of_items: dto.bundle_items.length,
    };

    return this.bundleModel.create(bundleData);
  }

  async findAll(query: GetBundlesPaginationDto): Promise<{
    result: Bundle[];
    meta: PaginationMetaType;
  }> {
    const filter: any = {};

    if (query.type) {
      filter.type = query.type;
    }

    if (query.status !== undefined) {
      filter.status = query.status;
    }

    if (query.nested_bundles !== undefined) {
      filter.nested_bundles = query.nested_bundles;
    }

    if (query.queries) {
      filter.$or = [
        { 'name.en': { $regex: query.queries, $options: 'i' } },
        { 'name.ar': { $regex: query.queries, $options: 'i' } },
        { code: { $regex: query.queries, $options: 'i' } },
      ];
    }

    const result = await this.bundleModel
      .find(filter)
      .select('code name original_price bundle_price status number_of_items')
      .limit(query.limit)
      .skip((query.page - 1) * query.limit)
      .exec();

    const meta = await paginate(
      query.limit,
      query.page,
      this.bundleModel,
      filter,
    );

    return { result, meta };
  }

  async abstract(
    query: GetBundlesPaginationDto,
  ): Promise<{ result: Bundle[] }> {
    const filter: any = {};

    if (query.type) {
      filter.type = query.type;
    }

    if (query.status !== undefined) {
      filter.status = query.status;
    } else {
      filter.status = true; // Only active bundles by default
    }

    const result = await this.bundleModel
      .find(filter)
      .select('_id id code name type')
      .exec();

    return { result };
  }

  async findOne(_id: string): Promise<Bundle> {
    return this.bundleModel
      .findOne({ _id })
      .populate('bundle_items.item', 'id code name')
      .populate('bundle_items.unit', 'id code name')
      .exec();
  }

  async update(_id: string, updateBundleDto: UpdateBundleDto): Promise<Bundle> {
    // Check if bundle exists
    const existingBundle = await this.bundleModel.findOne({
      _id: _id,
    });

    if (!existingBundle) {
      throw new HttpException(
        nameOf(usersExceptionCode, (exception) => exception.bundleDoesNotExist),
        usersExceptionCode.bundleDoesNotExist,
      );
    }

    // Validate code uniqueness if being updated
    if (updateBundleDto.code && updateBundleDto.code !== existingBundle.code) {
      const codeExist = await this.bundleModel.findOne({
        code: updateBundleDto.code,
        _id: { $ne: _id },
      });
      if (codeExist) {
        throw new HttpException(
          nameOf(usersExceptionCode, (exception) => exception.duplicatedCode),
          usersExceptionCode.duplicatedCode,
        );
      }
    }

    // Validate bundle items if being updated
    if (updateBundleDto.bundle_items !== undefined) {
      // Automatically set number_of_items when bundle_items is updated
      (updateBundleDto as any).number_of_items =
        updateBundleDto.bundle_items.length;

      // Validate bundle items calculations
      const totalBundleItemsPrice = new Calculator(0);
      for (const bundleItem of updateBundleDto.bundle_items) {
        const calculatedTotal = Calculator.mul(bundleItem.qty, bundleItem.price)
          .round()
          .number();
        if (calculatedTotal !== bundleItem.total) {
          throw new HttpException(
            nameOf(
              usersExceptionCode,
              (exception) => exception.bundleItemTotalWrong,
            ),
            usersExceptionCode.bundleItemTotalWrong,
          );
        }
        totalBundleItemsPrice.plus(bundleItem.price);
      }

      // Get the original price to validate against (use updated value if provided, otherwise existing)
      const originalPrice =
        updateBundleDto.original_price !== undefined
          ? updateBundleDto.original_price
          : existingBundle.original_price;

      // Validate original price equals sum of bundle items
      const totalBundleItemsPriceNumber = totalBundleItemsPrice
        .round()
        .number();
      if (originalPrice !== totalBundleItemsPriceNumber) {
        throw new HttpException(
          nameOf(
            usersExceptionCode,
            (exception) => exception.bundleOriginalPriceWrong,
          ),
          usersExceptionCode.bundleOriginalPriceWrong,
        );
      }
    }

    // Validate pricing calculations if any pricing fields are being updated
    if (
      updateBundleDto.original_price !== undefined ||
      updateBundleDto.bundle_price !== undefined ||
      updateBundleDto.discount_percentage !== undefined
    ) {
      const originalPrice =
        updateBundleDto.original_price !== undefined
          ? updateBundleDto.original_price
          : existingBundle.original_price;

      const bundlePrice =
        updateBundleDto.bundle_price !== undefined
          ? updateBundleDto.bundle_price
          : existingBundle.bundle_price;

      const discountPercentage =
        updateBundleDto.discount_percentage !== undefined
          ? updateBundleDto.discount_percentage
          : existingBundle.discount_percentage;

      // Validate discount percentage calculation
      const expectedDiscountPercentage = Calculator.div(
        Calculator.subtract(originalPrice, bundlePrice).number(),
        originalPrice,
      )
        .multiply(100)
        .round()
        .number();

      if (discountPercentage !== expectedDiscountPercentage) {
        throw new HttpException(
          nameOf(
            usersExceptionCode,
            (exception) => exception.bundleDiscountPercentageWrong,
          ),
          usersExceptionCode.bundleDiscountPercentageWrong,
        );
      }
    }

    return this.bundleModel
      .findByIdAndUpdate(_id, updateBundleDto, { new: true })
      .populate('bundle_items.item', 'id code name')
      .populate('bundle_items.unit', 'id number name')
      .exec();
  }

  async findMaxNumber(): Promise<number> {
    return findMaxNumber(this.bundleModel);
  }

  async remove(ids: string[]): Promise<any> {
    return this.bundleModel.deleteMany({
      _id: { $in: ids },
    });
  }

  async delete(id: string | Types.ObjectId): Promise<any> {
    return this.bundleModel.deleteOne({ _id: id });
  }
}
