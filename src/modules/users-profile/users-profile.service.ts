import { Injectable, Scope } from '@nestjs/common';
import { UsersService } from '../users/users.service';
import { RequestWithUser } from '../auth/interfaces/authorize.interface';
import { ApiTokenService } from '../api-token/api-token.service';
import { Types } from 'mongoose';
import { CreateApiTokenDto } from './dto/create-api-token.dto';
import { UpdateUsersProfileDto } from './dto/update-users-profile.dto';
import { ConfigService } from '@nestjs/config';
import { MinioClientService } from '../minio/minio-client.service';
import { BufferedFile } from '../minio/file.model';

@Injectable({ scope: Scope.REQUEST, durable: true })
export class UsersProfileService {
  constructor(
    private readonly usersService: UsersService,
    private readonly apiTokenService: ApiTokenService,
    private readonly minioClientService: MinioClientService,
    private readonly configService: ConfigService,
  ) {}

  async findMe(req: RequestWithUser) {
    const me = await this.usersService.findOne({ _id: req.user.user_id });
    const bucket = await this.configService.get('S3_BUCKET_PROFILE');
    try {
      me.logo = await this.minioClientService.getFile(me.logo, bucket);
    } catch (err) {
      console.log('user does not have logo', err);
    }
    return me;
  }

  async update(req: RequestWithUser, body: UpdateUsersProfileDto) {
    const updateObject: any = { _id: req.user.user_id, ...body };
    return await this.usersService.update(req, updateObject);
  }

  async createApiToken(
    req: RequestWithUser,
    createApiTokenDto: CreateApiTokenDto,
  ) {
    return await this.apiTokenService.createApiToken(
      req.user.user_id,
      createApiTokenDto,
    );
  }

  async findMyTokens(req: RequestWithUser) {
    return await this.apiTokenService.find(req.user.user_id);
  }

  async revokeApiToken(req: RequestWithUser, _id) {
    await this.apiTokenService.revoke(
      new Types.ObjectId(_id),
      new Types.ObjectId(req.user.user_id),
    );
  }

  async validateApiToken(user_id, api_token: string): Promise<any> {
    const jwtDec = api_token.split('.');
    const jwt = `${jwtDec[0]}.${jwtDec[1]}.`;
    const rs = await this.apiTokenService.findOne(
      jwtDec[2], //api_id
      new Types.ObjectId(user_id),
    );

    if (!rs) {
      return false;
    }

    if ((await rs.compareToken(jwt)) === true) {
      return { ...rs.toObject(), api_token: jwt };
    }
    return false;
  }

  async uploadLogo(req: RequestWithUser, file: BufferedFile) {
    const bucket = await this.configService.get('S3_BUCKET_PROFILE');
    const upload = await this.minioClientService.upload(file, bucket);
    const updateObject: any = { _id: req.user?.user_id, logo: upload.name };
    return await this.usersService.update(req, updateObject);
  }
}
