import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { UsersProfileService } from './users-profile.service';
import { RequestWithUser } from '../auth/interfaces/authorize.interface';
import { CaslGuard } from '../casl/guards/casl.guard';
import { Abilities } from '../casl/guards/abilities.decorator';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiHeader,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { CreateApiTokenDto } from './dto/create-api-token.dto';
import { UpdateUsersProfileDto } from './dto/update-users-profile.dto';
import { authType } from '../auth/guards/auth-type.decorator';
import { extname } from 'path';
import { BufferedFile } from '../minio/file.model';
import { FileInterceptor } from '@nestjs/platform-express';
@ApiTags('user-profile')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  example: '64956ef5f506e9edf21df4e8',
  required: true,
})
@Controller('users/users-profile')
export class UsersProfileController {
  constructor(private readonly usersProfileService: UsersProfileService) {}
  //TODO: Implement the correct ability to get the user profile
  @UseGuards(CaslGuard)
  @Abilities({ action: 'edit', subject: 'users' })
  @Post('/logo')
  @ApiOperation({ summary: 'Upload logo' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(
    FileInterceptor('file', {
      limits: {
        files: 1,
        fileSize: 20 * 1000 * 1000, // 20 mb in bytes
      },
      fileFilter: (req: any, file: any, cb: any) => {
        if (
          file.mimetype.match(`pdf.*|image.*|excel.*|sheet|word.*`) &&
          file.originalname.match(/\.(pdf|jpg|jpeg|png|xlsx|xls|docx)$/g)
        ) {
          // Allow storage of file
          cb(null, true);
        } else {
          // Reject file
          cb(
            new HttpException(
              `Unsupported file type ${extname(file.originalname)}`,
              HttpStatus.BAD_REQUEST,
            ),
            false,
          );
        }
      },
    }),
  )
  async uploadFile(
    @Req() req: RequestWithUser,
    @UploadedFile() file: BufferedFile,
  ) {
    return this.usersProfileService.uploadLogo(req, file);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'users' })
  @Get()
  findAll(@Req() req: RequestWithUser) {
    return this.usersProfileService.findMe(req);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'edit', subject: 'users' })
  @Patch()
  update(@Req() request: RequestWithUser, @Body() body: UpdateUsersProfileDto) {
    return this.usersProfileService.update(request, body);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'users' })
  @Post('token')
  createApiToken(
    @Req() req: RequestWithUser,
    @Body() createApiTokenDto: CreateApiTokenDto,
  ) {
    return this.usersProfileService.createApiToken(req, createApiTokenDto);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'users' })
  @Get('token')
  findAllTokens(@Req() req: RequestWithUser) {
    return this.usersProfileService.findMyTokens(req);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'users' })
  @Delete('token/:id')
  revokeApiToken(@Req() req: RequestWithUser, @Param('id') id: string) {
    return this.usersProfileService.revokeApiToken(req, id);
  }

  @ApiBearerAuth('TOKEN')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'users' })
  @authType('apiKey')
  @Get('test')
  test() {
    return 1;
  }
}
