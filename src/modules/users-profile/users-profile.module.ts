import { Module } from '@nestjs/common';
import { UsersProfileService } from './users-profile.service';
import { UsersProfileController } from './users-profile.controller';
import { UsersModule } from '../users/users.module';
import { ApiTokenModule } from '../api-token/api-token.module';
import { MinioClientModule } from '../minio/minio-client.module';

@Module({
  imports: [UsersModule, ApiTokenModule, MinioClientModule],
  controllers: [UsersProfileController],
  providers: [UsersProfileService],
  exports: [UsersProfileService],
})
export class UsersProfileModule {}
