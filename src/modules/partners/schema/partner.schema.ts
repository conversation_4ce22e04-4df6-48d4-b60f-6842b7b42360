import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

import { GeneralInfo } from './general-info.schema';
import mongoose, { Types } from 'mongoose';

@Schema({ timestamps: true })
export class Partner extends GeneralInfo {
  @Prop({ type: Number, required: true })
  id: number;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Branch',
    required: true,
  })
  branch_id: Types.ObjectId;

  @Prop({ type: Boolean, required: true })
  cash_only: boolean;

  @Prop({ type: String, required: true })
  code: string;
}

export const partnerSchema = SchemaFactory.createForClass(Partner);
partnerSchema.index({ branch_id: 1, id: 1 }, { unique: true });

partnerSchema.index({ branch_id: 1, code: 1, type: 1 }, { unique: true });
