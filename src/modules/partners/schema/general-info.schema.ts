import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { partnerType } from '../types';
import { NationalAddress } from './national-address.schema';
import { Name, nameSchema } from '../../../utils/schemas';

@Schema({ timestamps: true })
export class GeneralInfo {
  @Prop({ type: nameSchema, required: true })
  name: Name;

  @Prop()
  mobile: string;

  @Prop({ required: false })
  tax_code: string;

  @Prop()
  email: string;

  @Prop({ type: NationalAddress, default: () => ({}) })
  national_address: NationalAddress;

  @Prop({ type: Boolean, default: false })
  isActive: boolean;

  @Prop({ type: String, enum: partnerType, default: partnerType.customer })
  type: partnerType;
}

export const generalInfoSchema = SchemaFactory.createForClass(GeneralInfo);
