import { Prop, Schema } from '@nestjs/mongoose';
import { Name, nameSchema } from '../../../utils/schemas';

@Schema({ _id: false })
export class NationalAddress {
  @Prop({ type: nameSchema })
  trade_name: Name;

  @Prop({ type: String, default: '' })
  commercial_activities: string;

  @Prop({ type: String, default: '' })
  short_address: string;

  @Prop({ type: String, default: '' })
  building_no: string;

  @Prop({ type: String, default: '' })
  street: string;

  @Prop({ type: String, default: '' })
  secondary_no: string;

  @Prop({ type: String, default: '' })
  district: string;

  @Prop({ type: String, default: '' })
  postal_code: string;

  @Prop({ type: String, default: '' })
  city: string;

  @Prop({ type: String, default: '' })
  governorate: string;
}
