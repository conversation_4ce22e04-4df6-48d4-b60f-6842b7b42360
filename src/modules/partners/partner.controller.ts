import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiHeader, ApiTags } from '@nestjs/swagger';
import { CaslGuard } from '../casl/guards/casl.guard';
import { CustomerService } from './customer.service';
import {
  CreateCustomerDto,
  FindAllCustomersQueriesDto,
  FindPartnerResponseDto,
} from './dto';
import { Partner } from './schema';
import { Abilities } from '../casl/guards/abilities.decorator';
import { authType } from '../auth/guards/auth-type.decorator';
import { RequestHeaders } from 'src/utils/decorators/request-header.validator';
import { BranchHeaderDto } from 'src/utils/dto/request-headers.dto';
import { DeleteExceptIdsDto } from '../../utils/dto/delete-except-ids.dto';
import { CreateCashCustomerDto } from './dto/create-cash-customer.dto';

@ApiTags('Partner')
@Controller('trade/partner')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
export class PartnerController {
  constructor(private readonly customerService: CustomerService) {}

  @UseGuards(CaslGuard)
  @Abilities({ action: 'create', subject: 'customer' })
  @Post('/customer')
  @authType('both')
  @ApiBearerAuth('TOKEN')
  @ApiBody({ type: [CreateCustomerDto] })
  createCustomer(
    @RequestHeaders() headers: BranchHeaderDto,
    @Body() createDto: CreateCustomerDto[],
  ): Promise<any> {
    const { branch } = headers;
    return this.customerService.createBulk(branch, createDto);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'create', subject: 'customer' })
  @Post('/customer/cash')
  createCashCustomer(
    @Body() createDto: CreateCashCustomerDto,
    @RequestHeaders() headers: BranchHeaderDto,
  ): Promise<Partner> {
    const { branch } = headers;
    return this.customerService.createCashCustomer(createDto, branch);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'customer' })
  @Get('/customer')
  @authType('apiKey')
  @ApiBearerAuth('TOKEN')
  findAllCustomer(
    @RequestHeaders() headers: BranchHeaderDto,
    @Query() query: FindAllCustomersQueriesDto,
  ): Promise<FindPartnerResponseDto> {
    const { branch } = headers;
    return this.customerService.findAll(branch, query);
  }

  @Get('/customer/abstract')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'abstract_customers' })
  abstractCustomer(@RequestHeaders() headers: BranchHeaderDto) {
    const { branch } = headers;
    return this.customerService.abstract(branch);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'read', subject: 'customer' })
  @Get('/customer/:id')
  findOneCustomer(@Param('id') id: string): Promise<Partner> {
    return this.customerService.findOne(id, undefined);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'delete', subject: 'customer' })
  @authType('apiKey')
  @ApiBearerAuth('TOKEN')
  @Delete()
  delete(@Body() dto: DeleteExceptIdsDto) {
    return this.customerService.delete(dto.ids);
  }
}
