import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PartnerController } from './partner.controller';
import { CustomerService } from './customer.service';
import { Partner, partnerSchema } from './schema';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Partner.name, schema: partnerSchema }]),
  ],
  providers: [CustomerService],
  controllers: [PartnerController],
  exports: [CustomerService],
})
export class PartnerModule {}
