import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsPhoneNumber,
  IsString,
  ValidateNested,
} from 'class-validator';

import { NameDto } from '../../../utils/dto/name.dto';
import { partnerType } from '../types';
import { NationalAddressDto } from './national-address.dto';

export class GeneralInfoDto {
  @ApiProperty({
    description: 'customer code',
    example: 'cs-12413',
    required: true,
  })
  @IsString()
  code: string;

  @ApiProperty({
    description: 'customer name',
    required: true,
    type: NameDto,
  })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => NameDto)
  name: NameDto;

  @ApiProperty({
    description: 'customer mobile',
    example: '+201122334455',
    required: false,
  })
  @IsOptional()
  @IsPhoneNumber()
  mobile?: string;

  @ApiProperty({
    description: 'customer tax code',
    example: '4003401',
    required: true,
  })
  @IsOptional()
  @IsString()
  tax_code?: string;

  @ApiProperty({
    description: 'customer email',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({
    description: 'National Address',
    required: false,
    type: NationalAddressDto,
  })
  @ValidateNested()
  @IsOptional()
  @Type(() => NationalAddressDto)
  national_address: NationalAddressDto;

  @ApiProperty({
    description: 'add customer to vendor list',
    example: true,
    required: true,
  })
  @IsNotEmpty()
  @IsBoolean()
  isActive: boolean;

  @IsNotEmpty()
  @IsBoolean()
  cash_only: boolean = false;

  @ApiHideProperty()
  type = partnerType.customer;
}
