import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsOptional, Validate, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';
import { partnerType } from '../types';
import { PaginationDto } from '../../../utils/dto/pagination.dto';

export class FindAllPartnerQueriesDto extends PaginationDto {
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsDateString()
  from_date?: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsDateString()
  @Validate(
    (o: FindAllPartnerQueriesDto) =>
      new Date(o.from_date) < new Date(o.to_date),
  )
  @Transform(({ value }) => value.to_date, { toClassOnly: true })
  to_date?: string;

  @ApiProperty({
    required: false,
    type: Boolean,
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value == 'true')
  isActive?: boolean;

  @ApiHideProperty()
  type?: partnerType;

  @ApiHideProperty()
  createdAt;
}
