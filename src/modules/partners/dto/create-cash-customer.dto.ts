import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsPhoneNumber,
  IsString,
} from 'class-validator';

export class CreateCashCustomerDto {
  @ApiProperty({
    description: 'customer name',
    required: true,
  })
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'customer mobile',
    example: '+201122334455',
    required: false,
  })
  @IsOptional()
  @IsPhoneNumber()
  mobile?: string;

  @ApiProperty({
    description: 'customer tax code',
    example: '4003401',
    required: true,
  })
  @IsOptional()
  @IsString()
  tax_code?: string;

  @ApiProperty({
    description: 'customer email',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail()
  email?: string;
}
