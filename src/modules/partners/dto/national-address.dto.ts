import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsOptional, IsString, Validate } from 'class-validator';
import {
  NameDto,
  validateEnLanguage,
  validateLanguage,
} from '../../../utils/dto/name.dto';
import { toLowerCaseObjectKeys } from '../../../utils/tolowerCaseObjectKeys';

export class NationalAddressDto {
  @ApiProperty({
    example: {
      en: 'ruzi',
      ar: 'روزبه',
    },
  })
  @IsOptional()
  @Transform((param) => toLowerCaseObjectKeys(param.value))
  @Validate(validateLanguage)
  @Validate(validateEnLanguage)
  trade_name: NameDto;

  @IsOptional()
  @ApiProperty()
  @IsString()
  commercial_activities: string;

  @IsOptional()
  @ApiProperty()
  @IsString()
  short_address: string;

  @IsOptional()
  @ApiProperty()
  @IsString()
  building_no: string;

  @IsOptional()
  @ApiProperty()
  @IsString()
  street: string;

  @IsOptional()
  @ApiProperty()
  @IsString()
  secondary_no: string;

  @IsOptional()
  @ApiProperty()
  @IsString()
  district: string;

  @IsOptional()
  @ApiProperty()
  @IsString()
  postal_code: string;

  @IsOptional()
  @ApiProperty()
  @IsString()
  city: string;

  @IsOptional()
  @ApiProperty()
  @IsString()
  governorate: string;
}
