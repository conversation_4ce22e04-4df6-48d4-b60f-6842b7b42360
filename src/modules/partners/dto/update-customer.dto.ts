import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsOptional, ValidateNested } from 'class-validator';
import { Expose, Type } from 'class-transformer';

import { GeneralInfoDto } from './general-info.dto';
import { NationalAddressDto } from './national-address.dto';
export class UpdateCustomerDto extends PartialType(GeneralInfoDto) {
  @ApiProperty({
    description: 'National Address',
    required: false,
    type: NationalAddressDto,
  })
  @ValidateNested()
  @IsOptional()
  @Type(() => NationalAddressDto)
  @Expose({ name: 'national_address' })
  nationalAddress: NationalAddressDto;
}
