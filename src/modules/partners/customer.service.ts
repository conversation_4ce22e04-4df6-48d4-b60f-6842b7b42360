import { HttpException, Injectable, Scope } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { nameOf } from '../../utils/object-key-name';
import {
  CreateCustomerDto,
  FindAllCustomersQueriesDto,
  FindPartnerResponseDto,
} from './dto';
import { Partner } from './schema';
import { partnerType } from './types';
import { usersExceptionCode } from '../../exceptions/exception-code.users';
import { paginate } from '../../utils/dto/pagination.dto';
import { CreateCashCustomerDto } from './dto/create-cash-customer.dto';

@Injectable({ scope: Scope.REQUEST, durable: true })
export class CustomerService {
  constructor(
    @InjectModel(Partner.name) private readonly partnerModel: Model<Partner>,
  ) {}

  async create(createDto: CreateCustomerDto): Promise<Partner> {
    const createCustomer = new this.partnerModel(createDto);

    await createCustomer.save();

    return await this.partnerModel.findOne({
      _id: createCustomer._id,
    });
  }

  async createBulk(
    branch: Types.ObjectId,
    createCustomerDto: CreateCustomerDto[],
  ) {
    const bulkOps = createCustomerDto.map((dto) => ({
      updateOne: {
        filter: { id: dto.id, branch_id: branch },
        update: {
          $set: {
            ...dto,
            branch_id: branch,
          },
        },
        upsert: true,
      },
    }));

    return this.partnerModel.bulkWrite(bulkOps);
  }

  async findAll(
    branch: Types.ObjectId,
    query: FindAllCustomersQueriesDto,
  ): Promise<FindPartnerResponseDto> {
    return await this.getFindAllResult(branch, query);
  }

  async getFindAllResult(
    branch: Types.ObjectId,
    query: FindAllCustomersQueriesDto,
  ): Promise<FindPartnerResponseDto> {
    const {
      limit,
      page,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      from_date,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      to_date,
      queries,
      sortBy,
      sortType,
      ...rest
    } = query;
    rest.type = partnerType.customer;
    const sort = { [sortBy]: sortType };
    let search = { ...rest, branch_id: branch } as FindAllCustomersQueriesDto;

    search.createdAt = {};
    if (from_date) {
      search.createdAt.$gte = from_date;
    }
    if (to_date) {
      search.createdAt.$lte = to_date;
    }
    if (Object.keys(search.createdAt).length == 0) {
      delete search.createdAt;
    }
    if (queries) {
      const escapedQueries = queries.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const searchfield = {
        $regex: new RegExp(`(^|\\s)${escapedQueries}`),
        $options: 'i',
      };
      search = {
        ...search,
        $or: [
          {
            $expr: {
              $regexMatch: {
                input: { $toString: '$code' },
                regex: new RegExp(`^${escapedQueries}`),
                options: 'i',
              },
            },
          },
          {
            'name.en': searchfield,
          },
          {
            'name.ar': searchfield,
          },
          {
            mobile: searchfield,
          },
        ],
      } as any;
    }
    const result = await this.partnerModel

      .find(search, null, { sort })
      .select([
        'id',
        'isActive',
        'name',
        'code',
        'mobile',
        'createdAt',
        'branch_id',
      ])
      .skip((+page - 1) * +limit)
      .limit(+limit)
      .lean();
    const meta = await paginate(+limit, +page, this.partnerModel, search);
    return { meta, result } as any;
  }

  async abstract(branch: Types.ObjectId) {
    const search: any = {
      $and: [{ branch_id: branch }, { $or: [{ type: partnerType.customer }] }],
    };
    const result = await this.partnerModel
      .find(search)
      .select('id name mobile code tax_code cash_only accounting_info')
      .lean();
    const res = result.map((partner) => {
      return {
        ...partner,
        id: partner.id.toString(),
      };
    });
    return { result: res };
  }

  async findOne(
    _id: string | Types.ObjectId,
    type: partnerType = partnerType.customer,
  ): Promise<Partner> {
    const data = (await this.getFindOneResult(_id, type)) as any;
    const { name } = data?.accounting_info?.general_account;
    data.accounting_info.general_account = { name };

    return data;
  }

  async getFindOneResult(
    _id: string | Types.ObjectId,
    type: partnerType = partnerType.customer,
  ): Promise<Partner> {
    const data = (await this.partnerModel
      .findOne({ _id, type })
      .lean()) as Partner;

    if (!data) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exception) => exception.customerDoesNotExist,
        ),
        usersExceptionCode.customerDoesNotExist,
      );
    }
    return data;
  }

  async findOnePartner(
    code: number,
    id: string | Types.ObjectId,
  ): Promise<Partner> {
    const data = (await this.partnerModel.findOne({ id }).lean()) as Partner;

    if (!data) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exception) => exception.customerDoesNotExist,
        ),
        usersExceptionCode.customerDoesNotExist,
      );
    }
    return data;
  }

  async delete(ids: number[]) {
    const result = await this.partnerModel.deleteMany({
      id: { $nin: ids },
    });
    return result;
  }

  async createCashCustomer(
    createDto: CreateCashCustomerDto,
    branch_id: Types.ObjectId,
  ): Promise<Partner> {
    const maxIdDoc = await this.partnerModel
      .findOne({ type: partnerType.customer })
      .sort({ id: -1 }) // Sort by `id` descending
      .select('id')
      .lean();

    const nextId = maxIdDoc?.id ? maxIdDoc.id + 1 : 1;
    const name = { ar: createDto.name, en: createDto.name };
    const type = partnerType.customer;
    const cashCustomer = {
      ...createDto,
      name,
      id: nextId,
      code: nextId,
      type,
      branch_id,
      cash_only: true,
    };
    const createCustomer = await this.partnerModel.create(cashCustomer);

    return await this.partnerModel.findOne({
      _id: createCustomer._id,
      type: partnerType.customer,
    });
  }
}
