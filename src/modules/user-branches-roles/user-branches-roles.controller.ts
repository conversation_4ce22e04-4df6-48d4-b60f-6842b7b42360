import { Controller, Get, Req } from '@nestjs/common';
import { UserBranchesRolesService } from './user-branches-roles.service';
import { RequestWithUser } from '../auth/interfaces/authorize.interface';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('user-branches-roles')
@Controller('users/user-branches-roles')
export class UserBranchesRolesController {
  constructor(
    private readonly userBranchesRolesService: UserBranchesRolesService,
  ) {}
  @Get()
  async findAll(@Req() request: RequestWithUser) {
    return await this.userBranchesRolesService.findAll(request.user?.user_id);
  }
}
