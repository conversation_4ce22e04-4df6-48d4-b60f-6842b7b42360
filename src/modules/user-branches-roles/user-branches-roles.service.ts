import { Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Connection, Model, ObjectId, Types } from 'mongoose';
import {
  UserBranchRole,
  userBranchRoleSchema,
} from './schema/user-branches-roles.schema';
import { RolesService } from '../roles/roles.service';
import { CreateUserBranchRolesDto } from './dto/create-branch-role.dto';
import { MongooseConfigService } from '../database/mongoose.service';
import DataBases from '../database/database.provider';

@Injectable()
export class UserBranchesRolesService {
  constructor(
    @InjectModel(UserBranchRole.name)
    private readonly userBranchRoleModel: Model<UserBranchRole>,
    private readonly rolesService: RolesService,
    @Inject(MongooseConfigService)
    private readonly mongooseConfigService: MongooseConfigService,
  ) {}

  public async findAll(userId: Types.ObjectId, code?: number) {
    let userBranchRole;
    const user = new Types.ObjectId(String(userId));
    if (code) {
      const dbConnection = await this.newDbConnection(code);
      userBranchRole = await this.findWithCustomConnection(dbConnection, user);
    } else {
      userBranchRole = await this.userBranchRoleModel
        .find({
          user: user,
        })
        .populate({ path: 'branch', select: '_id general_information.name' })
        .select('branch roles')
        .lean();
    }

    for (const record of userBranchRole) {
      (record as any).privileges =
        await this.rolesService.getUniquePermissionsFromRoles({
          ids: record.roles,
        });

      delete record.roles;
    }

    return userBranchRole;
  }
  public async find(branchId: Types.ObjectId) {
    const branch = new Types.ObjectId(String(branchId));
    const userBranchRole = await this.userBranchRoleModel.find({
      branch,
    });
    return userBranchRole;
  }
  public async deleteBranchRule(branchId: Types.ObjectId) {
    const branch = new Types.ObjectId(String(branchId));
    const userBranchRole = await this.userBranchRoleModel.deleteMany({
      branch,
    });
    return userBranchRole;
  }

  public async getUserPermissions(
    userId: ObjectId | Types.ObjectId | string,
    branchId: Types.ObjectId | string,
  ) {
    const branch = new Types.ObjectId(branchId);
    const userBranchRoles = await this.userBranchRoleModel
      .find({
        user: userId,
        branch: branch,
      })
      .lean();

    const permissions = [];
    for (const record of userBranchRoles) {
      permissions.push(
        await this.rolesService.getUniquePermissionsFromRoles({
          ids: record.roles,
        }),
      );
    }
    return permissions.flat();
  }

  public async create(dto: CreateUserBranchRolesDto) {
    await this.userBranchRoleModel
      .find({
        user: dto.user,
      })
      .deleteMany();
    for (const branch of dto.branches) {
      await this.userBranchRoleModel.findOneAndUpdate(
        {
          user: dto.user,
          branch: new Types.ObjectId(branch),
        },
        {
          user: dto.user,
          branch: new Types.ObjectId(branch),
          roles: dto.roles.map((role) => new Types.ObjectId(role)),
        },
        { upsert: true },
      );
    }
  }

  public async update(dto: CreateUserBranchRolesDto) {
    const userBranchRoleData = [];

    await this.userBranchRoleModel
      .find({
        user: dto.user,
      })
      .deleteMany();

    dto.branches?.forEach((branch) => {
      userBranchRoleData.push({
        user: dto.user,
        branch: new Types.ObjectId(branch),
        roles: dto.roles.map((role) => new Types.ObjectId(role)),
      });
    });

    return await this.userBranchRoleModel.insertMany(userBranchRoleData);
  }

  public async getByUser(user: Types.ObjectId) {
    const result = await this.userBranchRoleModel
      .find({ user: user })
      .populate({ path: 'branch', select: '_id general_information.name' })
      .exec();

    const resolvedRoles = [];
    for (const object of result) {
      const roles = await this.rolesService.abstract({ ids: object.roles });

      resolvedRoles.push({
        ...object.toObject().branch,
        roles: roles,
      });
    }
    return resolvedRoles;
  }

  async countUsersPerRole() {
    return await this.userBranchRoleModel.aggregate([
      { $unwind: '$roles' },
      // eslint-disable-next-line @typescript-eslint/naming-convention
      { $group: { _id: '$roles', uniqueUsers: { $addToSet: '$user' } } },
      {
        $project: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          roleId: '$_id',
          // eslint-disable-next-line @typescript-eslint/naming-convention
          userCount: { $size: '$uniqueUsers' },
        },
      },
    ]);
  }

  private async newDbConnection(code): Promise<Connection> {
    const liveConnections = await DataBases.getConnection(
      this.mongooseConfigService.createDbUrl(code),
    );
    return liveConnections;
  }

  async findWithCustomConnection(connection: Connection, user: Types.ObjectId) {
    const userBranchRoleModel =
      connection.models.UserBranchRole ||
      (connection.model<UserBranchRole>(
        UserBranchRole.name,
        userBranchRoleSchema,
      ) as any);
    return await userBranchRoleModel
      .find({ user })
      .populate({ path: 'branch', select: '_id general_information.name' })
      .select('branch roles')
      .lean();
  }
}
