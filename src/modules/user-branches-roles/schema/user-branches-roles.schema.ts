import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types } from 'mongoose';
import { Role } from '../../roles/schemas/role.schema';
import { User } from '../../users/schemas/user.schema';
import { Branch } from '../../branch/schemas/branch.schema';

@Schema({ timestamps: true })
export class UserBranchRole {
  @Prop({
    required: true,
    type: Types.ObjectId,
    ref: User.name,
  })
  user: Types.ObjectId;

  @Prop({
    required: true,
    type: Types.ObjectId,
    ref: Branch.name,
  })
  branch: Branch;

  @Prop([
    {
      required: true,
      type: Types.ObjectId,
      ref: Role.name,
    },
  ])
  roles: Types.ObjectId[];
}

export const userBranchRoleSchema =
  SchemaFactory.createForClass(UserBranchRole);

userBranchRoleSchema.index({ user: 1, branch: 1 }, { unique: true });
