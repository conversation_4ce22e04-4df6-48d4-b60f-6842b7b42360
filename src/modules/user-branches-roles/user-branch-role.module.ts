import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  UserBranchRole,
  userBranchRoleSchema,
} from '../user-branches-roles/schema/user-branches-roles.schema';
import { UserBranchesRolesController } from './user-branches-roles.controller';
import { UserBranchesRolesService } from './user-branches-roles.service';
import {
  Permission,
  permissionSchema,
} from '../permissions/schema/permisstions.schema';
import { RolesModule } from '../roles/roles.module';
import { DatabaseModule } from '../database/database.module';

@Module({
  imports: [
    DatabaseModule,
    MongooseModule.forFeature([
      { name: UserBranchRole.name, schema: userBranchRoleSchema },
      { name: Permission.name, schema: permissionSchema },
    ]),
    forwardRef(() => RolesModule),
  ],
  controllers: [UserBranchesRolesController],
  providers: [UserBranchesRolesService],
  exports: [UserBranchesRolesService],
})
export class UserBranchesRolesModule {}
