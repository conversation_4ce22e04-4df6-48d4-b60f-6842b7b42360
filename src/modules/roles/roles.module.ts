import { forwardRef, Module } from '@nestjs/common';
import { RolesService } from './roles.service';
import { RolesController } from './roles.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Role, roleSchema } from './schemas/role.schema';
import {
  Permission,
  permissionSchema,
} from '../permissions/schema/permisstions.schema';
import { UserBranchesRolesModule } from '../user-branches-roles/user-branch-role.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Role.name, schema: roleSchema }]),
    MongooseModule.forFeature([
      { name: Permission.name, schema: permissionSchema },
    ]),
    forwardRef(() => UserBranchesRolesModule),
  ],
  controllers: [RolesController],
  providers: [RolesService],
  exports: [RolesService],
})
export class RolesModule {}
