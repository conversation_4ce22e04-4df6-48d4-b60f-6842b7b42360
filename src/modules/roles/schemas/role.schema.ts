import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Permission } from '../../permissions/schema/permisstions.schema';

@Schema({ timestamps: true })
export class Role {
  @Prop({
    required: true,
    trim: true,
    unique: true,
  })
  @ApiProperty()
  name: string;

  @Prop({
    required: true,
    ref: Permission.name,
  })
  @ApiProperty()
  permissions: Permission[];
}

export const roleSchema = SchemaFactory.createForClass(Role);
