import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { PaginationMetaType } from 'src/utils/dto/pagination.dto';
import { Type } from 'class-transformer';
import { Permission } from '../../permissions/schema/permisstions.schema';

export class GetRolesDto {
  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  name?: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  permissions?: Permission[];
}

export class GetRolesPaginationDto extends GetRolesDto {
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  public page?: number;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  public limit?: number;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  queries?: string;
}

export class GetRolesResponseDto {
  @ApiProperty()
  result: GetRolesDto[];

  @ApiProperty()
  meta: PaginationMetaType;
}
