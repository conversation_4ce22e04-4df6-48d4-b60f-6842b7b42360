import { IsNotEmpty } from '@nestjs/class-validator';
import { IsArray, IsString } from 'class-validator';
import { Types } from 'mongoose';
import { ApiProperty, PartialType } from '@nestjs/swagger';
export class CreateRolesDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    required: true,
    description: 'role name',
    example: 'superadmin',
  })
  public name: string;

  @IsArray()
  @IsNotEmpty()
  @ApiProperty({
    required: true,
    description: 'permissions ids',
    example: ['6501b9f946ad8225ab76cb71'],
  })
  public permissions: Types.ObjectId[];
}

export class UpdateRolesDto extends PartialType(CreateRolesDto) {}
