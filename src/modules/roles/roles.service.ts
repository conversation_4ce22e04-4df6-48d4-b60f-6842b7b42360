import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Role } from './schemas/role.schema';
import { Model, Types } from 'mongoose';
import { rolesDto } from './dtos/roles.dto';
import { Permission } from '../permissions/schema/permisstions.schema';
import { paginate } from '../../utils/dto/pagination.dto';
import {
  GetRolesPaginationDto,
  GetRolesResponseDto,
} from './dtos/get-roles.dto';
import { CreateRolesDto, UpdateRolesDto } from './dtos/create-roles.dto';
import { UserBranchesRolesService } from '../user-branches-roles/user-branches-roles.service';

@Injectable()
export class RolesService {
  constructor(
    @InjectModel(Role.name)
    private readonly roleModel: Model<Role>,
    @InjectModel(Permission.name)
    private readonly permissionModel: Model<Permissions>,
    @Inject(forwardRef(() => UserBranchesRolesService))
    private readonly userBranchRolesService: UserBranchesRolesService,
  ) {}

  public async getUniquePermissionsFromRoles(dto: rolesDto) {
    const roles = await this.roleModel.find({ _id: dto.ids }).exec();
    const permissions = roles.map((role) => role['permissions']).flat();

    const uniquePermissions = Array.from(
      new Set(permissions.map((permission) => permission.toString())),
    );

    const queriedPermissions = await this.fetchPermissions(uniquePermissions);
    const result = queriedPermissions
      .map((permission: any) => permission.privileges)
      .flat();

    return result.filter(
      (permission, index, self) =>
        index ===
        self.findIndex(
          (t) =>
            t.action === permission.action && t.subject === permission.subject,
        ),
    );
  }

  private async fetchPermissions(permissionIds: string[]) {
    return await this.permissionModel
      .find({ _id: { $in: permissionIds } })
      .select('privileges')
      .exec();
  }

  create(createRolesDto: CreateRolesDto) {
    const permissions = createRolesDto.permissions.map(
      (permission) => new Types.ObjectId(permission),
    );

    return new this.roleModel({ ...createRolesDto, permissions }).save();
  }

  async findAll(
    rolesPaginationDto: GetRolesPaginationDto,
  ): Promise<GetRolesResponseDto> {
    const { queries, page, limit, ...rest } = rolesPaginationDto;
    let search = rest || ({} as GetRolesPaginationDto);
    if (queries) {
      const searchfield = { $regex: new RegExp(queries), $options: 'i' };
      search = {
        ...search,
        $or: [{ name: searchfield }],
      } as any;
    }

    const roles = await this.roleModel
      .find(search)
      .skip((page - 1) * limit)
      .limit(limit)
      .populate({ path: 'permissions', select: '_id name' })
      .lean();

    const countUsersPerRole =
      await this.userBranchRolesService.countUsersPerRole();

    const userCountByRoleId = countUsersPerRole.reduce((map, item) => {
      map[item.roleId] = item.userCount;
      return map;
    }, {});

    const result = roles.map((role) => {
      return {
        ...role,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        totalUsers: userCountByRoleId[String(role._id)] || 0,
      };
    });

    const meta = await paginate(limit, page, this.roleModel, search);
    return { result, meta };
  }

  async findAbstract() {
    const result = await this.roleModel.find().select('_id name').exec();
    return { result: result };
  }

  async findOne(_id: string) {
    return await this.roleModel
      .findOne({ _id: _id })
      .populate({ path: 'permissions', select: 'id name' });
  }

  async update(_id: string, updateRolesDto: UpdateRolesDto) {
    return await this.roleModel.updateOne({ _id }, updateRolesDto);
  }

  async remove(_id: string) {
    return await this.roleModel.findOneAndDelete({ _id });
  }

  public async abstract(rolesDto: rolesDto): Promise<Array<Role>> {
    return await this.roleModel
      .find({ _id: { $in: rolesDto.ids } })
      .select('_id name')
      .exec();
  }
}
