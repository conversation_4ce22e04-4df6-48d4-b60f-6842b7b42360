import { Test, TestingModule } from '@nestjs/testing';
import { RolesController } from './roles.controller';
import { RolesService } from './roles.service';
import { getModelToken } from '@nestjs/mongoose';
import { UserBranchesRolesService } from '../user-branches-roles/user-branches-roles.service';
import { CaslAbilityFactory } from '../casl/casl-ability.factory';

describe('RolesController', () => {
  let controller: RolesController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RolesController],
      providers: [
        RolesService,
        { provide: getModelToken('Role'), useValue: {} },
        { provide: getModelToken('Permission'), useValue: {} },
        {
          provide: UserBranchesRolesService,
          useValue: {},
        },
        {
          provide: CaslAbilityFactory,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<RolesController>(RolesController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
