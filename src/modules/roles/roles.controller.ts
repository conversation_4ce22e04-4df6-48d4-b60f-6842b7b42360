import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { RolesService } from './roles.service';
import { CreateRolesDto, UpdateRolesDto } from './dtos/create-roles.dto';
import { GetRolesPaginationDto } from './dtos/get-roles.dto';
import { ApiHeader, ApiTags } from '@nestjs/swagger';
import { CaslGuard } from '../casl/guards/casl.guard';
import { Abilities } from '../casl/guards/abilities.decorator';

@ApiTags('roles')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  example: '64956ef5f506e9edf21df4e8',
  required: true,
})
@Controller('users/roles')
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  @UseGuards(CaslGuard)
  @Abilities({ action: 'create', subject: 'roles_and_permissions' })
  @Post()
  public create(@Body() body: CreateRolesDto) {
    return this.rolesService.create(body);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'roles_and_permissions' })
  @Get()
  public async findAll(@Query() getRolesPaginationDto: GetRolesPaginationDto) {
    return this.rolesService.findAll(getRolesPaginationDto);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'abstract_roles_and_permissions' })
  @Get('abstract')
  public async abstract() {
    return this.rolesService.findAbstract();
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'read', subject: 'roles_and_permissions' })
  @Get(':id')
  public async find(@Param('id') _id: string) {
    return this.rolesService.findOne(_id);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'edit', subject: 'roles_and_permissions' })
  @Patch(':id')
  public async update(
    @Param('id') _id: string,
    @Body() updateRolesDto: UpdateRolesDto,
  ) {
    return this.rolesService.update(_id, updateRolesDto);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'delete', subject: 'roles_and_permissions' })
  @Delete(':id')
  public async delete(@Param('id') _id: string) {
    return this.rolesService.remove(_id);
  }
}
