import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ItemExplorerService } from './item-explorer.service';
import { Abilities } from '../casl/guards/abilities.decorator';
import { ItemExplorerQueryDto } from './dto/item-explorer.dto';
import { CaslGuard } from '../casl/guards/casl.guard';
import { ApiHeader, ApiTags } from '@nestjs/swagger';

@ApiTags('item-explorer')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@Controller('inventory/item-explorer')
export class ItemExplorerController {
  constructor(private readonly itemExplorerService: ItemExplorerService) {}
  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'item' })
  @Get('')
  itemExplorer(@Query() query: ItemExplorerQueryDto) {
    return this.itemExplorerService.itemExplorer(query);
  }
}
