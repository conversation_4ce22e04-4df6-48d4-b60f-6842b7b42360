import {
  IsDate<PERSON><PERSON>,
  IsEnum,
  IsNotEmpty,
  <PERSON>Optional,
  IsString,
  Min,
} from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { Transform } from 'class-transformer';
import { IsNumber } from '@nestjs/class-validator';
import { searchTypeEnum } from '../../inventory-item/types/search-types.enum';
import { PaginationDto } from './../../../utils/dto/pagination.dto';
import { itemType } from '../../../modules/inventory-item/types/item-types.enum';
import { availabilityEnum } from '../enums/availability.enum';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';

export class ItemExplorerQueryDto extends PaginationDto {
  @ApiProperty({
    name: 'store',
    type: 'string',
    required: false,
  })
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  @IsNotEmpty()
  store: Types.ObjectId;

  @ApiProperty({ type: 'string' })
  @IsString()
  @IsOptional()
  @IsEnum(itemType)
  type?: itemType;

  @IsOptional()
  @IsEnum(searchTypeEnum)
  search_type?: searchTypeEnum = searchTypeEnum.contains;

  @ApiProperty({ required: false })
  @IsEnum(availabilityEnum)
  @IsOptional()
  availability?: availabilityEnum;

  @ApiProperty()
  @IsOptional()
  @Transform(({ value }) => +value)
  @IsNumber()
  item_group?: number;

  @IsDateString()
  @IsOptional()
  date_added?: string;

  @Transform(({ value }) => +value)
  @IsNumber()
  @Min(0)
  @IsOptional()
  to_price?: number;

  @Transform(({ value }) => +value)
  @IsNumber()
  @Min(0)
  @IsOptional()
  from_price?: number;
}
