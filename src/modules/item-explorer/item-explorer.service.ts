import { Injectable } from '@nestjs/common';
import { ItemsService } from '../inventory-item/items.service';
import { ItemExplorerQueryDto } from './dto/item-explorer.dto';
import { itemType } from '../inventory-item/types/item-types.enum';
import { availabilityEnum } from './enums/availability.enum';

@Injectable()
export class ItemExplorerService {
  constructor(private readonly itemService: ItemsService) {}
  async itemExplorer(query: ItemExplorerQueryDto) {
    const documents = await this.itemService.itemExplorer(query);
    console.log('documents', documents);
    const response = { ...documents };
    response.result = [];
    for (let index = 0; index < documents.result.length; index++) {
      const itemData = documents.result[index];
      itemData.unit = itemData.selected_unit.unit_data;
      itemData.price = itemData.selected_unit.sales_price;
      itemData.quantity = itemData.selected_unit?.quantity || 0;

      if (itemData.type === itemType.goods) {
        if (
          typeof itemData.availability === 'string' &&
          itemData.store_inventory
        ) {
          itemData.snap_shot = true;
        }
        response.result.push({ ...itemData });
      } else if (itemData.type === itemType.service) {
        response.result.push({
          ...itemData,
          availability: availabilityEnum.out_of_stock,
          quantity: 0,
          price: itemData.selected_unit.sales_price,
          snap_shot: false,
        });
      }
    }
    return response;
  }
}
