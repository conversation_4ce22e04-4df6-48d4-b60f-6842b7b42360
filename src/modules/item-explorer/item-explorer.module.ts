import { Module } from '@nestjs/common';
import { ItemExplorerService } from './item-explorer.service';
import { ItemExplorerController } from './item-explorer.controller';
import { ItemsModule } from '../inventory-item/items.module';
import { UnitModule } from '../unit/unit.module';

@Module({
  imports: [ItemsModule, UnitModule],
  controllers: [ItemExplorerController],
  providers: [ItemExplorerService],
})
export class ItemExplorerModule {}
