import { HttpException, Injectable, Scope } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ObjectId, Types } from 'mongoose';
import { paginate } from '../../utils/dto/pagination.dto';
import { RequestWithUser } from '../auth/interfaces/authorize.interface';
import { CreateStoreDto } from './dto/create-store.dto';
import { GetStoreDto, GetStoresPaginationDto } from './dto/find-item.dto';
import { UpdateStoreDto } from './dto/update-store.dto';
import { Store } from './schema/store.schema';
import { nameOf } from '../../utils/object-key-name';
import { usersExceptionCode } from '../../exceptions/exception-code.users';

@Injectable({ scope: Scope.REQUEST, durable: true })
export class StoreService {
  constructor(
    @InjectModel(Store.name) private readonly storeModel: Model<Store>,
  ) {}
  async create(request: RequestWithUser, createStoreDto: CreateStoreDto) {
    if (typeof createStoreDto.number == 'number') {
      const numberExist = await this.storeModel.findOne({
        number: createStoreDto.number,
      });
      if (numberExist) {
        throw new HttpException(
          nameOf(usersExceptionCode, (exception) => exception.numberDuplicated),
          usersExceptionCode.numberDuplicated,
        );
      }
    }

    if (createStoreDto.is_default === true) {
      await this.storeModel.updateMany(
        {
          branch: createStoreDto.branch,
          is_default: true,
        },
        { is_default: false },
      );
    }
    const storeExists = await this.findone({ is_default: true });
    if (!storeExists) {
      createStoreDto.is_default = true;
    }
    return new this.storeModel(createStoreDto).save();
  }

  async findByBranch(branch: Types.ObjectId = null) {
    const query: any = {};
    if (branch) {
      query.branch = branch;
    }
    return await this.storeModel.find(query);
  }

  async findAll(
    branch: Types.ObjectId,
    req: RequestWithUser,
    query: GetStoresPaginationDto,
  ): Promise<any> {
    const { limit, page, queries, ...rest } = query;
    let search = { ...rest, branch };
    if (queries) {
      const searchfield = { $regex: new RegExp(queries), $options: 'i' };
      search = {
        ...search,
        $or: [
          { arabic_name: searchfield },
          { english_name: searchfield },
          { item_number: searchfield },
          { abbreviation: searchfield },
          { item_group: searchfield },
          { active: searchfield },
          { store: searchfield },
        ],
      } as any;
    }
    const result = await this.storeModel
      .find(search)
      .skip((page - 1) * limit)
      .limit(limit)
      .lean();

    const meta = await paginate(limit, page, this.storeModel, queries);
    return { meta, result };
  }
  async findAllDesktop(
    req: RequestWithUser,
    query: GetStoresPaginationDto,
  ): Promise<any> {
    const { limit, page, queries, ...rest } = query;
    let search = { ...rest };
    if (queries) {
      const searchfield = { $regex: new RegExp(queries), $options: 'i' };
      search = {
        ...search,
        $or: [
          { arabic_name: searchfield },
          { english_name: searchfield },
          { item_number: searchfield },
          { abbreviation: searchfield },
          { item_group: searchfield },
          { active: searchfield },
          { store: searchfield },
        ],
      } as any;
    }
    const result = await this.storeModel
      .find(search)
      .skip((page - 1) * limit)
      .limit(limit)
      .lean();

    const meta = await paginate(limit, page, this.storeModel, queries);
    return { meta, result };
  }

  async abstract(branch: Types.ObjectId): Promise<any> {
    const result = await this.storeModel
      .find({ branch: branch })
      .select('_id name is_default')
      .lean();

    return { result: result };
  }

  async findById(req: RequestWithUser, id: ObjectId) {
    const store = await this.storeModel.findById(id).lean();
    return store;
  }

  findone(data: GetStoreDto) {
    return this.storeModel.findOne(data);
  }

  async update(
    branch: Types.ObjectId,
    _id: ObjectId,
    updateStoreDto: UpdateStoreDto,
  ) {
    if (updateStoreDto.is_default === true) {
      await this.storeModel.updateMany(
        {
          branch,
          is_default: true,
        },
        { is_default: false },
      );
    }
    return this.storeModel.updateOne({ _id }, updateStoreDto);
  }

  async remove(_id: string) {
    return this.storeModel.deleteOne({ _id });
  }

  async findByIds(ids: Array<string>) {
    return this.storeModel.find({ _id: { $in: ids } });
  }

  async hasAny(): Promise<boolean> {
    const storeCount = await this.storeModel.countDocuments();
    return storeCount > 0 ? true : false;
  }
}
