import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { Api<PERSON><PERSON><PERSON>A<PERSON>, <PERSON>pi<PERSON>eader, <PERSON>pi<PERSON>aram, ApiTags } from '@nestjs/swagger';
import { ObjectId } from 'mongoose';
import { RequestWithUser } from '../auth/interfaces/authorize.interface';
import { Abilities } from '../casl/guards/abilities.decorator';
import { CaslGuard } from '../casl/guards/casl.guard';
import { CreateStoreDto } from './dto/create-store.dto';
import { GetStoresPaginationDto } from './dto/find-item.dto';
import { UpdateStoreDto } from './dto/update-store.dto';
import { StoreService } from './store.service';
import { RequestHeaders } from '../../utils/decorators/request-header.validator';
import { BranchHeaderDto } from '../../utils/dto/request-headers.dto';
import { authType } from '../auth/guards/auth-type.decorator';
import { ItemsService } from '../inventory-item/items.service';
import { GetItemDto } from '../inventory-item/dto/get-items.dto';

@Controller('inventory/stores')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@ApiTags('store')
export class StoreController {
  constructor(
    private readonly storeService: StoreService,
    private readonly itemsService: ItemsService,
  ) {}

  @Post()
  @UseGuards(CaslGuard)
  @Abilities({ action: 'create', subject: 'store' })
  async create(
    @Req() request: RequestWithUser,
    @Body() createStoreDto: CreateStoreDto,
  ) {
    return this.storeService.create(request, createStoreDto);
  }

  @Get()
  @authType('both')
  @ApiBearerAuth('TOKEN')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'store' })
  findAll(
    @RequestHeaders() headers: BranchHeaderDto,
    @Req() req: RequestWithUser,
    @Query() query: GetStoresPaginationDto,
  ) {
    const { branch } = headers;
    return this.storeService.findAll(branch, req, query);
  }
  @Get('desktop')
  @authType('both')
  @ApiBearerAuth('TOKEN')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'store' })
  findAllDesktop(
    @Req() req: RequestWithUser,
    @Query() query: GetStoresPaginationDto,
  ) {
    return this.storeService.findAllDesktop(req, query);
  }

  @Get('abstract')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'abstract_stores' })
  abstract(@RequestHeaders() headers: BranchHeaderDto) {
    const { branch } = headers;
    return this.storeService.abstract(branch);
  }

  @ApiParam({ name: 'id' })
  @UseGuards(CaslGuard)
  @Abilities({ action: 'read', subject: 'store' })
  @Get(':id')
  findOne(@Req() req: RequestWithUser, @Param('id') id: ObjectId) {
    return this.storeService.findById(req, id);
  }

  @ApiParam({ name: '_id' })
  @ApiParam({ name: 'item_id' })
  @Get('/:_id/items/:item_id')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'read', subject: 'store' })
  @Get()
  async get(
    @Req() req: RequestWithUser,
    @Param('_id') _id: string,
    @Param('item_id') item_id: string,
  ): Promise<{ result: GetItemDto }> {
    const result = await this.itemsService.searchById(_id, item_id);
    return { result };
  }

  @ApiParam({ name: 'id' })
  @UseGuards(CaslGuard)
  @Abilities({ action: 'edit', subject: 'store' })
  @Patch(':id')
  update(
    @Param('id') id: ObjectId,
    @Body() updateStoreDto: UpdateStoreDto,
    @RequestHeaders() headers: BranchHeaderDto,
  ) {
    const { branch } = headers;

    return this.storeService.update(branch, id, updateStoreDto);
  }

  @ApiParam({ name: 'id' })
  @UseGuards(CaslGuard)
  @Abilities({ action: 'delete', subject: 'store' })
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.storeService.remove(id);
  }
}
