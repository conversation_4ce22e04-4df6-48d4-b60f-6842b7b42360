import {
  IsBoolean,
  IsNotEmpty,
  IsObject,
  IsPhoneNumber,
  IsString,
  ValidateNested,
  IsNumber,
  Length,
  Validate,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { Transform, Type } from 'class-transformer';
import { IsOptional } from '@nestjs/class-validator';
import {
  NameDto,
  validateLanguage,
  validateEnLanguage,
} from '../../../utils/dto/name.dto';
import { toLowerCaseObjectKeys } from '../../../utils/tolowerCaseObjectKeys';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';

export class NationalAddress {
  @ApiProperty({
    description: 'short address of NationalAddress',
    example: 'short address',
    required: false,
  })
  @IsString()
  @IsOptional()
  short_address: string;

  @ApiProperty({
    description: 'street of NationalAddress',
    example: 'abraham street',
    required: false,
  })
  @IsString()
  @IsOptional()
  street: string;

  @ApiProperty({
    description: 'district of NationalAddress',
    example: 'district',
    required: false,
  })
  @IsString()
  @IsOptional()
  district: string;

  @ApiProperty({
    description: 'city of NationalAddress',
    example: 'london',
    required: false,
  })
  @IsString()
  @IsOptional()
  city: string;

  @ApiProperty({
    description: 'building number of NationalAddress',
    example: '0123',
    required: false,
  })
  @IsString()
  @IsOptional()
  building_no: string;

  @ApiProperty({
    description: 'secondary_no of NationalAddress',
    example: '123234',
    required: false,
  })
  @IsString()
  @IsOptional()
  secondary_no: string;

  @ApiProperty({
    description: 'secondary_no of NationalAddress',
    example: '11111',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Length(5, 25)
  postal_code: string;

  @ApiProperty({
    description: 'governorate of NationalAddress',
    example: 'governorate',
    required: false,
  })
  @IsString()
  @IsOptional()
  governorate: string;
}
export class CreateStoreDto {
  @ApiProperty({
    description: 'store number',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  number: number;

  @ApiProperty({ type: NameDto })
  @Transform((param) => toLowerCaseObjectKeys(param.value))
  @Validate(validateLanguage)
  @Validate(validateEnLanguage)
  name: NameDto;

  @ApiProperty({
    description: 'is default store',
    example: false,
    required: false,
  })
  @Transform(({ value }) => value === true)
  @IsBoolean()
  @IsOptional()
  is_default: boolean;

  @ApiProperty({
    description: 'branch of inventory item ',
    example: '507f1f77bcf86cd799439011',
    required: true,
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch: Types.ObjectId;

  @ApiProperty({
    description: 'phone of store',
    example: '+989370416208',
    required: false,
  })
  @IsOptional()
  @IsNotEmpty()
  phone: string;

  @ApiProperty({
    description: 'fax of store',
    example: '+989370416208',
    required: false,
  })
  @IsPhoneNumber()
  @IsOptional()
  fax: string;

  @ValidateNested()
  @IsObject()
  @IsOptional()
  @Type(() => NationalAddress)
  national_address: NationalAddress;
}
