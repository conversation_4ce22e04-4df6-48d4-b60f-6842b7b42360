import { PartialType } from '@nestjs/mapped-types';
import { Type } from 'class-transformer';
import { IsInt, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { CreateStoreDto } from './create-store.dto';

export class GetStoreDto extends PartialType(CreateStoreDto) {
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  public _id?: string;
}
export class GetStoresPaginationDto extends PartialType(GetStoreDto) {
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  public page?: number;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  public limit?: number;

  @IsString()
  @IsOptional()
  queries?: string;
}
