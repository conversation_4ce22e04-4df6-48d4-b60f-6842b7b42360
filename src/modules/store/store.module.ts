import { Store, storeSchema } from './schema/store.schema';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { StoreController } from './store.controller';
import { StoreService } from './store.service';
import { ItemsModule } from '../inventory-item/items.module';

@Module({
  imports: [
    ItemsModule,
    MongooseModule.forFeature([{ name: Store.name, schema: storeSchema }]),
  ],
  controllers: [StoreController],
  providers: [StoreService],
  exports: [StoreService],
})
export class StoreModule {}
