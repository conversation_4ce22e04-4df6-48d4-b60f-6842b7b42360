import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Types } from 'mongoose';
import { NameDto } from '../../../utils/dto/name.dto';

export class NationalAddress {
  @Prop({ type: String, required: false })
  short_address: string;

  @Prop({ type: String, required: false })
  street: string;

  @Prop({ type: String, required: false })
  district: string;

  @Prop({ type: String, required: false })
  city: string;

  @Prop({ type: String, required: false })
  building_no: string;

  @Prop({ type: String, required: false })
  secondary_no: string;

  @Prop({ type: String, required: false })
  postal_code: string;

  @Prop({ type: String, required: false })
  governorate: string;
}

@Schema({ timestamps: true })
export class Store {
  @Prop({ type: Number, required: true })
  number: number;

  @Prop({ type: NameDto, required: true })
  name: NameDto; //TODO: change name

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Branch',
    required: true,
  })
  branch: Types.ObjectId;

  @Prop({
    type: String,
    required: false,
  })
  phone: string;

  @Prop({
    type: String,
    required: false,
  })
  fax: string;

  @Prop({
    type: NationalAddress,
    required: false,
  })
  national_address: NationalAddress;

  @Prop({
    type: Boolean,
    default: false,
  })
  is_default: boolean;
}

export const storeSchema = SchemaFactory.createForClass(Store);
storeSchema.index({ number: 1, branch: 1 }, { unique: true });
