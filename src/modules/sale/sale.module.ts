import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PartnerModule } from '../partners/partner.module';
import { TemplateDesignModule } from '../template-design/template-design.module';
import { SaleController } from './sale.controller';
import { SaleService } from './sale.service';
import { invoiceSchema } from './schema/invoice.schema';
import { ReturnSaleModule } from '../return-sale/return-sale.module';
import { StoreModule } from '../store/store.module';
import { ItemsModule } from '../inventory-item/items.module';
import { BranchModule } from '../branch/branch.module';
import { CompanyModule } from '../company/company.module';
import {
  SalesRepresentative,
  salesRepresentativeSchema,
} from '../sales-representative/schema/sales-representative.schema';
import { UnitModule } from '../unit/unit.module';
import { PaymentTypesModule } from '../payment-types/payment-types.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Sale', schema: invoiceSchema },
      { name: SalesRepresentative.name, schema: salesRepresentativeSchema },
    ]),
    PartnerModule,
    TemplateDesignModule,
    StoreModule,
    ItemsModule,
    BranchModule,
    CompanyModule,
    UnitModule,
    PaymentTypesModule,
    forwardRef(() => ReturnSaleModule),
  ],
  controllers: [SaleController],
  providers: [SaleService],
  exports: [SaleService],
})
export class SaleModule {}
