import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { CreateTransactionItemDto } from './create-transaction-item.dto';
import { invoiceType } from './enums';
import { PrintOptionsDto } from './print-options.dto';
import { PaymentTypesDto } from './payment-types.dto';
import { Types } from 'mongoose';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';
import { isTwoDecimalPlaces } from '../../../utils/decorators/number-two-digits-places';

export class CreateInvoiceDto {
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  invoice_no: number;

  @Transform((param) => param.value.toLowerCase())
  @ApiProperty({ enum: invoiceType })
  @IsEnum(invoiceType)
  invoice_type: invoiceType;

  @ApiProperty()
  @IsBoolean()
  registered_customer: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  free_tax: boolean = false;

  @ApiProperty()
  @IsOptional()
  @IsString()
  sale_order_id: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  customer_name: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  customer_name_ar: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  customer_id: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  customer_phone: string;

  @ApiProperty()
  @IsOptional()
  @IsArray()
  @ValidateNested()
  @Type(() => PaymentTypesDto)
  payment_types: PaymentTypesDto[];

  @ApiProperty()
  @IsOptional()
  @IsString()
  tax_no: string;

  @ApiProperty({ example: '2022-11-10' })
  @IsOptional()
  @IsDateString()
  invoice_date: string;

  @ApiProperty({ example: '2022-11-10' })
  @IsOptional()
  @IsDateString()
  due_date: Date;

  @ApiProperty()
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  store: Types.ObjectId;

  @ApiProperty({
    description: 'sales_representative id',
    example: '507f191e810c19729de860ea',
    required: false,
  })
  @IsOptional()
  @IsString()
  sales_representative: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  note: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  reference: string;

  @ApiProperty({
    description: 'is percentage discount implemented',
    example: 'false | true',
    required: false,
  })
  @IsBoolean()
  is_percentage_discount: boolean = false;

  @ApiProperty({
    default: 0,
  })
  @IsNumber()
  @isTwoDecimalPlaces()
  invoice_discount_vat: number = 0;

  @ApiProperty({
    default: 0,
  })
  @IsNumber()
  @isTwoDecimalPlaces()
  invoice_discount: number = 0;

  @ApiHideProperty()
  invoice_discount_percentage: number = 0;

  @ApiProperty()
  @IsNumber()
  @isTwoDecimalPlaces()
  total_discounts: number;

  @ApiProperty()
  @IsNumber()
  @isTwoDecimalPlaces()
  total_vat: number;

  @ApiProperty()
  @IsNumber()
  @isTwoDecimalPlaces()
  invoice_total: number;

  @ApiProperty()
  @IsNumber()
  item_total: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  paid: number;

  @ApiProperty()
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch_id: Types.ObjectId;

  @ApiProperty()
  @IsArray()
  @ValidateNested()
  @Type(() => CreateTransactionItemDto)
  transactions: CreateTransactionItemDto[];

  @ApiHideProperty()
  qr_code: string;

  @ApiProperty()
  @ValidateNested()
  @Type(() => PrintOptionsDto)
  printOptions: PrintOptionsDto;

  @ApiHideProperty()
  customerData;
}
