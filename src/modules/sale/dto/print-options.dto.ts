import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsEnum,
  IsIn,
  IsOptional,
  IsString,
} from 'class-validator';

export enum printingType {
  thermal = 'thermal',
  normal = 'normal',
}

export class PrintOptionsDto {
  @ApiProperty({ example: 'en' })
  @IsString()
  @IsIn(['en', 'ar'])
  templateLang: string;

  @ApiProperty({ example: '<EMAIL>', required: false })
  @IsOptional()
  @IsEmail()
  email: string;

  @ApiProperty({ example: '+201122334455', required: false })
  @IsOptional()
  @IsString()
  phoneNumber: string;

  @ApiProperty()
  @IsBoolean()
  sendEmail: boolean;

  @ApiProperty()
  @IsBoolean()
  sendSms: boolean;

  @ApiProperty()
  @IsBoolean()
  print: boolean;

  @ApiProperty()
  @IsEnum(printingType)
  print_type: printingType = printingType.normal;
}
