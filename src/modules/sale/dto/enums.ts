export enum invoiceType {
  cash = 'cash',
  credit = 'credit',
}

export enum inventoryJournalDocumentTypes {
  sales_invoice = 'sales_invoice',
  sales_return_invoice = 'sales_return_invoice',
  purchase_invoice = 'purchase_invoice',
  purchase_return_invoice = 'purchase_return_invoice',
  sales_order = 'sales_order',
}

export enum inventoryJournalTransactionType {
  add = 'add',
  sub = 'sub',
}

export enum journalTypes {
  receipt_voucher = 'receipt_voucher',
  general = 'general',
  purchase_invoice = 'purchase_invoice',
  purchase_return_invoice = 'purchase_return_invoice',
  sales_invoice = 'sales_invoice',
  sales_order = 'sales_order',
  sales_return_invoice = 'sales_return_invoice',
  memo = 'memo',
}

export enum invoicePaymentType {
  cash = 'cash',
  credit_card = 'credit_card',
  bank_transfer = 'bank_transfer',
  debit = 'debit',
}

export enum saleInvoiceStatus {
  active = 'active',
  expired = 'expired',
  setteled = 'setteled',
}

export enum itemType {
  service = 'service',
  goods = 'goods',
}
