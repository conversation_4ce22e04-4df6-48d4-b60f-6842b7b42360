import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
} from 'class-validator';
import { invoiceType } from './enums';
import { Types } from 'mongoose';

@ValidatorConstraint({ name: 'paymentTypeValidation', async: false })
export class PaymentValidation implements ValidatorConstraintInterface {
  validate(paymentType: string, args: ValidationArguments) {
    if (
      JSON.parse(JSON.stringify(args.object)).invoice_type === invoiceType.cash
    ) {
      return Types.ObjectId.isValid(paymentType);
    } else if (
      JSON.parse(JSON.stringify(args.object)).invoice_type ===
      invoiceType.credit
    ) {
      return paymentType ? Types.ObjectId.isValid(paymentType) : true;
    }
  }

  defaultMessage(args: ValidationArguments) {
    if (
      JSON.parse(JSON.stringify(args.object)).invoice_type === invoiceType.cash
    ) {
      return ' payment type should be valid mongo id';
    }
    if (
      JSON.parse(JSON.stringify(args.object)).invoice_type ===
      invoiceType.credit
    ) {
      return 'payment type should be empty or valid mongoid';
    }
  }
}
