import { ApiHideProperty, ApiProperty, PartialType } from '@nestjs/swagger';
import { IsString } from 'class-validator';
import { CreateInvoiceDto } from './create-invoice.dto';

export class UpdateInvoiceDto extends PartialType(CreateInvoiceDto) {
  @ApiHideProperty()
  deletedAt?: Date;
}

export class UpdateInvoiceBulkDto {
  updateData: UpdateInvoiceWithIdDto[];
}

export class UpdateInvoiceWithIdDto extends UpdateInvoiceDto {
  @ApiProperty()
  @IsString()
  id: string;
}
