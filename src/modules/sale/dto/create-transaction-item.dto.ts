import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';
import { ObjectId } from 'mongoose';
import { Transform } from 'class-transformer';
import { itemType } from './enums';
import { isTwoDecimalPlaces } from '../../../utils/decorators/number-two-digits-places';

export class CreateTransactionItemDto {
  @ApiProperty()
  @IsString()
  item: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  item_invoice_name: string;

  @Transform((param) => param.value.toLowerCase())
  @ApiProperty({ enum: itemType })
  @IsNotEmpty()
  @IsEnum(itemType)
  item_type: itemType;

  @ApiProperty()
  @IsString()
  unit: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  free_tax: boolean = false;

  @ApiProperty()
  @IsNumber()
  qty: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @isTwoDecimalPlaces()
  price: number;

  @ApiProperty()
  @IsNumber()
  @isTwoDecimalPlaces()
  subtotal: number;

  @ApiHideProperty()
  discount_percentage: number = 0;

  @ApiProperty({
    default: 0,
  })
  @IsNumber()
  @isTwoDecimalPlaces()
  discount: number;

  @ApiProperty()
  @IsNumber()
  @isTwoDecimalPlaces()
  vat: number;

  @ApiProperty()
  @IsNumber()
  @isTwoDecimalPlaces()
  total: number;

  @ApiProperty({
    default: 0,
  })
  @IsNumber()
  @isTwoDecimalPlaces()
  row_invoice_discount: number = 0;

  @ApiProperty({
    default: 0,
  })
  @IsNumber()
  @isTwoDecimalPlaces()
  row_invoice_discount_vat: number;

  @ApiProperty({ default: 0 })
  @IsOptional()
  @IsNumber()
  returned_qty: number = 0;

  @ApiHideProperty()
  // eslint-disable-next-line @typescript-eslint/naming-convention
  itemObject?: { _id: ObjectId; units_prices: Array<any> };

  @ApiHideProperty()
  profit_margin?: number;

  @ApiHideProperty()
  average_cost?: number;

  @ApiHideProperty()
  remaining_average_cost?: number;

  @ApiHideProperty()
  remaining_subtotal: number;

  @ApiHideProperty()
  remaining_discount: number;

  @ApiHideProperty()
  remaining_vat: number;

  @ApiHideProperty()
  remaining_row_invoice_discount: number;

  @ApiHideProperty()
  remaining_row_invoice_discount_vat: number;
}
