import { HttpException } from '@nestjs/common';
import { nameOf } from '../../utils/object-key-name';
import { usersExceptionCode } from '../../exceptions/exception-code.users';
import { CustomHttpException } from '../../utils/custom-http.exception';
import { Calculator } from '../../utils/calculator';

export async function validateTransactions(
  transactions,
  itemsService,
  code,
  store,
  taxRate,
  is_percentage_discount,
  invoice_discount,
  total_discounts,
  invoice_discount_vat,
  total_vat,
  invoice_total,
  free_tax,
) {
  // check items and units and get to populate it
  const items = transactions.map((transaction) => transaction.item);
  const itemsData = await itemsService.findByIds(items, store);

  for (const transaction of transactions) {
    const itemObject = itemsData.find(
      (item) => String(item.id) === String(transaction.item),
    );

    if (!itemObject) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.transactionDoesNotHaveItems,
        ),
        usersExceptionCode.transactionDoesNotHaveItems,
      );
    }
    transaction.itemObject = itemObject;
    console.log('itemObject', itemObject);
    const unitItem = transaction.itemObject.units.find(
      (unit) => String(unit.unit) === String(transaction.unit),
    );
    if (!unitItem) {
      throw new CustomHttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.itemDoesNotHaveUnit,
        ),
        usersExceptionCode.itemDoesNotHaveUnit,
        {
          item: transaction.itemObject,
        },
      );
    }
  }

  const subtotalSumAfterDiscount = transactions.reduce(
    (sum, transaction) =>
      sum +
      Calculator.subtract(transaction.subtotal, transaction.discount).number(),
    0,
  );

  let rowInvoiceDiscountSum = 0;
  let rowInvoiceDiscountVatSum = 0;
  let rowTotals = 0;
  let rowVats = 0;
  let rowDiscount = 0;
  const subtotalAfterDiscountSum = new Calculator();

  // check journal snapshot to calculate profit and quantity
  for (const transaction of transactions) {
    const snapshotItem = transaction.itemObject;
    transaction.average_cost = Number(
      snapshotItem.average_cost_in_base_unit || 0,
    );

    if (is_percentage_discount) {
      transaction.discount_percentage = transaction.discount;
      transaction.discount = Calculator.div(
        Calculator.mul(transaction.subtotal, transaction.discount).number(),
        100,
      )
        .round()
        .number();
    }

    const subtotal = Calculator.mul(transaction.price, transaction.qty)
      .round()
      .number();
    if (subtotal !== transaction.subtotal) {
      throw new CustomHttpException(
        nameOf(usersExceptionCode, (exception) => exception.invalidSubtotal),
        usersExceptionCode.invalidSubtotal,
        {
          calculated_subtotal: subtotal,
          submitted_subtotal: transaction.subtotal,
        },
      );
    }

    const subtotalAfterDiscount = Calculator.subtract(
      transaction.subtotal,
      transaction.discount,
    ).number();

    subtotalAfterDiscountSum.plus(subtotalAfterDiscount);

    if (free_tax) {
      if (transaction.vat !== 0) {
        throw new CustomHttpException(
          nameOf(usersExceptionCode, (exception) => exception.freeTaxVat),
          usersExceptionCode.freeTaxVat,
          {
            calculated_vat: 0,
            submitted_vat: transaction.vat,
          },
        );
      }
      transaction.free_tax = true;
    } else {
      if (transaction.itemObject.tax_on_sales) {
        const vat = Calculator.mul(subtotalAfterDiscount, taxRate)
          .round()
          .number();
        if (vat !== transaction.vat) {
          throw new CustomHttpException(
            nameOf(usersExceptionCode, (exception) => exception.invalidVat),
            usersExceptionCode.invalidVat,
            {
              calculated_vat: vat,
              submitted_vat: transaction.vat,
            },
          );
        }
        transaction.free_tax = false;
      } else {
        if (transaction.vat !== 0) {
          throw new CustomHttpException(
            nameOf(usersExceptionCode, (exception) => exception.freeTaxVat),
            usersExceptionCode.freeTaxVat,
            {
              calculated_vat: 0,
              submitted_vat: transaction.vat,
            },
          );
        }
        transaction.free_tax = true;
      }
    }

    rowVats = Calculator.sum(rowVats, transaction.vat).number();

    const rowTotal = Calculator.sum(
      subtotalAfterDiscount,
      transaction.vat,
    ).number();

    if (rowTotal !== transaction.total) {
      throw new CustomHttpException(
        nameOf(
          usersExceptionCode,
          (exception) => exception.invalidTotalWithVat,
        ),
        usersExceptionCode.invalidTotalWithVat,
        {
          calculated_total: rowTotal,
          submitted_total: transaction.total,
        },
      );
    }
    rowTotals = Calculator.sum(rowTotals, rowTotal).number();

    if (transaction !== transactions[transactions.length - 1]) {
      const rowInvoiceDiscount = Calculator.mul(
        Calculator.div(invoice_discount, subtotalSumAfterDiscount).number(),
        Number(subtotalAfterDiscount),
      )
        .round()
        .number();
      if (rowInvoiceDiscount !== transaction.row_invoice_discount) {
        throw new CustomHttpException(
          nameOf(
            usersExceptionCode,
            (exception) => exception.rowInvoiceDiscountNotValid,
          ),
          usersExceptionCode.rowInvoiceDiscountNotValid,
          {
            calculated_row_invoice_discount: rowInvoiceDiscount,
            submitted_row_invoice_discount: transaction.row_invoice_discount,
          },
        );
      }

      rowInvoiceDiscountSum = Calculator.sum(
        rowInvoiceDiscountSum,
        rowInvoiceDiscount,
      ).number();

      let rowInvoiceDiscountVat;
      if (free_tax || !transaction.itemObject.tax_on_sales) {
        rowInvoiceDiscountVat = Calculator.mul(rowInvoiceDiscount, 0)
          .round()
          .number();
      } else {
        rowInvoiceDiscountVat = Calculator.mul(rowInvoiceDiscount, taxRate)
          .round()
          .number();
      }

      if (rowInvoiceDiscountVat !== transaction.row_invoice_discount_vat) {
        throw new CustomHttpException(
          nameOf(
            usersExceptionCode,
            (exception) => exception.rowInvoiceDiscountVatNotValid,
          ),
          usersExceptionCode.rowInvoiceDiscountVatNotValid,
          {
            calculated_row_invoice_discount_vat: rowInvoiceDiscountVat,
            submitted_row_invoice_discount_vat:
              transaction.row_invoice_discount_vat,
          },
        );
      }

      rowInvoiceDiscountVatSum = Calculator.sum(
        rowInvoiceDiscountVatSum,
        rowInvoiceDiscountVat,
      ).number();
    } else {
      console.log('last transaction');

      const rowInvoiceDiscount = Calculator.mul(
        Calculator.div(invoice_discount, subtotalSumAfterDiscount).number() ||
          1,
        Number(subtotalAfterDiscount),
      )
        .round()
        .number();

      const discounts = rowInvoiceDiscountSum + rowInvoiceDiscount;
      const errorCorrectionOfRowInvoiceDiscount = Calculator.subtract(
        invoice_discount,
        discounts,
      ).number();

      const rowInvoiceDiscountAfterErrorCorrection = Calculator.sum(
        rowInvoiceDiscount,
        errorCorrectionOfRowInvoiceDiscount,
      ).number();
      if (
        rowInvoiceDiscountAfterErrorCorrection !==
        transaction.row_invoice_discount
      ) {
        throw new CustomHttpException(
          nameOf(
            usersExceptionCode,
            (exception) => exception.rowInvoiceDiscountNotValid,
          ),
          usersExceptionCode.rowInvoiceDiscountNotValid,
          {
            calculated_last_row_invoice_discount:
              rowInvoiceDiscountAfterErrorCorrection,
            submitted_last_row_invoice_discount:
              transaction.row_invoice_discount,
          },
        );
      }

      rowInvoiceDiscountSum = Calculator.sum(
        rowInvoiceDiscountSum,
        rowInvoiceDiscountAfterErrorCorrection,
      )
        .round()
        .number();

      const result = rowInvoiceDiscount + errorCorrectionOfRowInvoiceDiscount;

      let rowInvoiceDiscountVat;
      if (free_tax || !transaction.itemObject.tax_on_sales) {
        rowInvoiceDiscountVat = Calculator.mul(result, 0).round().number();
      } else {
        rowInvoiceDiscountVat = Calculator.mul(result, taxRate)
          .round()
          .number();
      }

      if (rowInvoiceDiscountVat !== transaction.row_invoice_discount_vat) {
        throw new CustomHttpException(
          nameOf(
            usersExceptionCode,
            (exception) => exception.rowInvoiceDiscountVatNotValid,
          ),
          usersExceptionCode.rowInvoiceDiscountVatNotValid,
          {
            calculated_last_row_invoice_discount_vat: rowInvoiceDiscountVat,
            submitted_last_row_invoice_discount_vat:
              transaction.row_invoice_discount_vat,
          },
        );
      }

      rowInvoiceDiscountVatSum = Calculator.sum(
        rowInvoiceDiscountVatSum,
        rowInvoiceDiscountVat,
      ).number();
    }

    rowDiscount = Calculator.sum(rowDiscount, transaction.discount).number();
    transaction.remaining_average_cost =
      transaction.average_cost * transaction.qty;
    transaction.remaining_subtotal = transaction.subtotal;
    transaction.remaining_discount = transaction.discount;
    transaction.remaining_vat = transaction.vat;
    transaction.remaining_row_invoice_discount =
      transaction.row_invoice_discount;
    transaction.remaining_row_invoice_discount_vat =
      transaction.row_invoice_discount_vat;
  }

  const totalDiscount = Calculator.sum(
    rowDiscount.valueOf(),
    invoice_discount,
  ).number();

  if (totalDiscount !== total_discounts) {
    throw new CustomHttpException(
      nameOf(
        usersExceptionCode,
        (exceptions) => exceptions.totalDiscountNotValid,
      ),
      usersExceptionCode.totalDiscountNotValid,
      {
        calculated_total_discount: totalDiscount,
        submitted_total_discount: total_discounts,
      },
    );
  }

  const totalAfterDiscount = Calculator.subtract(
    subtotalAfterDiscountSum.valueOf(),
    invoice_discount,
  ).number();

  if (rowInvoiceDiscountVatSum !== invoice_discount_vat) {
    throw new CustomHttpException(
      nameOf(
        usersExceptionCode,
        (exceptions) => exceptions.invoiceDiscountVatNotValid,
      ),
      usersExceptionCode.invoiceDiscountVatNotValid,
      {
        calculated_invoice_discount_vat: rowInvoiceDiscountVatSum,
        submitted_invoice_discount_vat: invoice_discount_vat,
      },
    );
  }

  if (free_tax && total_vat !== 0) {
    throw new CustomHttpException(
      nameOf(usersExceptionCode, (exception) => exception.freeTaxVat),
      usersExceptionCode.freeTaxVat,
      {
        calculated_vat: 0,
        submitted_vat: total_vat,
      },
    );
  }

  if (
    !free_tax &&
    Calculator.subtract(rowVats, rowInvoiceDiscountVatSum).number() !==
      total_vat
  ) {
    throw new CustomHttpException(
      nameOf(usersExceptionCode, (exceptions) => exceptions.totalVatNotValid),
      usersExceptionCode.totalVatNotValid,
      {
        // condition vs value is not correct!
        calculated_total_vat: Calculator.subtract(
          rowVats,
          rowInvoiceDiscountVatSum,
        ).number(), // rowInvoiceDiscountVatSum,
        submitted_total_vat: total_vat,
      },
    );
  }

  const finalPrice = Calculator.subtract(
    rowTotals,
    Calculator.sum(invoice_discount, rowInvoiceDiscountVatSum).number(),
  ).number();

  if (finalPrice !== invoice_total) {
    // throw new CustomHttpException(
    //   nameOf(
    //     usersExceptionCode,
    //     (exceptions) => exceptions.finalPriceIsNotValid,
    //   ),
    //   usersExceptionCode.finalPriceIsNotValid,
    //   {
    //     calculated_final_price: +finalPrice,
    //     submitted_final_price: invoice_total,
    //   },
    // );
  }
  return {
    transactions,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    totalAfterDiscount,
  };
}
