import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { invoiceType } from '../dto';
import { TransactionItem, transactionSchema } from './transaction-items.schema';
import { PaymentTypes } from './payment-types.schema';
import { SoftDeleteDocument, softDeletePlugin } from '../../../utils/schemas';
import { Store } from '../../store/schema/store.schema';

export type InvoiceDocument = Invoice & SoftDeleteDocument & Document;

@Schema({ timestamps: true })
export class Invoice {
  @Prop({ required: true })
  invoice_no: number;

  @Prop({ required: true, enum: invoiceType })
  invoice_type: invoiceType;

  @Prop({
    required: true,
  })
  registered_customer: boolean;

  @Prop({ default: false })
  free_tax: boolean;

  @Prop()
  customer_name: string;

  @Prop()
  customer_name_ar: string;

  @Prop({
    type: String,
    ref: 'Partner',
    required: false,
  })
  customer_id?: string;

  @Prop()
  customer_phone: string;

  @Prop({ type: String })
  tax_no: string;

  @Prop({ type: Date })
  invoice_date: Date;

  @Prop({ type: Date })
  due_date: Date;

  @Prop({
    type: Types.ObjectId,
    ref: Store.name,
    required: true,
  })
  store: Types.ObjectId;

  @Prop({
    type: String,
    ref: 'SalesRepresentative',
    required: false,
  })
  sales_representative?: string;

  @Prop()
  note: string;

  @Prop()
  reference: string;

  @Prop({ default: false })
  is_percentage_discount: boolean;

  @Prop({ default: 0 })
  invoice_discount_percentage: number;

  @Prop()
  invoice_discount: number;

  @Prop()
  tax_amount: number;

  @Prop()
  total_discounts: number;

  @Prop()
  total_vat: number;

  @Prop({ required: true })
  invoice_total: number;

  @Prop()
  item_total: number;

  @Prop({ type: Array<PaymentTypes>, default: () => ({}) })
  payment_types: PaymentTypes[];

  @Prop()
  paid: number;

  @Prop()
  qr_code: string;

  @Prop({ required: true })
  branch_id: Types.ObjectId;

  @Prop({ type: [transactionSchema] })
  transactions: TransactionItem[];

  @Prop({ type: Date })
  deletedAt: Date;
}

export const invoiceSchema = SchemaFactory.createForClass(Invoice);
invoiceSchema.index({ branch_id: 1, invoice_no: 1 }, { unique: true });

invoiceSchema.plugin(softDeletePlugin);
