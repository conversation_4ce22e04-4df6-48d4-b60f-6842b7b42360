import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { itemType } from '../dto';
import { Types } from 'mongoose';

@Schema()
export class TransactionItem {
  @Prop({ type: Types.ObjectId, default: () => new Types.ObjectId() })
  _id: Types.ObjectId;

  @Prop({ type: String })
  item: string;

  @Prop({ type: String, required: true })
  item_invoice_name: string;

  @Prop({ required: true, enum: itemType })
  item_type: itemType;

  @Prop({
    required: true,
  })
  unit: string;

  @Prop({ default: false })
  free_tax: boolean;

  @Prop({ type: Number, required: true })
  qty: number;

  @Prop({ type: Number, required: true })
  price: number;

  @Prop({ type: Number, required: true })
  subtotal: number;

  @Prop({ type: Number, default: 0 })
  discount_percentage: number;

  @Prop({ type: Number, default: 0 })
  discount: number;

  @Prop({ type: Number, required: true })
  vat: number;

  @Prop({ type: Number, required: true })
  total: number;

  @Prop({ type: Number, default: 0 })
  row_invoice_discount: number;

  @Prop({ type: Number, default: 0 })
  row_invoice_discount_vat: number;

  @Prop({ type: Number, default: 0 })
  returned_qty: number;

  @Prop({ type: Number, default: 0 })
  remaining_average_cost: number;

  @Prop({ type: Number, default: 0 })
  remaining_subtotal: number;

  @Prop({ type: Number, default: 0 })
  remaining_discount: number;

  @Prop({ type: Number, default: 0 })
  remaining_vat: number;

  @Prop({ type: Number, default: 0 })
  remaining_row_invoice_discount: number;

  @Prop({ type: Number, default: 0 })
  remaining_row_invoice_discount_vat: number;

  @Prop({ type: Number, default: 0 })
  average_cost: number;
}

export const transactionSchema = SchemaFactory.createForClass(TransactionItem);
