import { HttpException, Injectable, Scope } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import { InjectModel } from '@nestjs/mongoose';
import { extend } from 'lodash';
import mongoose, { Model, Types } from 'mongoose';
import { nameOf } from '../../utils/object-key-name';
import { RequestWithUser } from '../auth/interfaces/authorize.interface';
import { CustomerService } from '../partners/customer.service';
import { templateTypeEnum } from '../template-design/dto/create-template-design.dto';
import { TemplateDesignService } from '../template-design/template-design.service';
import {
  CreateInvoiceDto,
  FindInvoiceDesktopDto,
  FindInvoiceDto,
  GetTemplateDto,
  invoiceType,
  UpdateInvoiceDto,
  UpdateInvoiceWithIdDto,
} from './dto';
import { printingType, PrintOptionsDto } from './dto/print-options.dto';
import { VoucherInvoice } from './dto/voucher-invoice.dto';
import { Invoice, InvoiceDocument } from './schema/invoice.schema';
import * as salesTax from 'sales-tax';
import { SoftDeleteModel } from '../../utils/schemas';
import { ReturnSaleService } from '../return-sale/return-sale.service';
import { validateTransactions } from './sale-helper';
import { usersExceptionCode } from '../../exceptions/exception-code.users';
import { Calculator } from '../../utils/calculator';
import { paginate, PaginationMetaType } from '../../utils/dto/pagination.dto';
import { StoreService } from '../store/store.service';
import { ItemsService } from '../inventory-item/items.service';
import { BranchService } from '../branch/branch.service';
import { CompanyService } from '../company/company.service';
import { SalesRepresentative } from '../sales-representative/schema/sales-representative.schema';
import { UnitsService } from '../unit/units.service';
import { PaymentTypesService } from '../payment-types/payment-types.service';
import { TLV } from '../../utils/qr-code';
import { TenantServices } from '../tenants/tenant.service';

@Injectable({ scope: Scope.REQUEST, durable: true })
export class SaleService {
  constructor(
    @InjectModel('Sale')
    private readonly invoiceModel: SoftDeleteModel<InvoiceDocument>,
    @InjectModel(SalesRepresentative.name)
    private readonly salesRepresentativeModel: Model<SalesRepresentative>,
    public readonly returnSaleService: ReturnSaleService,
    private readonly customerService: CustomerService,
    private readonly storeService: StoreService,
    private readonly itemsService: ItemsService,
    private readonly unitsService: UnitsService,
    private readonly branchService: BranchService,
    private readonly companyService: CompanyService,
    private readonly paymentTypesService: PaymentTypesService,
    private readonly templateDesignService: TemplateDesignService,
    private readonly tenantServices: TenantServices,
  ) {}

  async create(
    req: RequestWithUser,
    createInvoiceDto: CreateInvoiceDto,
  ): Promise<any> {
    try {
      const invoiceExists = await this.invoiceModel
        .findOne({
          invoice_no: createInvoiceDto.invoice_no,
          branch_id: createInvoiceDto.branch_id,
          invoice_type: createInvoiceDto.invoice_type,
        })
        .exec();
      if (invoiceExists) {
        throw new HttpException(
          nameOf(usersExceptionCode, (exception) => exception.numberDuplicated),
          usersExceptionCode.numberDuplicated,
        );
      }
      //TODO proper validation for payment types, below code is not useful
      // check first payment type
      // if (createInvoiceDto.payment_types[0]?.id) {
      //   for (const paymentType of createInvoiceDto.payment_types) {
      //     console.log('paymentType', paymentType);
      //     const retrievedPaymentType = await this.paymentTypesService.findOne({
      //       id: paymentType.id,
      //     });

      //     paymentType['payment_type'] = retrievedPaymentType;
      //     console.log('retrievedPaymentType', retrievedPaymentType);
      //   }
      // }
      // get customer if registered_customer is true
      if (
        (createInvoiceDto.invoice_type === invoiceType.credit ||
          createInvoiceDto.registered_customer) &&
        !createInvoiceDto.customer_id
      ) {
        throw new HttpException(
          nameOf(
            usersExceptionCode,
            (exceptions) => exceptions.customerIdRequiredIfRegisterCustomerTrue,
          ),
          usersExceptionCode.customerIdRequiredIfRegisterCustomerTrue,
        );
      }
      if (createInvoiceDto.registered_customer) {
        const customer = await this.customerService.findOnePartner(
          req.user.code,
          createInvoiceDto.customer_id,
        );
        if (
          createInvoiceDto.invoice_type !== invoiceType.cash &&
          customer.cash_only === true
        ) {
          throw new HttpException(
            nameOf(
              usersExceptionCode,
              (exceptions) => exceptions.customerIsCashOnly,
            ),
            usersExceptionCode.customerIsCashOnly,
          );
        }
        createInvoiceDto.customer_name = customer.name.en || customer.name.ar;
        createInvoiceDto.customer_name_ar =
          customer.name.ar || customer.name.en;
        createInvoiceDto.customer_phone = customer.mobile;
        createInvoiceDto.tax_no = customer.tax_code;
        createInvoiceDto.customerData = customer;
      }
      const taxRate = (await salesTax.getSalesTax('SA'))?.rate;

      // check discount type
      if (createInvoiceDto.is_percentage_discount) {
        createInvoiceDto.invoice_discount_percentage =
          createInvoiceDto.invoice_discount;
        createInvoiceDto.invoice_discount = Calculator.div(
          Calculator.mul(
            createInvoiceDto.item_total,
            createInvoiceDto.invoice_discount,
          ),
          100,
        )
          .round()
          .number();

        createInvoiceDto.invoice_discount_percentage =
          createInvoiceDto.invoice_discount;
        createInvoiceDto.invoice_discount = Calculator.div(
          Calculator.mul(
            createInvoiceDto.item_total,
            createInvoiceDto.invoice_discount,
          ),
          100,
        )
          .round()
          .number();
      }
      if (!createInvoiceDto.invoice_no) {
        const lastInvoiceNo =
          (
            await this.invoiceModel
              .findOne({ branch_id: createInvoiceDto.branch_id })
              .sort({ invoice_no: -1 })
              // eslint-disable-next-line @typescript-eslint/naming-convention
              .setOptions({ overrideSoftDelete: true })
          )?.invoice_no || 0;
        createInvoiceDto.invoice_no = lastInvoiceNo + 1;
      }
      // validate transactions
      const validated = await validateTransactions(
        createInvoiceDto.transactions,
        this.itemsService,
        req.user?.code,
        createInvoiceDto.store,
        taxRate,
        createInvoiceDto.is_percentage_discount,
        createInvoiceDto.invoice_discount,
        createInvoiceDto.total_discounts,
        createInvoiceDto.invoice_discount_vat,
        createInvoiceDto.total_vat,
        createInvoiceDto.invoice_total,
        createInvoiceDto.free_tax,
      );
      createInvoiceDto.transactions = validated.transactions;

      const branch = await this.branchService.findByIds([
        String(createInvoiceDto.branch_id),
      ]);

      // create qrCode
      createInvoiceDto.qr_code = await new TLV(
        String(branch[0]?.general_information?.name?.en),
        String(branch[0]?.general_information?.tax_code),
        String(createInvoiceDto.invoice_total),
        String(createInvoiceDto.total_vat),
        new Date(createInvoiceDto.invoice_date).toISOString(),
      )
        .toTlvB64()
        .toQrCode();

      const paidAmount = createInvoiceDto.paid;
      const newInvoice = new this.invoiceModel(createInvoiceDto);
      const savedInvoice = await newInvoice.save();
      createInvoiceDto.paid = paidAmount;

      await savedInvoice.save();
      let renderedTemplate = '';

      if (createInvoiceDto.printOptions?.templateLang) {
        renderedTemplate = await this.createInvoiceTemplate(
          req,
          savedInvoice.id,
          createInvoiceDto.printOptions.templateLang,
          createInvoiceDto.printOptions.print_type,
        );
        await this.sendTemplate(
          renderedTemplate,
          createInvoiceDto.printOptions,
        );
      }

      return {
        template: createInvoiceDto.printOptions?.print ? renderedTemplate : '',
        invoice: savedInvoice,
      };
    } catch (err) {
      throw err;
    }
  }

  async findAll(
    findInvoiceDto: FindInvoiceDto,
    branch: Types.ObjectId,
  ): Promise<{ meta: PaginationMetaType; result: Invoice[] }> {
    const {
      limit,
      page,
      fromDate,
      toDate,
      queries,
      store,
      invoiceType,
      customerId,
      sortBy,
      fullyReturned,
      sortType,
    } = findInvoiceDto;
    let search: any = { $and: [] };
    const sortCriteria = {};
    sortCriteria[sortBy] = sortType === 'desc' ? -1 : 1;
    if (branch) {
      search.$and.push({
        branch_id: { $eq: branch },
      });
    }
    if (invoiceType) {
      search.$and.push({ invoice_type: { $eq: invoiceType } });
    }
    if (fullyReturned) {
      search.$and.push({
        $expr: {
          $not: {
            $eq: [
              { $size: '$transactions' },
              {
                $size: {
                  $filter: {
                    input: '$transactions',
                    as: 'transaction',
                    cond: {
                      $eq: ['$$transaction.qty', '$$transaction.returned_qty'],
                    },
                  },
                },
              },
            ],
          },
        },
      });
    }
    if (customerId) {
      search.$and.push({
        customer_id: { $eq: new mongoose.Types.ObjectId(customerId) },
      });
    }
    if (store) {
      search.$and.push({ store: { $eq: store } });
    }
    if (fromDate) {
      search.$and.push({
        invoice_date: { $gte: new Date(fromDate), $lte: new Date(toDate) },
      });
    }
    if (queries) {
      const searchfield = {
        $regex: new RegExp(`(^|\\s)${queries}`),
        $options: 'i',
      };
      search.$and.push({
        $or: [
          {
            $expr: {
              $regexMatch: {
                input: { $toString: '$invoice_no' },
                regex: new RegExp(`^${queries}`), // Prefix match for invoice_no
                options: 'i',
              },
            },
          },
          {
            customer_name: searchfield,
          },
          {
            customer_name_ar: searchfield,
          },
        ],
      });
    }
    if (search.$and.length === 0) {
      search = {};
    }
    const result = await this.invoiceModel
      .aggregate([
        { $match: search },
        {
          $project: {
            _id: 1,
            invoice_no: 1,
            invoice_type: 1,
            invoice_total: 1,
            invoice_date: 1,
            store: 1,
            registered_customer: 1,
            customer_name: 1,
            customer_name_ar: 1,
            customer_id: 1,
            createdAt: 1,
          },
        },
        {
          $addFields: {
            customer_id_num: { $toInt: '$customer_id' },
          },
        },
        { $sort: sortCriteria },
        { $skip: (page - 1) * limit },
        { $limit: limit },
        {
          $lookup: {
            from: 'partners',
            localField: 'customer_id_num',
            foreignField: 'id',
            pipeline: [{ $project: { name: 1, code: 1 } }],
            as: 'customer_id',
          },
        },
        {
          $addFields: { customer_id: { $arrayElemAt: ['$customer_id', 0] } },
        },
      ])
      .exec();
    const retrievedStores = await this.storeService.findByIds(
      result.map((invoice) => invoice.store),
    );

    result.forEach((invoice) => {
      invoice.store = retrievedStores.find(
        (store) => String(invoice.store) === String(store._id),
      );
    });
    const meta = await paginate(limit, page, this.invoiceModel, search);
    return { meta, result };
  }

  async findAllDesktop(
    findInvoiceDto: FindInvoiceDesktopDto,
    branch: Types.ObjectId,
  ): Promise<{ result: Invoice[] }> {
    const { fromDate, store } = findInvoiceDto;
    let search: any = { $and: [] };
    if (branch) {
      search.$and.push({
        branch_id: { $eq: branch },
      });
    }
    if (store) {
      search.$and.push({ store: { $eq: store } });
    }
    if (fromDate) {
      search.$and.push({
        invoice_date: { $gte: new Date(fromDate) },
      });
    }
    if (search.$and.length === 0) {
      search = {};
    }
    const result = await this.invoiceModel
      .aggregate([{ $match: search }])
      .exec();

    return { result };
  }

  async findByIds(ids) {
    return this.invoiceModel.find({ _id: { $in: ids } });
  }

  async findOneLean(_id) {
    return this.invoiceModel.findOne({ _id });
  }

  async findOne(code: number, _id: string): Promise<Invoice> {
    const invoice: any | Invoice = await this.invoiceModel
      .findOne({ _id })
      .populate({
        path: 'customer_id',
        model: 'Partner',
        localField: 'customer_id',
        foreignField: 'id',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        justOne: true,
      })
      .populate({
        path: 'sales_representative',
        model: 'SalesRepresentative',
        localField: 'sales_representative',
        foreignField: 'id',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        justOne: true,
      })
      .lean();
    if (!invoice) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.invoiceDoesNotExist,
        ),
        usersExceptionCode.invoiceDoesNotExist,
      );
    }
    if (invoice.sales_representative?.id) {
      invoice.sales_representative.id =
        invoice.sales_representative.id.toString();
    }
    const items = invoice.transactions.map((transaction) => transaction.item);

    const itemsData = await this.itemsService.findByIds(items, invoice.store);

    invoice.transactions.forEach((transaction) => {
      transaction.itemObject = itemsData.find(
        (item) => String(item.id) === String(transaction.item),
      );

      if (!transaction.itemObject) {
        throw new HttpException(
          nameOf(
            usersExceptionCode,
            (exceptions) => exceptions.transactionDoesNotHaveItems,
          ),
          usersExceptionCode.transactionDoesNotHaveItems,
        );
      }
    });

    const retrievedStore = await this.storeService.findByIds([invoice.store]);

    invoice.store = retrievedStore[0];
    const units = invoice.transactions.map((transaction) => transaction.unit);
    const retrievedَUnits = await this.unitsService.findByIds(units);

    invoice.transactions.forEach(async (transaction) => {
      transaction.unitObject = retrievedَUnits.find(
        (unit) => String(unit.id) === String(transaction.unit),
      );
    });

    invoice.transactions.forEach(async (transaction) => {
      transaction.tax_percentage =
        !transaction.free_tax && transaction.itemObject.tax_on_sales
          ? (await salesTax.getSalesTax('SA'))?.rate * 100
          : 0;
    });
    const branch = await this.branchService.findByIds([invoice.branch_id]);

    if (branch.length === 0) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.branchDoesNotExist,
        ),
        usersExceptionCode.branchDoesNotExist,
      );
    }
    invoice.total_subtotal = invoice.transactions.reduce(
      (sum, item) => sum + item.subtotal,
      0,
    );
    invoice.total_item_qty = invoice.transactions.reduce(
      (sum, item) => sum + item.qty,
      0,
    );
    invoice.store.branch = branch[0];

    invoice.tax_percentage = !invoice.free_tax
      ? (await salesTax.getSalesTax('SA'))?.rate * 100
      : 0;
    return invoice;
  }

  async update(
    _id: string | Types.ObjectId,
    updateInvoiceDto: UpdateInvoiceDto,
  ): Promise<Invoice> {
    const invoice = await this.invoiceModel.findOne({ _id });
    if (!invoice) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.invoiceDoesNotExist,
        ),
        usersExceptionCode.invoiceDoesNotExist,
      );
    }
    extend(invoice, updateInvoiceDto);
    extend(invoice.transactions, updateInvoiceDto.transactions);
    await invoice.save();
    return this.invoiceModel.findOne({ _id }).exec();
  }

  async updateWithCoordinator(
    req: RequestWithUser,
    _id: string,
    updateInvoiceDto: UpdateInvoiceDto,
  ): Promise<Invoice> {
    const saleInvoice = await this.invoiceModel.findById(_id);
    if (!saleInvoice) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.invoiceDoesNotExist,
        ),
        usersExceptionCode.invoiceDoesNotExist,
      );
    }

    try {
      // get customer if exist
      let customer;
      if (saleInvoice.registered_customer) {
        const objectId = saleInvoice.customer_id;
        customer = await this.customerService.findOnePartner(
          req.user.code,
          objectId as any,
        );
        if (
          saleInvoice.invoice_type !== invoiceType.cash &&
          customer.cash_only === true
        ) {
          throw new HttpException(
            nameOf(
              usersExceptionCode,
              (exceptions) => exceptions.customerIsCashOnly,
            ),
            usersExceptionCode.customerIsCashOnly,
          );
        }
        updateInvoiceDto.customer_name = customer.name.en || customer.name.ar;
        updateInvoiceDto.customer_name_ar =
          customer.name.ar || customer.name.en;
        updateInvoiceDto.customer_phone = customer.mobile;
        updateInvoiceDto.tax_no = customer.tax_code;
      }

      if (updateInvoiceDto.is_percentage_discount) {
        updateInvoiceDto.invoice_discount_percentage =
          updateInvoiceDto.invoice_discount;
        updateInvoiceDto.invoice_discount = Calculator.div(
          Calculator.mul(
            updateInvoiceDto.item_total,
            updateInvoiceDto.invoice_discount,
          ).number(),
          100,
        )
          .round()
          .number();
      }
      const taxRate = (await salesTax.getSalesTax('SA'))?.rate;
      // validate transactions
      const validated = await validateTransactions(
        updateInvoiceDto.transactions,
        this.itemsService,
        req.user?.code,
        updateInvoiceDto.store,
        taxRate,
        updateInvoiceDto.is_percentage_discount,
        updateInvoiceDto.invoice_discount,
        updateInvoiceDto.total_discounts,
        updateInvoiceDto.invoice_discount_vat,
        updateInvoiceDto.total_vat,
        updateInvoiceDto.invoice_total,
        updateInvoiceDto.free_tax,
      );
      updateInvoiceDto.transactions = validated.transactions;

      extend(saleInvoice, updateInvoiceDto);
      (saleInvoice.transactions as any) = updateInvoiceDto.transactions;
      await saleInvoice.save();

      await saleInvoice.save();
      return saleInvoice;
    } catch (err) {
      throw err;
    }
  }

  async updateBulk(updateData: UpdateInvoiceWithIdDto[]) {
    try {
      const updates: any = updateData.map((data) => {
        const { id, ...rest } = data;
        return {
          updateOne: { filter: { _id: id }, update: { ...rest } },
        };
      });
      await this.invoiceModel.bulkWrite(updates);
    } catch (error) {
      throw new RpcException(error);
    }
  }

  //TODO check this
  async updateRemaining(updateData: VoucherInvoice[], reduce: boolean = false) {
    try {
      const updates: any = updateData.map((data) => {
        const invoice = data.Invoice;
        const amount = data.amount;
        return {
          updateOne: {
            filter: { _id: invoice },
            update:
              reduce === true
                ? { $inc: { paid: -amount } }
                : { $inc: { paid: amount } },
          },
        };
      });
      return await this.invoiceModel.bulkWrite(updates);
    } catch (error) {
      throw new RpcException(error);
    }
  }

  async remove(_id: string) {
    return await this.invoiceModel.deleteOne({ _id }).catch(() => {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.invoiceDoesNotExist,
        ),
        usersExceptionCode.invoiceDoesNotExist,
      );
    });
  }

  async delete(req: RequestWithUser, _id: string) {
    const saleInvoice = await this.invoiceModel.findById(_id);
    if (!saleInvoice) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.invoiceDoesNotExist,
        ),
        usersExceptionCode.invoiceDoesNotExist,
      );
    }
    const saleReturnCount =
      await this.returnSaleService.salesRelatedInvocies(_id);
    if (saleReturnCount) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.documentCantDelete,
        ),
        usersExceptionCode.documentCantDelete,
      );
    }
    saleInvoice.branch_id = new Types.ObjectId(saleInvoice.branch_id);
    saleInvoice.store = saleInvoice.store;
    try {
      // 1.softDelete ERPDocument
      const invoice = await this.invoiceModel.findById(_id).exec();
      await invoice.softDelete();
      return saleInvoice;
    } catch (err) {
      throw err;
    }
  }

  async createInvoiceTemplate(
    req,
    invoiceId,
    lang,
    printType,
  ): Promise<string> {
    const companyInfo = await this.companyService.get();
    const tenant = await this.tenantServices.getTenantData(req.user?.code);

    const templateData: any = await this.findOne(req.user?.code, invoiceId);
    const templateType =
      printType === printingType.thermal
        ? templateTypeEnum.thermal_invoice
        : templateData.tax_no
          ? templateTypeEnum.tax_invoice
          : templateTypeEnum.simplified_tax_invoice;
    if (printType === printingType.thermal) {
      templateData.company_info = companyInfo;

      // eslint-disable-next-line @typescript-eslint/naming-convention
      const { building_no, street, secondary_no, district, postal_code, city } =
        templateData.store?.branch?.national_address;
      templateData.store.address = `${building_no} ${street} ${secondary_no} ${district} ${postal_code} ${city}`;

      templateData.company_info.logo = tenant?.logo || '';
      templateData.tax_code =
        templateData.store?.branch?.general_information?.tax_code || '';
    } else {
      const { buildingNo, street, secondaryNo, district, postalCode, city } =
        templateData.store?.branch?.national_address;
      templateData.company_info = companyInfo;
      templateData.company_info.branch_tax_code =
        templateData.store?.branch?.general_information?.tax_code || '';
      templateData.customer = templateData?.customer_id?.general_info || {};
      templateData.company_info.logo = tenant?.logo || '';
      templateData.store.branch.general_information.address = `${buildingNo} ${street} ${secondaryNo} ${district} ${postalCode} ${city}`;
      templateData.customer.tax_code =
        templateData?.customer_id?.tax_code || templateData.tax_no;
      templateData.customer.name = templateData?.customer_id?.name || {
        ar: templateData.customer_name,
        en: templateData.customer_name,
      };
      templateData.tax_rate = templateData.tax_percentage;
      templateData.customer.phone =
        templateData?.customer_id?.phone || templateData.customer_phone;
    }

    return await this.templateDesignService.renderTemplateByType(
      templateType,
      {
        data: templateData,
      },
      lang,
    );
  }

  async printOne(req: RequestWithUser, getTemplateDto: GetTemplateDto) {
    const { invoiceId, ...sendOptions } = getTemplateDto;
    const template = await this.createInvoiceTemplate(
      req,
      invoiceId,
      sendOptions.templateLang,
      sendOptions.print_type,
    );
    await this.sendTemplate(template, sendOptions);
    return { template: sendOptions.print ? template : null };
  }

  async sendTemplate(renderedTemplate: string, sendOptions: PrintOptionsDto) {
    if (sendOptions.sendEmail) {
    }
    if (sendOptions.sendSms) {
      // send template via sms
    }
  }

  async findAllRaw(ids: []): Promise<Invoice[]> {
    return await this.invoiceModel.find({ _id: { $in: ids } });
  }

  async rollbackReturnedQuantity(_id: string, transactions: any) {
    const sale = await this.invoiceModel.findOne({ _id: _id });
    sale.transactions.map((transaction) => {
      const foundTransaction = transactions.find(
        (returnedTransaction) =>
          String(returnedTransaction.item) === String(transaction.item) &&
          String(returnedTransaction.unit) === String(transaction.unit),
      );
      if (foundTransaction) {
        transaction.returned_qty = Calculator.subtract(
          transaction.returned_qty,
          foundTransaction.qty,
        )
          .round()
          .number();
        transaction.remaining_average_cost = Calculator.sum(
          transaction.remaining_average_cost,
          foundTransaction.average_cost,
        )
          .round()
          .number();
        transaction.remaining_subtotal = Calculator.sum(
          transaction.remaining_subtotal,
          foundTransaction.subtotal,
        )
          .round()
          .number();
        transaction.remaining_discount = Calculator.sum(
          transaction.remaining_discount,
          foundTransaction.discount,
        )
          .round()
          .number();
        transaction.remaining_vat = Calculator.sum(
          transaction.remaining_vat,
          foundTransaction.vat,
        )
          .round()
          .number();
        transaction.remaining_row_invoice_discount = Calculator.sum(
          transaction.remaining_row_invoice_discount,
          foundTransaction.row_invoice_discount,
        )
          .round()
          .number();
        transaction.remaining_row_invoice_discount_vat = Calculator.sum(
          transaction.remaining_row_invoice_discount_vat,
          foundTransaction.row_invoice_discount_vat,
        )
          .round()
          .number();
      }
    });

    return await this.invoiceModel.findOneAndUpdate(
      { _id },
      { $set: sale },
      { new: true, runValidators: true },
    );
  }
}
