import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { Api<PERSON><PERSON>erAuth, ApiHeader, ApiTags } from '@nestjs/swagger';
import { PaginationMetaType } from '../../utils/dto/pagination.dto';
import { RequestWithUser } from '../auth/interfaces/authorize.interface';
import { FindInvoiceDesktopDto, GetTemplateDto } from './dto';
import { CreateInvoiceDto } from './dto/create-invoice.dto';
import { FindInvoiceDto } from './dto/find-invoice.dto';
import { UpdateInvoiceDto } from './dto/update-invoice.dto';
import { SaleService } from './sale.service';
import { Invoice } from './schema/invoice.schema';
import { CaslGuard } from '../casl/guards/casl.guard';
import { BranchHeaderDto } from '../../utils/dto/request-headers.dto';
import { RequestHeaders } from '../../utils/decorators/request-header.validator';
import { Abilities } from '../casl/guards/abilities.decorator';
import { authType } from '../auth/guards/auth-type.decorator';

@ApiTags('sale')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@Controller('trade/sale')
export class SaleController {
  constructor(private readonly saleService: SaleService) {}

  @Get('/policy')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'read', subject: 'sales_invoice' })
  getPolicies() {
    return {
      allow_custom_price_if_price_zero: true,
    };
  }

  @Post()
  @UseGuards(CaslGuard)
  @Abilities({ action: 'create', subject: 'sales_invoice' })
  create(
    @Body() createInvoiceDto: CreateInvoiceDto,
    @Req() req: RequestWithUser,
  ): Promise<string> {
    return this.saleService.create(req, createInvoiceDto);
  }

  @Get()
  @authType('both')
  @ApiBearerAuth('TOKEN')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'sales_invoice' })
  findAll(
    @Query() findInvoiceDto: FindInvoiceDto,
    @RequestHeaders() headers: BranchHeaderDto,
  ): Promise<{ meta: PaginationMetaType; result: Invoice[] }> {
    const { branch } = headers;

    return this.saleService.findAll(findInvoiceDto, branch);
  }

  @Get('/desktop')
  @authType('apiKey')
  @ApiBearerAuth('TOKEN')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'sales_invoice' })
  findAllDesktop(
    @Query() findInvoiceDto: FindInvoiceDesktopDto,
    @RequestHeaders() headers: BranchHeaderDto,
  ): Promise<{ result: Invoice[] }> {
    const { branch } = headers;

    return this.saleService.findAllDesktop(findInvoiceDto, branch);
  }

  @Post('/template')
  async printOne(
    @Body() getTemplateDto: GetTemplateDto,
    @Req() req: RequestWithUser,
  ): Promise<any> {
    return await this.saleService.printOne(req, getTemplateDto);
  }

  @Get(':id')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'read', subject: 'sales_invoice' })
  findOne(
    @Param('id') id: string,
    @Req() req: RequestWithUser,
  ): Promise<Invoice> {
    return this.saleService.findOne(req.user?.code, id);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'edit', subject: 'sales_invoice' })
  @Patch(':id')
  update(
    @Req() req: RequestWithUser,
    @Param('id') id: string,
    @Body() updateInvoiceDto: UpdateInvoiceDto,
  ): Promise<Invoice> {
    return this.saleService.updateWithCoordinator(req, id, updateInvoiceDto);
  }

  @Delete(':id')
  @UseGuards(CaslGuard)
  @Abilities({ action: 'delete', subject: 'sales_invoice' })
  remove(@Param('id') id: string, @Req() req: RequestWithUser) {
    return this.saleService.delete(req, id);
  }
}
