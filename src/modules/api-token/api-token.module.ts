import { Module } from '@nestjs/common';
import { ApiTokenService } from './api-token.service';
import { MongooseModule } from '@nestjs/mongoose';
import { ApiToken, apiTokenSchema } from './schema/api-token.schema';
import { TenantModule } from '../tenants/tenant.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    TenantModule,
    UsersModule,
    MongooseModule.forFeature([
      { name: ApiToken.name, schema: apiTokenSchema },
    ]),
  ],
  providers: [ApiTokenService],
  exports: [ApiTokenService],
})
export class ApiTokenModule {}
