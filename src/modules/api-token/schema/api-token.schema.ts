import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { User } from '../../users/schemas/user.schema';
import { randomPass } from '../../../utils/random-str';
import * as argon2 from 'argon2';

export type ApiTokenDocument = ApiToken & Document;

@Schema({ timestamps: true })
export class ApiToken {
  @Prop({ required: true, ref: User.name, type: Types.ObjectId })
  user: User;

  @Prop({ required: true, type: String, select: false })
  api_token: string;

  @Prop({
    type: String,
    default: () => randomPass(20),
    select: false,
  })
  api_id: string;

  @Prop({ required: true, type: String })
  name: string;

  @Prop({ type: String })
  description: string;

  compareToken: (candidateToken: string) => Promise<boolean>;
}

export const apiTokenSchema = SchemaFactory.createForClass(ApiToken);

apiTokenSchema.pre('save', async function (next: any) {
  try {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const token = this;
    if (!token.isModified('api_token')) return next();
    token.api_token = await argon2.hash(token.api_token);

    return next();
  } catch (error) {
    error;
  }
});

apiTokenSchema.methods.compareToken = async function (candidateToken) {
  const result = await argon2.verify(this.api_token, candidateToken);
  return result;
};
