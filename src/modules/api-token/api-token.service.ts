import { Injectable, Scope } from '@nestjs/common';
import { Model, Types } from 'mongoose';
import { TenantServices } from '../tenants/tenant.service';
import { UsersService } from '../users/users.service';
import { InjectModel } from '@nestjs/mongoose';
import { ApiToken } from './schema/api-token.schema';
import { generateApiToken } from '../../utils/refresh-token';
import { CreateApiTokenDto } from '../users-profile/dto/create-api-token.dto';

@Injectable({ scope: Scope.REQUEST, durable: true })
export class ApiTokenService {
  constructor(
    private readonly tenantService: TenantServices,
    private readonly usersService: UsersService,
    @InjectModel(ApiToken.name) private readonly apiTokenModel: Model<ApiToken>,
  ) {}
  async createApiToken(
    _id: Types.ObjectId,
    createApiTokenDto: CreateApiTokenDto,
  ) {
    const userDoc = await this.usersService.findOneLimited(_id);
    const tenantUser = await this.tenantService.getTenantUser({
      user_email: userDoc.email,
    });
    const apiToken = generateApiToken(
      {
        user_id: userDoc._id,
        code: tenantUser.tenant.code,
        api_token: true,
      },
      31536000, // a year
      // '365d' this.configService.get('REFRESH_TOKEN_EXPIRE'),
    );
    const token = await this.apiTokenModel.create({
      api_token: apiToken,
      user: userDoc._id,
      ...createApiTokenDto,
    });
    token.api_token = `${apiToken}${token.api_id}`;
    return token;
  }
  async revoke(_id: Types.ObjectId, user: Types.ObjectId) {
    await this.apiTokenModel.findOneAndDelete({ _id: _id, user });
  }

  async find(user: Types.ObjectId) {
    return await this.apiTokenModel.find({ user: new Types.ObjectId(user) });
  }

  async findOne(api_id: string, user: Types.ObjectId) {
    return await this.apiTokenModel
      .findOne({ user, api_id })
      .select('api_token');
  }
}
