import { HttpException, Inject, Injectable } from '@nestjs/common';
import { extend } from 'lodash';
import {
  CreateBranchDto,
  GetBranchDto,
  GetBranchPaginationDto,
  GetBranchResponseDto,
  OnBoardBranchDto,
  UpdateBranchDto,
} from './dto/branch.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Connection, Model, Types } from 'mongoose';
import { Branch, BranchDocument, branchSchema } from './schemas/branch.schema';
import { paginate } from '../../utils/dto/pagination.dto';
import { nameOf } from '../../utils/object-key-name';
import { usersExceptionCode } from '../../exceptions/exception-code.users';
import DataBases from '../database/database.provider';
import { MongooseConfigService } from '../database/mongoose.service';
import { DeleteBranchDto } from './dto/delete-branch.dto';
import { UserBranchesRolesService } from '../user-branches-roles/user-branches-roles.service';

@Injectable()
export class BranchService {
  constructor(
    @InjectModel(Branch.name)
    private readonly branchModel: Model<Branch>,
    @Inject(MongooseConfigService)
    private readonly mongooseConfigService: MongooseConfigService,
    private readonly userBranchesRolesService: UserBranchesRolesService,
  ) {}
  async create(createBranch: CreateBranchDto): Promise<BranchDocument> {
    if (createBranch.general_information?.is_main_branch === true) {
      await this.branchModel.updateMany(
        {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          'general_information.is_main_branch': true,
        },
        // eslint-disable-next-line @typescript-eslint/naming-convention
        { $set: { 'general_information.is_main_branch': false } },
      );
    }
    return await new this.branchModel(createBranch).save();
  }

  async onBoard(onBoardBranchDto: OnBoardBranchDto): Promise<BranchDocument> {
    // eslint-disable-next-line @typescript-eslint/naming-convention,@typescript-eslint/no-unused-vars
    const { use_company_address, ...rest } = onBoardBranchDto;

    return await new this.branchModel(rest).save();
  }

  async findAll(query: GetBranchPaginationDto): Promise<GetBranchResponseDto> {
    const { page, limit, queries, ...rest } = query;
    let search = rest ?? {};

    if (queries) {
      const searchfield = { $regex: new RegExp(queries), $options: 'i' };
      search = {
        ...search,
        $or: [
          { code: searchfield },
          { name_lang_1: searchfield },
          { name_lang_2: searchfield },
          { email: searchfield },
          { phone: searchfield },
          { address: searchfield },
        ],
      } as any;
    }
    const result = await this.branchModel
      .find(search)
      .select('_id general_information')
      .skip((page - 1) * limit)
      .limit(limit);
    const meta = await paginate(limit, page, this.branchModel, search);
    return {
      result,
      meta,
    };
  }

  async findOne(qeury: GetBranchDto): Promise<BranchDocument> {
    return await this.branchModel.findOne(qeury).exec();
  }

  async findOneBranch(id: Types.ObjectId): Promise<Branch> {
    return await this.branchModel
      .findOne({ _id: id })
      .select('_id general_information')
      .lean();
  }

  async update(query: UpdateBranchDto): Promise<Branch> {
    const { _id, ...rest } = query;
    const branchData = await this.branchModel.findOne({ _id });
    if (!branchData) {
      throw new HttpException(
        nameOf(usersExceptionCode, (x) => x.branchNotFound),
        usersExceptionCode.branchNotFound,
      );
    }
    if (query.general_information?.is_main_branch === true) {
      await this.branchModel.updateMany(
        {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          'general_information.is_main_branch': true,
        },
        // eslint-disable-next-line @typescript-eslint/naming-convention
        { $set: { 'general_information.is_main_branch': false } },
      );
    }
    rest.version = Math.floor(Math.random() * (999999 - 10000) + 10000);
    extend(branchData, rest);
    return await branchData.save();
  }

  async delete(query: DeleteBranchDto): Promise<any> {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { _id, force } = query;

    const branchData = await this.branchModel.findOneAndDelete({ _id });

    if (!branchData) {
      throw new HttpException(
        nameOf(usersExceptionCode, (x) => x.branchNotFound),
        usersExceptionCode.branchNotFound,
      );
    }

    await this.userBranchesRolesService.deleteBranchRule(
      new Types.ObjectId(_id),
    );

    return branchData;
  }

  async findOneWithConnection(
    qeury: GetBranchDto,
    code: number,
  ): Promise<BranchDocument> {
    const connection = await this.newDbConnection(code);
    const brnachModel =
      connection.models.Branch ||
      (connection.model<Branch>(Branch.name, branchSchema) as any);
    return await brnachModel.findOne(qeury).lean();
  }

  async localFindAllRaw(query: GetBranchPaginationDto): Promise<Branch[]> {
    return await this.branchModel.find(query).select('_id name');
  }

  async localFindAllRawWithConnection(
    query: GetBranchPaginationDto,
    connection: Connection,
  ): Promise<Branch[]> {
    const brnachModel =
      connection.models.Branch ||
      (connection.model<Branch>(Branch.name, branchSchema) as any);
    return await brnachModel.find(query).select('_id general_information.name');
  }

  async updateMongooseMethod(query: UpdateBranchDto | any): Promise<Branch> {
    const { _id, ...rest } = query;
    const branchData = await this.branchModel.findByIdAndUpdate({ _id }, rest);
    if (!branchData) {
      throw new HttpException(
        nameOf(usersExceptionCode, (x) => x.branchNotFound),
        usersExceptionCode.branchNotFound,
      );
    }
    //extend(branchData, rest);
    return await branchData;
  }

  async findAllRaw2(query: GetBranchPaginationDto): Promise<Branch[]> {
    return await this.branchModel.find(query);
  }

  async findByIds(ids: Array<string>): Promise<Branch[]> {
    return this.branchModel.find({ _id: { $in: ids } }, [
      'general_information',
      'national_address',
    ]);
  }

  async getBranchesSetting() {
    try {
      const branchSettings = (await this.findAllRaw2({})) as Branch[];
      return branchSettings;
    } catch (e) {
      console.log(e);
    }
  }

  async updateBranch(payload) {
    try {
      const path = `document.${payload.path}`;
      const result = await this.updateMongooseMethod({
        _id: payload.branch,
        $inc: { [path]: payload.number || 1 },
      });
      return result;
    } catch (e) {
      console.log(e);
    }
  }

  private async newDbConnection(code): Promise<Connection> {
    return await DataBases.getConnection(
      this.mongooseConfigService.createDbUrl(code),
    );
  }
}
