import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Delete,
  Req,
  Query,
  Param,
  UseGuards,
} from '@nestjs/common';
import {
  UpdateBranchDto,
  CreateBranchDto,
  GetBranchPaginationDto,
  GetBranchResponseDto,
  OnBoardBranchDto,
} from './dto/branch.dto';
import { RequestWithUser } from '../auth/interfaces/authorize.interface';
import { Branch } from './schemas/branch.schema';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { BffBranchService } from './bff-branch.service';
import { Types } from 'mongoose';
import { DeleteBranchDto } from './dto/delete-branch.dto';
import { CaslGuard } from '../casl/guards/casl.guard';
import { Abilities } from '../casl/guards/abilities.decorator';
import { authType } from '../auth/guards/auth-type.decorator';

@ApiTags('branch')
@Controller('users/branch')
export class BffBranchController {
  constructor(private readonly bffBranchService: BffBranchService) {}

  @UseGuards(CaslGuard)
  @Abilities({ action: 'create', subject: 'branches_management' })
  @Post()
  create(
    @Body() createBranchDto: CreateBranchDto,
    @Req() req: RequestWithUser,
  ) {
    return this.bffBranchService.create(createBranchDto, req);
  }

  @Post('/on-board')
  onBoarding(
    @Body() onboardingBranchDto: OnBoardBranchDto,
    @Req() req: RequestWithUser,
  ) {
    return this.bffBranchService.onBoard(onboardingBranchDto, req);
  }
  @Get()
  @authType('both')
  @ApiBearerAuth('TOKEN')
  findAll(
    @Query() query: GetBranchPaginationDto,
  ): Promise<GetBranchResponseDto> {
    return this.bffBranchService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.bffBranchService.findOne({ _id: new Types.ObjectId(id) });
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'edit', subject: 'branches_management' })
  @Patch()
  update(@Body() body: UpdateBranchDto) {
    return this.bffBranchService.update(body);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'delete', subject: 'branches_management' })
  @Delete()
  async delete(@Query() query: DeleteBranchDto): Promise<Branch> {
    return this.bffBranchService.delete(query);
  }
}
