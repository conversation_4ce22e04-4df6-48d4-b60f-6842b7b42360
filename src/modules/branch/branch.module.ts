import { Modu<PERSON> } from '@nestjs/common';
import { BranchService } from './branch.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Branch, branchSchema } from './schemas/branch.schema';
import { DatabaseModule } from '../database/database.module';
import { BffBranchController } from './bff-branch.controller';
import { BffBranchService } from './bff-branch.service';
import { UserBranchesRolesModule } from '../user-branches-roles/user-branch-role.module';
import { StoreModule } from '../store/store.module';
@Module({
  imports: [
    DatabaseModule,
    MongooseModule.forFeature([{ name: Branch.name, schema: branchSchema }]),
    UserBranchesRolesModule,
    StoreModule,
  ],

  controllers: [BffBranchController],
  providers: [BranchService, BffBranchService],
  exports: [BranchService, BffBranchService],
})
export class BranchModule {}
