import { IsArray, IsNotEmpty } from 'class-validator';
import { CodeValidatorDto } from '../../../utils/dto/code.dto';
import { Types } from 'mongoose';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';
import { Transform } from 'class-transformer';

export class GetBranchesRpcDto extends CodeValidatorDto {
  @IsArray()
  @IsNotEmpty()
  ids: string[];
}

export class GetBranchRpcDto extends CodeValidatorDto {
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  @IsNotEmpty()
  id: Types.ObjectId;
}
