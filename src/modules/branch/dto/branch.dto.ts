import {
  IsNotEmpty,
  IsO<PERSON>al,
  IsString,
  IsEmail,
  IsPhoneNumber,
  ValidateNested,
  IsInt,
  IsObject,
  MinLength,
  ValidateIf,
  IsBoolean,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { Types } from 'mongoose';
import { PaginationMetaType } from 'src/utils/dto/pagination.dto';
import { Branch } from '../schemas/branch.schema';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';

import {
  BranchSalesAccountsDto,
  BranchPurchaseAccountsDto,
  BranchCashBankAccountsDto,
  BranchSubAccountsDto,
  BranchOtherAccountsDto,
  BranchTaxAccountsDto,
} from './global-account.dto';
import { GeneralSettingsDto } from './general-settings.dto';
import { BranchDocumentSettings } from '../schemas/doc-settings.schema';
import { BranchDatesSettings } from '../schemas/branch-dates-settings';
import { BranchNationalAddress } from '../schemas/branch-national-address.schema';
import { BranchGeneralInfo } from '../schemas/branch-general-info.schema';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';
import { OverridePolicyDto } from './override-policy';

class ContactPersonDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  public name?: string;

  @ApiProperty()
  @ValidateIf((o) => o.phone && o.phone.length > 0)
  @IsOptional()
  @IsPhoneNumber()
  public phone?: string;

  @ApiProperty()
  @ValidateIf((o) => o.mobile && o.mobile.length > 0)
  @IsOptional()
  @IsPhoneNumber()
  public mobile?: string;

  @ApiProperty()
  @Transform(({ value }: { value: string }) => value.toLowerCase())
  @ValidateIf((o) => o.email && o.email.length > 0)
  @IsOptional()
  @IsEmail()
  public email?: string;
}

export class BranchGlobalAccountsSettingsDto {
  @ValidateNested()
  @Type(() => BranchSalesAccountsDto)
  sales_accounts: BranchSalesAccountsDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => BranchPurchaseAccountsDto)
  purchase_accounts: BranchPurchaseAccountsDto;

  @ValidateNested()
  @Type(() => BranchCashBankAccountsDto)
  cash_and_bank: BranchCashBankAccountsDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => BranchSubAccountsDto)
  sub_accounts: BranchSubAccountsDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => BranchTaxAccountsDto)
  tax_accounts: BranchTaxAccountsDto;

  @ValidateNested()
  @Type(() => BranchOtherAccountsDto)
  other_accounts: BranchOtherAccountsDto;
}

class BranchDocumentSettingsDto {
  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  sales_invoices?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  purchases_invoices?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  sales_return?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  purchase_return?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  transfer_order?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  quotation?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  reservation?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  preparation?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  journal?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  payment_voucher_cash?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  receipt_voucher?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  debit_memo?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  credit_memo?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  service_invoice_customer?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  service_invoice_vendors?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  purchase_order?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  store_transactions_and_adjustments?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  import_order?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  prepare_purchase_entry?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  transfer_receive?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  quotation_order_and_comp?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  physical_inventory_voucher?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  physical_inventory_posting?: BranchDocumentSettings;
  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  sales_receipts_consignment?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  purchase_receipts_consignment?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  issue_permission?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  addition_permission?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  work_orders?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  entry_permission?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  release_permission?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  work_order_production?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  transfer_preparing_order?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  production_order?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  quotation_request_p?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  cargo_manifest?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  cargo_manifest_invoice?: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  sales_order?: BranchDocumentSettings;
}

class BranchSettingsDto {
  @ApiProperty({ type: GeneralSettingsDto })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => GeneralSettingsDto)
  general?: GeneralSettingsDto;

  @ApiProperty({ type: BranchGlobalAccountsSettingsDto })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchGlobalAccountsSettingsDto)
  global_accounts?: BranchGlobalAccountsSettingsDto;

  @ApiProperty({ type: OverridePolicyDto })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => OverridePolicyDto)
  override_policy?: OverridePolicyDto;
}

export class CreateBranchDto {
  @ApiProperty({ type: BranchGeneralInfo })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchGeneralInfo)
  general_information: BranchGeneralInfo;

  @ApiProperty({ type: ContactPersonDto })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => ContactPersonDto)
  contact_person?: ContactPersonDto;

  @ApiProperty({ type: BranchNationalAddress })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchNationalAddress)
  national_address?: BranchNationalAddress;
}

export class OnBoardBranchDto {
  @ApiProperty({ type: BranchGeneralInfo })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchGeneralInfo)
  general_information: BranchGeneralInfo;

  @ApiProperty({ type: ContactPersonDto })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => ContactPersonDto)
  contact_person?: ContactPersonDto;

  @ApiProperty({ type: BranchNationalAddress })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchNationalAddress)
  national_address?: BranchNationalAddress;

  @IsBoolean()
  use_company_address: boolean = false;
}

export class UpdateBranchDto {
  @ApiProperty()
  @IsString()
  @MinLength(4)
  _id: string;

  @ApiProperty({ type: BranchGeneralInfo })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchGeneralInfo)
  general_information: BranchGeneralInfo;

  @ApiProperty({ type: ContactPersonDto })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => ContactPersonDto)
  contact_person?: ContactPersonDto;

  @ApiProperty({ type: BranchNationalAddress })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchNationalAddress)
  national_address?: BranchNationalAddress;

  @ApiProperty({ type: BranchSettingsDto })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchSettingsDto)
  settings?: BranchSettingsDto;

  @ApiProperty({ type: BranchDatesSettings })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDatesSettings)
  dates?: BranchDatesSettings;

  @ApiProperty({ type: BranchDocumentSettingsDto })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BranchDocumentSettingsDto)
  document?: BranchDocumentSettingsDto;
  @ApiHideProperty()
  version: number;
}

export class GetBranchDto {
  @ApiProperty({ type: 'string' })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  public _id?: Types.ObjectId;
}

export class GetOneBranchDto {
  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  public _id?: Types.ObjectId;
}

export class GetBranchPaginationDto extends GetBranchDto {
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  public page?: number;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  public limit?: number;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  queries?: string;
}

export class GetBranchResponseDto {
  @ApiProperty()
  result: Branch[];
  @ApiProperty()
  meta: PaginationMetaType;
}
