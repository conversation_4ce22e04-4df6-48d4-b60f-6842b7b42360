import {
  IsOptional,
  IsBoolean,
  IsEnum,
  IsNumber,
  ValidateIf,
  Min,
  IsObject,
  ValidateNested,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { DocumentEditionsEnum } from '../interfaces/enum';

class DocumentEditionSettingsDto {
  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  enabled: boolean;

  @ApiProperty({
    enum: DocumentEditionsEnum,
    examples: DocumentEditionsEnum,
  })
  @ValidateIf((o) => o.enabled === true)
  @IsEnum(DocumentEditionsEnum)
  type: DocumentEditionsEnum;

  @ApiProperty()
  @ValidateIf((o) => o.type === DocumentEditionsEnum.DeadlineInDays)
  @IsNumber()
  @Min(1)
  parameter: number;
}

export class GeneralSettingsDto {
  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  allow_documents_deletion: boolean;

  @ApiProperty()
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => DocumentEditionSettingsDto)
  allow_documents_edition: DocumentEditionSettingsDto;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  use_multi_stores: boolean;
}
