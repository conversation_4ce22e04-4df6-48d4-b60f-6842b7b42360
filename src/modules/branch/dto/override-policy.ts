import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { allowCustomPrice } from '../../users/schemas/enums';

export class OverridePolicyDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  override_profit_margin: boolean;

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  allow_price_under_cost: boolean;

  @ApiProperty()
  @IsOptional()
  @IsEnum(allowCustomPrice)
  allow_custom_price: allowCustomPrice;
}
