import {
  IsBoolean,
  IsMongoId,
  IsEmail,
  IsNotEmpty,
  IsObject,
  IsPhoneNumber,
  IsString,
  ValidateIf,
  ValidateNested,
  IsNumber,
  Length,
  Validate,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ObjectId } from 'mongoose';
import { Transform, Type } from 'class-transformer';
import { IsOptional } from '@nestjs/class-validator';
import {
  NameDto,
  validateLanguage,
  validateEnLanguage,
} from '../../../utils/dto/name.dto';
import { toLowerCaseObjectKeys } from '../../../utils/tolowerCaseObjectKeys';

class StoreSetting {
  @IsBoolean()
  @Transform(({ value }) => value === true)
  @ApiProperty({
    description: 'multi unit sotre setting of inventory  ',
    example: false,
    required: false,
    default: false,
  })
  multi_unit: boolean;

  @ApiProperty({
    description: 'barcode store setting of inventory  ',
    example: true,
    required: false,
    default: true,
  })
  @IsBoolean()
  @Transform(({ value }) => value === true)
  barcode: boolean;

  @ApiProperty({
    description: 'rounting setting of inventory  ',
    example: 0,
    required: false,
    default: 0,
  })
  @IsNumber()
  rounding: number;
}

class ContactPersonDto {
  @ApiProperty({
    default: 'ruzi',
  })
  @IsString()
  @IsOptional()
  public name?: string;

  @ApiProperty({
    default: '+989370416208',
  })
  @ValidateIf((o) => o.mobile && o.mobile.length > 0)
  @IsOptional()
  @IsPhoneNumber()
  public mobile?: string;

  @ApiProperty()
  @ValidateIf((o) => o.email && o.email.length > 0)
  @IsOptional()
  @IsEmail()
  @ApiProperty({
    default: '<EMAIL>',
  })
  public email?: string;
}

export class NationalAddress {
  @ApiProperty({
    description: 'short address of NationalAddress',
    example: 'short address',
    required: false,
  })
  @IsString()
  @IsOptional()
  short_address: string;

  @ApiProperty({
    description: 'street of NationalAddress',
    example: 'abraham street',
    required: false,
  })
  @IsString()
  @IsOptional()
  street: string;

  @ApiProperty({
    description: 'district of NationalAddress',
    example: 'district',
    required: false,
  })
  @IsString()
  @IsOptional()
  district: string;

  @ApiProperty({
    description: 'city of NationalAddress',
    example: 'london',
    required: false,
  })
  @IsString()
  @IsOptional()
  city: string;

  @ApiProperty({
    description: 'building number of NationalAddress',
    example: '0123',
    required: false,
  })
  @IsString()
  @IsOptional()
  building_no: string;

  @ApiProperty({
    description: 'secondary_no of NationalAddress',
    example: '123234',
    required: false,
  })
  @IsString()
  @IsOptional()
  secondary_no: string;

  @ApiProperty({
    description: 'secondary_no of NationalAddress',
    example: '11111',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Length(5, 25)
  postal_code: string;

  @ApiProperty({
    description: 'governorate of NationalAddress',
    example: 'governorate',
    required: false,
  })
  @IsString()
  @IsOptional()
  governorate: string;
}
export class CreateStoreDto {
  @ApiProperty({ type: NameDto })
  @Transform((param) => toLowerCaseObjectKeys(param.value))
  @Validate(validateLanguage)
  @Validate(validateEnLanguage)
  name: NameDto;

  @ApiProperty({
    description: 'is default store',
    example: false,
    required: false,
  })
  @Transform(({ value }) => value === true)
  @IsBoolean()
  @IsOptional()
  is_default: boolean;

  @ApiProperty({
    description: 'branch of inventory item ',
    example: '507f1f77bcf86cd799439011',
    required: true,
  })
  @IsMongoId()
  branch: ObjectId;

  @ApiProperty({
    description: 'phone of store',
    example: '+989370416208',
    required: false,
  })
  @IsOptional()
  @IsNotEmpty()
  phone: string;

  @ApiProperty({
    description: 'fax of store',
    example: '+989370416208',
    required: false,
  })
  @IsPhoneNumber()
  @IsOptional()
  fax?: string;

  @ApiProperty({
    type: ContactPersonDto,
  })
  @ValidateNested()
  @IsObject()
  @IsOptional()
  @Type(() => ContactPersonDto)
  contact_person: ContactPersonDto;

  @ApiProperty({
    required: true,
    type: StoreSetting,
  })
  @ValidateNested()
  @IsObject()
  @Type(() => StoreSetting)
  @IsOptional()
  setting?: StoreSetting;

  @ValidateNested()
  @IsObject()
  @IsOptional()
  @Type(() => NationalAddress)
  national_address: NationalAddress;
}
