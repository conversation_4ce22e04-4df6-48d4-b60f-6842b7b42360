import { <PERSON>p, Schema } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber } from 'class-validator';

@Schema({ _id: false })
export class BranchDocumentSettings {
  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  @IsBoolean()
  auto_serial: boolean = false;

  @ApiProperty()
  @Prop({ type: Boolean, default: true })
  @IsBoolean()
  same_serial: boolean = true;

  @ApiProperty()
  @Prop({ type: Number, default: 0 })
  @IsNumber()
  cash_serial: number = 0;

  @ApiProperty()
  @Prop({ type: Number, default: 0 })
  @IsNumber()
  credit_serial: number = 0;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  @IsBoolean()
  default_unit_as_biggest: boolean;
}
