import { Prop, Schema } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsNumber,
  Validate,
  IsEmail,
  IsNumberString,
  Length,
} from 'class-validator';
import { toLowerCaseObjectKeys } from '../../../utils/tolowerCaseObjectKeys';
import {
  validateEnLanguage,
  validateLanguage,
} from '../../../utils/dto/name.dto';
import { Name } from '../../../utils/schemas';

@Schema({ _id: false })
export class BranchGeneralInfo {
  @ApiProperty()
  @IsString()
  @Prop({ required: true })
  code: string;

  @ApiProperty({
    example: {
      en: 'ruzi',
      ar: 'روزبه',
    },
  })
  @Transform((param) => toLowerCaseObjectKeys(param.value))
  @Validate(validateLanguage)
  @Validate(validateEnLanguage)
  @Prop({ required: true })
  name: Name;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  @IsOptional()
  parent_branch: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsOptional()
  @IsNumberString({ no_symbols: true })
  @Length(15, 15)
  tax_code: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Prop({ type: Number, required: true, default: 0 })
  registration_number: number;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  @IsOptional()
  phone: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsOptional()
  fax: string;

  @ApiProperty()
  @Prop({ type: String, default: '', lowercase: true, trim: true })
  @IsEmail()
  @Transform(({ value }: { value: string }) => value.toLowerCase())
  @IsOptional()
  email: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsOptional()
  address: string;

  @IsOptional()
  @ApiProperty()
  @Prop({ type: Boolean, default: true })
  @IsNotEmpty()
  activation_status: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  @IsBoolean()
  is_main_branch: boolean;
}
