import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import * as paginate from 'mongoose-paginate-v2';
import { BranchDatesSettings } from './branch-dates-settings';
import { BranchGeneralInfo } from './branch-general-info.schema';
import { BranchNationalAddress } from './branch-national-address.schema';
import { BranchSettings } from './branch-settings.schema';
import { ContactPerson } from './contant-person.schema';
import { BranchSpecificDocumentsSettings } from './specific-doc-settings.schema';
import { HydratedDocument } from 'mongoose';

export type BranchDocument = HydratedDocument<Branch>;

@Schema({ timestamps: true })
export class Branch {
  @ApiProperty({ type: BranchGeneralInfo })
  @Prop({ type: BranchGeneralInfo, default: () => ({}) })
  general_information: BranchGeneralInfo;

  @ApiProperty({ type: <PERSON><PERSON><PERSON> })
  @Prop({ type: Contact<PERSON>erson, default: () => ({}) })
  contact_person: ContactPerson;

  @ApiProperty({ type: BranchNationalAddress })
  @Prop({ type: BranchNationalAddress, default: () => ({}) })
  national_address: BranchNationalAddress;

  @ApiProperty({ type: BranchSettings })
  @Prop({ type: BranchSettings, default: () => ({}) })
  settings: BranchSettings;

  @ApiProperty({ type: BranchDatesSettings })
  @Prop({ type: BranchDatesSettings, default: () => ({}) })
  dates: BranchDatesSettings;

  @ApiProperty({ type: BranchSpecificDocumentsSettings })
  @Prop({ type: BranchSpecificDocumentsSettings, default: () => ({}) })
  document: BranchSpecificDocumentsSettings;
  @Prop({
    type: Number,
    default: () => {
      return Math.floor(Math.random() * (999999 - 10000) + 10000);
    },
    unique: true,
  })
  version: number;
}

export const branchSchema = SchemaFactory.createForClass(Branch);
branchSchema.plugin(paginate);
