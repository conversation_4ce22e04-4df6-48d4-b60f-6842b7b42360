import { Prop, Schema } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsString, Validate } from 'class-validator';
import { toLowerCaseObjectKeys } from '../../../utils/tolowerCaseObjectKeys';
import {
  validateEnLanguage,
  validateLanguage,
} from '../../../utils/dto/name.dto';
import { Name } from '../../../utils/schemas';

@Schema({ _id: false })
export class BranchNationalAddress {
  @ApiProperty({
    example: {
      en: 'ruzi',
      ar: 'روزبه',
    },
  })
  @Transform((param) => toLowerCaseObjectKeys(param.value))
  @Validate(validateLanguage)
  @Validate(validateEnLanguage)
  @Prop({ required: true })
  trade_name: Name;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  commercial_activities: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  short_address: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  building_no: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  street: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  secondary_no: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  district: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  postal_code: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  city: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  governorate: string;
}
