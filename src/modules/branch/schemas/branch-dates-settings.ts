import { <PERSON>p, Schema } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, ValidateIf } from 'class-validator';

@Schema({ _id: false })
export class BranchDatesSettings {
  @ApiProperty()
  @Prop({ type: String, default: '' })
  @ValidateIf((o) => o.year_beginning_date && o.year_beginning_date.length > 0)
  @IsDateString()
  year_beginning_date: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @ValidateIf((o) => o.year_ending_date && o.year_ending_date.length > 0)
  @IsDateString()
  year_ending_date: string;
}
