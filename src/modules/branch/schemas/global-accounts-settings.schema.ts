import { Prop, Schema } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

@Schema({ _id: false })
export class BranchSalesAccounts {
  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  cash_sales_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  credit_sales_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  cash_sales_return_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  credit_sales_return_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  sales_discount_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  customers_deposits_account: string;
}

@Schema({ _id: false })
export class BranchPurchaseAccounts {
  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  cash_purchase_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  credit_purchase_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  cash_purchase_return_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  credit_purchase_return_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  purchase_discount_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  deferred_discount_account: string;
}

@Schema({ _id: false })
export class BranchCashBankAccounts {
  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  cash_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  bank_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  additional_expense_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  cash_invoices_under_settlement: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  profit_account: string;
}

@Schema({ _id: false })
export class BranchSubAccounts {
  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  cash_sales_commission_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  credit_sales_commission_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  levy_commission_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  customer_group_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  vendor_group_account: string;
}

@Schema({ _id: false })
export class BranchOtherAccounts {
  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  transfer_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  adjustment_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  inventory_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  beginning_balance_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  ending_balance_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  cost_of_sales_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  cost_of_purchase_account: string;
}

@Schema({ _id: false })
export class BranchTaxAccounts {
  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  purchase_tax_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  sales_tax_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  payment_commission_tax_account: string;
}

@Schema({ _id: false })
export class BranchGlobalAccountsSettings {
  @ApiProperty({ type: BranchSalesAccounts })
  @Prop({ type: BranchSalesAccounts, default: () => ({}) })
  sales_accounts: BranchSalesAccounts = new BranchSalesAccounts();

  @ApiProperty({ type: BranchPurchaseAccounts })
  @Prop({ type: BranchPurchaseAccounts, default: () => ({}) })
  purchase_accounts: BranchPurchaseAccounts = new BranchPurchaseAccounts();

  @ApiProperty({ type: BranchCashBankAccounts })
  @Prop({ type: BranchCashBankAccounts, default: () => ({}) })
  cash_and_bank: BranchCashBankAccounts = new BranchCashBankAccounts();

  @ApiProperty({ type: BranchSubAccounts })
  @Prop({ type: BranchSubAccounts, default: () => ({}) })
  sub_accounts: BranchSubAccounts = new BranchSubAccounts();

  @ApiProperty({ type: BranchTaxAccounts })
  @Prop({ type: BranchTaxAccounts, default: () => ({}) })
  tax_accounts: BranchTaxAccounts = new BranchTaxAccounts();

  @ApiProperty({ type: BranchOtherAccounts })
  @Prop({ type: BranchOtherAccounts, default: () => ({}) })
  other_accounts: BranchOtherAccounts = new BranchOtherAccounts();
}
