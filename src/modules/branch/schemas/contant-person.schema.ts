import { Prop, Schema } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEmail, IsString } from 'class-validator';

@Schema({ _id: false })
export class <PERSON><PERSON>erson {
  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  name: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  mobile: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  phone: string;

  @ApiProperty()
  @Prop({ type: String, default: '', lowercase: true, trim: true })
  @IsEmail()
  @Transform(({ value }: { value: string }) => value.toLowerCase())
  email: string;
}
