import { Prop, Schema } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

@Schema({ _id: false })
export class BranchDefaultAccountsType {
  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  cash_sales_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  cash_sales_return_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  sales_discount_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  credit_sales_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  credit_sales_return_account: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  @IsString()
  customers_deposits_account: string;
}
