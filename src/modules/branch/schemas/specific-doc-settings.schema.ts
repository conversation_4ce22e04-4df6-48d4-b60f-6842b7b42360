import { Prop, Schema } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsObject, ValidateNested } from 'class-validator';
import { BranchDocumentSettings } from './doc-settings.schema';

@Schema({ _id: false })
export class BranchSpecificDocumentsSettings {
  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  sales_invoices: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  purchases_invoices: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  sales_return: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  purchase_return: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  transfer_order: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  quotation: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  reservation: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  preparation: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  journal: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  payment_voucher_cash: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  receipt_voucher: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  debit_memo: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  credit_memo: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  service_invoice_customer: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  service_invoice_vendors: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  purchase_order: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  store_transactions_and_adjustments: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  import_order: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  prepare_purchase_entry: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  transfer_receive: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  quotation_order_and_comp: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  physical_inventory_voucher: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  physical_inventory_posting: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  sales_receipts_consignment: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  purchase_receipts_consignment: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  issue_permission: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  addition_permission: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  work_orders: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  entry_permission: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  release_permission: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  work_order_production: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  transfer_preparing_order: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  production_order: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  quotation_request_p: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  cargo_manifest: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  cargo_manifest_invoice: BranchDocumentSettings;

  @ApiProperty({ type: BranchDocumentSettings })
  @Prop({ type: BranchDocumentSettings, default: () => ({}) })
  @IsObject()
  @ValidateNested()
  @Type(() => BranchDocumentSettings)
  sales_order: BranchDocumentSettings;
}
