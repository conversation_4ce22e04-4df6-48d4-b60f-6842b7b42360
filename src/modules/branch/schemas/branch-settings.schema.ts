import { Prop, Schema } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { BranchGeneralSettings } from './general-settings.schema';
import { BranchGlobalAccountsSettings } from './global-accounts-settings.schema';
import { OverridePolicy } from './override-policy.schema';

@Schema()
export class BranchSettings {
  @ApiProperty({ type: BranchGeneralSettings })
  @Prop({ type: BranchGeneralSettings, default: () => ({}) })
  general: BranchGeneralSettings;

  @ApiProperty({ type: BranchGlobalAccountsSettings })
  @Prop({ type: BranchGlobalAccountsSettings, default: () => ({}) })
  global_accounts: BranchGlobalAccountsSettings;

  @Prop({ type: OverridePolicy, default: () => ({}) })
  override_policy: OverridePolicy;
}
