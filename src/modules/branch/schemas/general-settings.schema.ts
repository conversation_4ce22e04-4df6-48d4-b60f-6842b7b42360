import { Prop, Schema } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsNumber,
  IsDefined,
  IsOptional,
  IsEnum,
} from 'class-validator';
import { DocumentEditionsEnum } from '../interfaces/enum';

export class DocumentEditionSettings {
  @ApiProperty({ description: 'Enable document editing', example: true })
  @Prop({ required: true })
  @IsBoolean()
  enabled: boolean;

  @ApiProperty({
    description: 'Type of document editing',
    enum: DocumentEditionsEnum,
    example: 'allow-custom-date-document',
  })
  @IsOptional()
  @IsEnum(DocumentEditionsEnum)
  type: DocumentEditionsEnum;

  @ApiProperty({ description: 'Parameter for document editing', example: 1 })
  @IsOptional()
  @IsNumber()
  parameter: number;
}

@Schema({ _id: false })
export class BranchGeneralSettings {
  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  @IsBoolean()
  allow_documents_deletion: boolean;

  @ApiProperty()
  @Prop({ type: DocumentEditionSettings, default: () => ({ enabled: false }) })
  @IsDefined()
  allow_documents_edition: DocumentEditionSettings;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  @IsBoolean()
  use_multi_stores: boolean;
}
