import { Injectable } from '@nestjs/common';
import {
  CreateBranchDto,
  GetBranchDto,
  GetBranchPaginationDto,
  GetBranchResponseDto,
  OnBoardBranchDto,
  UpdateBranchDto,
} from './dto/branch.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Branch, BranchDocument } from './schemas/branch.schema';
import { RequestWithUser } from '../auth/interfaces/authorize.interface';
import { BranchService } from './branch.service';
import { DeleteBranchDto } from './dto/delete-branch.dto';
import { StoreService } from '../store/store.service';

@Injectable()
export class BffBranchService {
  constructor(
    @InjectModel(Branch.name)
    private readonly branchModel: Model<Branch>,
    private readonly branchService: BranchService,
    private readonly storeService: StoreService,
  ) {}
  async create(
    createBranch: CreateBranchDto,
    req: RequestWithUser,
  ): Promise<Branch> {
    //TODO: save numbering
    const branchResult = await this.branchService.create(createBranch);

    await this.createStore(branchResult, req);

    const branch = await this.branchModel
      .findOne({ _id: branchResult._id })
      .lean();
    return branch;
  }

  async onBoard(
    onBoardBranchDto: OnBoardBranchDto,
    req: RequestWithUser,
  ): Promise<Branch> {
    //TODO: save numbering

    const branchResult = await this.branchService.onBoard(onBoardBranchDto);

    await this.createStore(branchResult, req);

    const branch = await this.branchModel
      .findOne({ _id: branchResult._id })
      .lean();
    return branch;
  }

  async findAll(query: GetBranchPaginationDto): Promise<GetBranchResponseDto> {
    return await this.branchService.findAll(query);
  }

  async findOne(qeury: GetBranchDto): Promise<Branch> {
    const branch = await this.branchService.findOne(qeury);
    return branch;
  }

  async findOneWithConnection(
    qeury: GetBranchDto,
    code: number,
  ): Promise<Branch> {
    const branch = await this.branchService.findOneWithConnection(qeury, code);
    return branch;
  }

  async update(query: UpdateBranchDto): Promise<Branch> {
    await this.branchService.update(query);
    const branch = await this.branchModel.findOne({ _id: query._id }).lean();
    return branch;
  }

  async delete(query: DeleteBranchDto): Promise<any> {
    return await this.branchService.delete(query);
  }

  async createStore(rs: BranchDocument, req: RequestWithUser) {
    const storeData: any = {
      name: rs.general_information.name,
      is_default: true,
      branch: rs._id,
      phone: rs.general_information.phone,
      national_address: rs.national_address,
      contact_person: rs.contact_person,
    };
    await this.storeService.create(req, storeData);
  }
}
