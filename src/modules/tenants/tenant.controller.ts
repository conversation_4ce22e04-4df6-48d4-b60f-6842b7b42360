import {
  Body,
  Controller,
  Get,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { TenantServices } from './tenant.service';
import { RegisterTenantRequestDto } from './dtos/register-tenant.dto';
import { CaslGuard } from '../casl/guards/casl.guard';
import { ApiTags } from '@nestjs/swagger';
import {
  CreateTenantRequestDto,
  GetTenantRequestDto,
  UpdateTenantRequestDto,
} from './dtos';

@ApiTags('Tenant')
@UseGuards(CaslGuard)
@Controller('tenant/admin-tenant')
export class TenantController {
  constructor(
    private readonly tenantServices: TenantServices, // @Inject(REQUEST) private request: Request,
  ) {}

  @Post()
  async create(@Body() body: CreateTenantRequestDto) {
    return await this.tenantServices.create(body);
  }

  @Get()
  async get(@Query() query: GetTenantRequestDto) {
    return await this.tenantServices.get(query);
  }

  @Patch()
  async update(@Body() body: UpdateTenantRequestDto) {
    return await this.tenantServices.update(body);
  }

  @Post('/register')
  async register(@Body() body: RegisterTenantRequestDto) {
    console.log(body);

    return await this.tenantServices.register(body);
  }
}
