import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { ObjectId } from 'mongoose';
import { Tenant } from './tenant.schema';
import * as paginate from 'mongoose-paginate-v2';

@Schema({ collection: 'tenant_users', timestamps: true })
export class TenantUser {
  _id: ObjectId;
  @Prop({
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
  })
  user_email: string;

  @Prop({
    required: true,
    type: mongoose.Schema.Types.ObjectId,
    ref: Tenant.name,
  })
  tenant: Tenant;
}

export const tenantUserSchema = SchemaFactory.createForClass(TenantUser);
tenantUserSchema.plugin(paginate);
