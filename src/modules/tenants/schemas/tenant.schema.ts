import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Document } from 'mongoose';
import * as paginate from 'mongoose-paginate-v2';
import { NationalAddress } from './national-address.schema';
export type TenantDocument = Tenant & Document;

@Schema({ _id: false })
export class User {
  @Prop({
    required: true,
  })
  name: string;

  @Prop({
    required: true,
    lowercase: true,
    trim: true,
  })
  email: string;
}
@Schema({ timestamps: true })
export class Tenant {
  @Prop({ required: true })
  @ApiProperty()
  name: string;

  @Prop()
  @ApiProperty()
  short_name: string;

  @Prop({
    required: true,
    type: Boolean,
  })
  @ApiProperty()
  activation_status: boolean;

  @Prop()
  @ApiProperty()
  country: string;

  @Prop({
    required: true,
  })
  @ApiProperty()
  registration_no: string;

  @Prop({
    required: true,
  })
  @ApiProperty()
  tax_code: string;

  @Prop({ type: String })
  logo: string;

  @Prop()
  @ApiProperty()
  notes: string;

  @ApiProperty()
  @Prop({ unique: true, required: true })
  code: number;

  @Prop({ select: false })
  db_url: string;

  @Prop({ select: false })
  db_user: string;

  @Prop({ select: false })
  db_password: string;

  @ApiProperty({ type: NationalAddress })
  @Prop({ type: NationalAddress, default: () => ({}) })
  national_address: NationalAddress;

  @Prop({ required: true })
  user: User;
}

export const tenantSchema = SchemaFactory.createForClass(Tenant);
tenantSchema.plugin(paginate);
