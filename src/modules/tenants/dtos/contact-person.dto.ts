import {
  IsNotEmpty,
  Is<PERSON><PERSON>al,
  IsString,
  IsEmail,
  IsPhoneNumber,
} from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class TenantContactPersonDto {
  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: 'name of the contacet persoon',
    example: 'ruzi',
    required: true,
  })
  public name: string;

  @IsOptional()
  @IsPhoneNumber()
  @ApiProperty({
    description: 'phone of the contacet persoon',
    example: '+989370451254',
    required: false,
  })
  public phone: string;

  @IsPhoneNumber()
  @IsOptional()
  @ApiProperty({
    description: 'mobile of the contacet persoon',
    example: '+989370415425',
    required: false,
  })
  public mobile: string;

  @IsEmail()
  @Transform(({ value }: { value: string }) => value.toLowerCase())
  @IsOptional()
  @ApiProperty({
    description: 'email of the contacet persoon',
    example: '<EMAIL>',
    required: false,
  })
  public email: string;
}
