import { IsNotEmpty } from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { CreateTenantUserRequestDto } from './create-tenant-user.dto';
import { Transform } from 'class-transformer';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';
export class ConfirmTenantRequestDto extends CreateTenantUserRequestDto {
  @ApiProperty({
    description: '_id of the tenant',
    example: 'das4d64df984er8t46d1gdfgr4t64',
    required: true,
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  public _id: string;
}
