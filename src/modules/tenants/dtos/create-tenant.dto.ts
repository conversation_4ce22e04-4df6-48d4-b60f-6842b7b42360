import {
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
  IsBoolean,
} from '@nestjs/class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { CreateTenantUserRequestDto } from './create-tenant-user.dto';
import { BranchNationalAddress } from '../../branch/schemas/branch-national-address.schema';

export class CreateTenantRequestDto {
  @ApiProperty({
    example: 'ruzi',
  })
  @IsString()
  name: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'name of tenant',
    example: 'ruzi',
    required: false,
  })
  short_name: string;

  @IsBoolean()
  @IsNotEmpty()
  @ApiProperty({
    description: 'confirmation of  tenant',
    example: true,
    required: true,
  })
  activation_status: boolean;

  @IsNotEmpty()
  @ApiProperty({
    description: 'address of tenant',
    required: true,
  })
  @ValidateNested()
  @Type(() => BranchNationalAddress)
  national_address: BranchNationalAddress;

  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'name of tenant',
    example: 'US',
    required: false,
  })
  country: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'registration Number of tenant',
    example: '1',
    required: true,
  })
  registration_no: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'tax code of tenant',
    example: '001',
    required: true,
  })
  tax_code: string;

  @ApiProperty()
  @IsOptional()
  logo?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'notes of  tenant',
    example: 'some notes',
    required: false,
  })
  notes: string;

  @ValidateNested()
  @Type(() => CreateTenantUserRequestDto)
  user!: CreateTenantUserRequestDto;
}
