import {
  IsOptional,
  IsString,
  IsEmail,
  ValidateIf,
} from '@nestjs/class-validator';
import { IdValidatorDto } from '../../../utils/dto/general.dto';
import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsBoolean, ValidateNested } from 'class-validator';
import { BranchNationalAddress } from '../../branch/schemas/branch-national-address.schema';
import { Transform, Type } from 'class-transformer';

export class UpdateBranchNationalAddress extends PartialType(
  BranchNationalAddress,
) {}
export class UpdateTenantRequestDto extends IdValidatorDto {
  @ApiProperty({
    description: 'put name in one language',
    example: 'ruzi',
  })
  @IsOptional()
  @ValidateIf((val) => val !== undefined)
  @IsString()
  name: string;

  @ApiProperty({
    description: 'address of tenant',
    required: true,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateBranchNationalAddress)
  national_address?: UpdateBranchNationalAddress;

  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'notes of tenant',
    example: 'ruzi',
    required: false,
  })
  notes?: string;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({
    description: 'confirmation of tenant',
    example: false,
    required: false,
  })
  activation_status: boolean;

  @ApiProperty()
  @IsOptional()
  logo?: string;
}

export class UpdateUserTenantRequestDto {
  @IsEmail()
  @IsOptional()
  @Transform(({ value }: { value: string }) => value.toLowerCase())
  @ApiProperty({
    description: 'email of  User tenant',
    example: '<EMAIL>',
    required: false,
  })
  user_email?: string;

  @IsString()
  @IsOptional()
  public _id?: string;
}
