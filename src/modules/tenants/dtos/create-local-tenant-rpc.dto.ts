import { IsNotEmpty, IsString } from 'class-validator';
import { CodeValidatorDto } from '../../../utils/dto/code.dto';
import { Type } from 'class-transformer';

class CreateLocalTenantData {
  @IsNotEmpty()
  tenantData;
  @IsNotEmpty()
  tenantDataSaved;
  @IsString()
  code: string;
}
export class CreateLocalTenantRpc extends CodeValidatorDto {
  @Type(() => CreateLocalTenantData)
  @IsNotEmpty()
  data: CreateLocalTenantData;
}
