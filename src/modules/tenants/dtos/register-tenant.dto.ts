import {
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsBoolean, IsNumber, IsNumberString, Length } from 'class-validator';
import { BranchNationalAddress } from '../../branch/schemas/branch-national-address.schema';
export class RegisterTenantRequestDto {
  @ApiProperty({
    example: 'ruzi',
  })
  @IsString()
  name: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'name of tenant',
    example: 'ruzi',
    required: false,
  })
  short_name: string;

  @IsBoolean()
  @IsNotEmpty()
  @ApiProperty({
    description: 'confirmation of  tenant',
    example: false,
    required: true,
  })
  activation_status: boolean;

  @IsNotEmpty()
  @ApiProperty({
    description: 'address of tenant',
    required: true,
  })
  @ValidateNested()
  @Type(() => BranchNationalAddress)
  national_address: BranchNationalAddress;

  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'name of tenant',
    example: 'US',
    required: false,
  })
  country: string;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty({
    description: 'registration Number of tenant',
    example: 1,
    required: true,
  })
  registration_no: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'tax code of tenant',
    example: '001',
    required: true,
  })
  @IsNumberString({ no_symbols: true })
  @Length(15, 15)
  tax_code: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'notes of  tenant',
    example: 'some notes',
    required: false,
  })
  notes: string;
}
