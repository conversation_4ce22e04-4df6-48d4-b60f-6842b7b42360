import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
} from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Tenant } from '../schemas/tenant.schema';
import { Transform } from 'class-transformer';
import { Types } from 'mongoose';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';
export class GetTenantUserRequestDto {
  @IsOptional()
  @ApiProperty({
    description: '_id  of  tenant user',
    example: 'sadsad7sdhf8sdhf8sd7fh',
    required: false,
  })
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  _id?: Types.ObjectId;

  @IsNotEmpty()
  @IsOptional()
  @IsEmail()
  @Transform(({ value }: { value: string }) => value.toLowerCase())
  @ApiProperty({
    description: 'notes of  tenantUser',
    example: '<EMAIL>',
    required: false,
  })
  user_email?: string;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @ApiProperty({
    description: 'id of  tenant',
    example: 'ruzi.com',
    required: false,
  })
  tenant?: string;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @ApiProperty({
    description: 'serach in   tenant user',
    example: 'ruzi',
    required: false,
  })
  queries?: string;
}
export class GetTenantUserResponseDto {
  result: Tenant[];
  meta: any;
}
