import { IsNotEmpty, IsString, IsEmail } from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class CreateTenantUserRequestDto {
  @ApiProperty({
    example: 'ruzi or روزبه',
  })
  @IsString()
  public name: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: 'password of user tenant',
    example: 'a125449846',
    required: true,
  })
  public password: string;

  @IsNotEmpty()
  @IsEmail()
  @Transform(({ value }: { value: string }) => value.toLowerCase())
  @ApiProperty({
    description: 'email of  user tenant',
    example: '<EMAIL>',
    required: true,
  })
  public email: string;
}
