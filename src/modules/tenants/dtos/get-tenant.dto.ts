import {
  IsNotEmpty,
  IsO<PERSON>al,
  IsString,
  IsEmail,
  IsPhoneNumber,
  IsUrl,
  IsBoolean,
} from '@nestjs/class-validator';
import { PaginationDto } from '../../../utils/dto/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { Types } from 'mongoose';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';
export class GetTenantRequestDto extends PaginationDto {
  @ApiProperty({
    description: '_id of  tenant',
    example: 'dfg745idsf723hrskdfh39',
    required: false,
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  _id?: Types.ObjectId;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @ApiProperty({
    description: 'name of  tenant',
    example: 'ruzi',
    required: false,
  })
  name?: string;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @ApiProperty({
    description: 'search  of  tenant',
    example: 'ruzi',
    required: false,
  })
  queries?: string;

  @IsPhoneNumber()
  @IsOptional()
  @ApiProperty({
    description: 'phone of  tenant',
    example: '+989370416207',
    required: false,
  })
  phone?: string;

  @IsEmail()
  @Transform(({ value }: { value: string }) => value.toLowerCase())
  @IsOptional()
  @ApiProperty({
    description: 'email of  tenant',
    example: '<EMAIL>',
    required: false,
  })
  email?: string;

  @IsString()
  @IsOptional()
  @IsOptional()
  @ApiProperty({
    description: 'address of  tenant',
    example: 'tehran',
    required: false,
  })
  address?: string;

  @IsUrl()
  @IsOptional()
  @IsOptional()
  @ApiProperty({
    description: 'website of  tenant',
    example: 'ruzi.com',
    required: false,
  })
  website?: string;

  @IsString()
  @IsOptional()
  @IsOptional()
  @ApiProperty({
    description: 'notes of  tenant',
    example: 'ruzis',
    required: false,
  })
  notes?: string;

  @IsBoolean()
  @Transform(({ value }) => value === true)
  @IsOptional()
  @IsOptional()
  @ApiProperty({
    description: 'status of tenant',
    example: 'true',
    required: false,
  })
  confirmed?: boolean;
}
