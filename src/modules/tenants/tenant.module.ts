import { Tenant<PERSON><PERSON>roller } from './tenant.controller';
import { Global, Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Tenant, tenantSchema } from './schemas/tenant.schema';
import { User, User_Schema } from '../users/schemas/user.schema';
import { TenantUser, tenantUserSchema } from './schemas/tenant-user.schema';
import { Role, roleSchema } from '../roles/schemas/role.schema';
import { TenantServices } from './tenant.service';
import { UsersModule } from '../users/users.module';
import { ConfigModule } from '@nestjs/config';
import { CaslModule } from '../casl/casl.module';
import * as dotenv from 'dotenv';
import { BranchModule } from '../branch/branch.module';
import { DatabaseModule } from '../database/database.module';
import {
  Permission,
  permissionSchema,
} from '../permissions/schema/permisstions.schema';
import { UserBranchesRolesModule } from '../user-branches-roles/user-branch-role.module';

dotenv.config();
@Global()
@Module({
  imports: [
    ConfigModule,
    DatabaseModule,
    forwardRef(() => BranchModule),
    CaslModule,
    forwardRef(() => UsersModule),
    MongooseModule.forFeature([
      { name: Tenant.name, schema: tenantSchema },
      { name: User.name, schema: User_Schema },
      { name: TenantUser.name, schema: tenantUserSchema },
      { name: Role.name, schema: roleSchema },
      { name: Permission.name, schema: permissionSchema },
    ]),
    MongooseModule.forFeature(
      [
        { name: Tenant.name, schema: tenantSchema },
        { name: TenantUser.name, schema: tenantUserSchema },
      ],
      'common',
    ),
    UserBranchesRolesModule,
  ],

  controllers: [TenantController],
  providers: [TenantServices],
  exports: [TenantServices],
})
export class TenantModule {}
