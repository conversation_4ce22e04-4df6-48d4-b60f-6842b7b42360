import { forwardRef, HttpException, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as argon2 from 'argon2';
import { Connection, Model, Types } from 'mongoose';
import { CreateTenantRequestDto } from './dtos/create-tenant.dto';
import { extend } from 'lodash';
import DataBases from '../database/database.provider';
import { Tenant, tenantSchema, TenantDocument } from './schemas/tenant.schema';
import { User, User_Schema, UserDocument } from '../users/schemas/user.schema';
import { TenantUser, tenantUserSchema } from './schemas/tenant-user.schema';
import { Role, roleSchema } from '../roles/schemas/role.schema';
import { GetTenantRequestDto } from './dtos/get-tenant.dto';
import {
  UpdateTenantRequestDto,
  UpdateUserTenantRequestDto,
} from './dtos/update-tenant.dto';
import { UsersService } from '../users/users.service';
import { RegisterTenantRequestDto } from './dtos/register-tenant.dto';
import {
  GetTenantUserRequestDto,
  GetTenantUserResponseDto,
} from './dtos/get-tenant-user.dto';
import { CreateTenantUserRequestDto } from './dtos/create-tenant-user.dto';
import { ConfigService } from '@nestjs/config';
import { paginate } from '../../utils/dto/pagination.dto';
import { Branch, branchSchema } from '../branch/schemas/branch.schema';
import { MongooseConfigService } from '../database/mongoose.service';
import { Company, companySchema } from '../company/schemas/company.schema';
import { nameOf } from '../../utils/object-key-name';
import { usersExceptionCode } from '../../exceptions/exception-code.users';
import { randomPass } from '../../utils/random-str';
import { permList } from '../../utils/assets/perm';
import {
  Permission,
  permissionSchema,
} from '../permissions/schema/permisstions.schema';
import {
  UserBranchRole,
  userBranchRoleSchema,
} from '../user-branches-roles/schema/user-branches-roles.schema';
import { Store, storeSchema } from '../store/schema/store.schema';
import { TemplateSeeder } from '../../utils/seeders/template-seeder';
import {
  TemplateDesign,
  templateDesignSchema,
} from '../template-design/schema/template-design.schema';
import {
  ServiceVersion,
  serviceVersionSchema,
} from '../script-runner/schema/version.schema';
import { schemaVersion } from '../script-runner/database_scripts/version';

@Injectable({})
export class TenantServices {
  constructor(
    @InjectModel(Tenant.name, 'common')
    private tenantModel: Model<TenantDocument>,
    @InjectModel(TenantUser.name, 'common')
    private tenantUserModel: Model<TenantUser>,
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => UsersService))
    private readonly userService: UsersService,
    @Inject(MongooseConfigService)
    private readonly mongooseConfigService: MongooseConfigService,
  ) {}
  //TODO: SWITCH between connections
  async create(tenantData: CreateTenantRequestDto): Promise<User[]> {
    const { name, email } = tenantData.user;
    const transformObject = { ...tenantData } as any;
    transformObject.user = { name, email };
    const lastCode = (await this.tenantModel.findOne().sort({ code: -1 }))
      ?.code;

    transformObject.code = !lastCode ? 1 : parseInt('' + lastCode) + 1;

    const emailExist = await this.tenantUserModel.findOne({
      user_email: tenantData.user.email,
    });
    if (emailExist) {
      throw new HttpException(
        nameOf(usersExceptionCode, (x) => x.emailExist),
        usersExceptionCode.emailExist,
      );
    }

    const tenantDataSaved = await this.tenantModel.create(transformObject);
    return this.newDbConnection(
      transformObject.code,
      tenantDataSaved._id,
      tenantData,
      tenantDataSaved,
    );
  }

  async newDbConnection(code, tenantId, data, tenantData?): Promise<User[]> {
    const liveConnections = await DataBases.getConnection(
      this.mongooseConfigService.createDbUrl(code),
    );

    const branchModel =
      liveConnections.models.Branch ||
      (liveConnections.model<Branch>(Branch.name, branchSchema) as any);

    const storeModel =
      liveConnections.models.Store ||
      (liveConnections.model<Store>(Store.name, storeSchema) as any);

    const templateDesignModel =
      liveConnections.models.TemplateDesign ||
      (liveConnections.model<TemplateDesign>(
        TemplateDesign.name,
        templateDesignSchema,
      ) as any);

    const serviceVersionModel =
      liveConnections.models.ServiceVersion ||
      (liveConnections.model<ServiceVersion>(
        ServiceVersion.name,
        serviceVersionSchema,
      ) as any);

    const companyModel =
      liveConnections.models.Company ||
      (liveConnections.model<Company>(Company.name, companySchema) as any);

    const roleModel =
      liveConnections.models.Role ||
      (liveConnections.model<Role>(Role.name, roleSchema) as any);

    const permissionModel =
      liveConnections.models.Permission ||
      (liveConnections.model<Permission>(
        Permission.name,
        permissionSchema,
      ) as any);

    const userModel =
      liveConnections.models.User ||
      (liveConnections.model<User>(User.name, User_Schema) as any);

    const userBranchRoleModel =
      liveConnections.models.UserBranchRole ||
      (liveConnections.model<UserBranchRole>(
        UserBranchRole.name,
        userBranchRoleSchema,
      ) as any);

    await companyModel.create({
      name: tenantData.name,
      short_name: tenantData.short_name,
      code,
      phone: tenantData.phone,
      email: tenantData.email,
      country: tenantData.country,
      address: tenantData.address,
      website: tenantData.website,
      notes: tenantData.notes,
    });

    const branchData = await branchModel.create({
      general_information: {
        code,
        name: { en: tenantData.name, ar: tenantData.name },
        registration_number: tenantData.registration_number,
        tax_code: tenantData.tax_code,
        activation_status: true,
        is_default: true,
        is_main_branch: true,
      },
      national_address: {
        trade_name: {
          en: tenantData.name,
          ar: tenantData.name,
        },
        street: tenantData.national_address,
      },
    });

    await storeModel.create({
      number: 1,
      name: {
        en: tenantData.name,
        ar: tenantData.name,
      },
      branch: branchData._id,
      phone: tenantData.phone,
      is_default: true,
    });

    const templateArray = new TemplateSeeder().getTemplates().templates_array;
    await templateDesignModel.create(templateArray);

    await serviceVersionModel.create({ version: schemaVersion.version });

    const permissions = await permissionModel.insertMany(permList);

    const permissionIds = permissions.map((permission) => permission._id);
    const roleData = await roleModel.create({
      name: 'admin',
      permissions: permissionIds,
    });

    const users = [
      {
        name: 'osus',
        password: await argon2.hash(randomPass(16)),
        email: `osus_${code}@test.com`,
        positions: [{ roles: [roleData._id], branches: [branchData._id] }],
        active: true,
        email_verified: true,
      },
      {
        ...data.user,
        password: await argon2.hash(data.user.password),
        positions: [{ roles: [roleData._id], branches: [branchData._id] }],
        active: true,
      },
    ];

    const tenantUsers = [];
    for (const user of users) {
      const userExists = await this.tenantUserModel.findOne({
        user_email: user.email,
      });

      if (userExists) {
        throw new HttpException(
          nameOf(usersExceptionCode, (x) => x.emailExist),
          usersExceptionCode.emailExist,
        );
      }
      tenantUsers.push({ user_email: user.email, tenant: tenantId._id });
    }

    await this.tenantUserModel.insertMany(tenantUsers);

    const usersData = (await userModel.insertMany(users)) as UserDocument[];

    const userBranchRoleData = [];
    usersData.forEach((user) => {
      userBranchRoleData.push({
        user: user._id,
        branch: branchData._id,
        roles: [roleData._id],
      });
    });
    await userBranchRoleModel.create(userBranchRoleData);

    return usersData;
  }

  async createTenantUser(
    users: CreateTenantUserRequestDto[],
    tenant: Types.ObjectId,
  ): Promise<UserDocument[]> {
    //email and tenant id saves in main tenant
    const tenantData = await this.tenantModel.findOne({ code: tenant });
    const userModelData = await this.userService.insertMany(users);
    const tenantUsers = users.map((user) => {
      return {
        user_email: user.email,
        tenant: tenantData._id,
      };
    });
    await this.tenantUserModel.insertMany(tenantUsers);
    return userModelData;
  }

  async get(filters: GetTenantRequestDto): Promise<GetTenantUserResponseDto> {
    const { page, limit, queries, ...rest } = filters;
    const transformFilter = { ...filters };
    delete transformFilter.page;
    delete transformFilter.limit;
    let search = rest ?? {};
    if (queries) {
      const searchfield = { $regex: new RegExp(queries), $options: 'i' };
      search = {
        ...search,
        $or: [
          { name: searchfield },
          { phone: searchfield },
          { email: searchfield },
          { address: searchfield },
          { website: searchfield },
          { notes: searchfield },
        ],
      } as any;
    }
    const tenantData = await this.tenantModel
      .find(search)
      .skip((page - 1) * limit)
      .limit(limit);
    const meta = await paginate(limit, page, this.tenantModel, search);
    return { result: tenantData, meta };
  }

  async getAll() {
    return await this.tenantModel.find({}).lean();
  }
  async getTenantUser(filters: GetTenantUserRequestDto): Promise<TenantUser> {
    const tenantData = await this.tenantUserModel
      .findOne(filters)
      .populate('tenant');

    if (!tenantData) {
      throw new HttpException(
        nameOf(usersExceptionCode, (x) => x.tenantNotFound),
        usersExceptionCode.tenantNotFound,
      );
    }
    return tenantData;
  }

  async getTenantUserWithConnections(
    filters: GetTenantUserRequestDto,
    connections: Connection,
  ): Promise<TenantUser> {
    const tennatUserModel =
      connections.models.TenantUser ||
      (connections.model<TenantUser>(TenantUser.name, tenantUserSchema) as any);
    console.log(connections.models);

    if (!connections.models.Tenant) {
      await connections.model<Tenant>(Tenant.name, tenantSchema);
    }
    const tenantData = await tennatUserModel
      .findOne(filters)
      .populate('tenant');
    if (!tenantData) {
      throw new HttpException(
        nameOf(usersExceptionCode, (x) => x.tenantNotFound),
        usersExceptionCode.tenantNotFound,
      );
    }
    return tenantData;
  }

  async getTenantUser2(filters: GetTenantUserRequestDto): Promise<TenantUser> {
    return await this.tenantUserModel.findOne(filters).populate('tenant');
  }

  async getOne(filters: GetTenantRequestDto) {
    delete filters.page;
    delete filters.limit;
    return await this.tenantModel.findOne(filters);
  }

  async update(filters: UpdateTenantRequestDto): Promise<Tenant> {
    const transformFilter = { ...filters };
    delete transformFilter._id;
    const tenantData = await this.tenantModel.findOne({ _id: filters._id });
    if (!tenantData) {
      throw new HttpException(
        nameOf(usersExceptionCode, (x) => x.tenantNotFound),
        usersExceptionCode.tenantNotFound,
      );
    }
    extend(tenantData, transformFilter);
    await tenantData.save();
    return tenantData;
  }

  async updateTenantUser(filters: UpdateUserTenantRequestDto, updateData) {
    const tenantUserData = await this.tenantUserModel.findOne(filters);

    if (!tenantUserData) {
      throw new HttpException(
        nameOf(usersExceptionCode, (x) => x.tenantNotFound),
        usersExceptionCode.tenantNotFound,
      );
    }
    extend(tenantUserData, updateData);
    await tenantUserData.save();
    return tenantUserData;
  }

  async deleteTenantUser(filters: UpdateUserTenantRequestDto) {
    const tenantUserData = await this.tenantUserModel.findOneAndDelete(filters);
    if (!tenantUserData) {
      throw new HttpException(
        nameOf(usersExceptionCode, (x) => x.tenantNotFound),
        usersExceptionCode.tenantNotFound,
      );
    }
    return tenantUserData;
  }

  async register(tenantData: RegisterTenantRequestDto): Promise<Tenant> {
    const transformObject = { ...tenantData } as any;
    transformObject.user = undefined;
    const lastCode = await this.tenantModel.findOne({}).sort({ code: -1 });
    transformObject.code = parseInt('' + lastCode.code) + 1;

    return await this.tenantModel.create(transformObject);
  }

  async getTenantData(code) {
    const connection = await this.commonDbConnection();
    const tenantModel =
      connection.models.Tenant ||
      (connection.model<Tenant>(Tenant.name, tenantSchema) as any);
    return await tenantModel.findOne({ code });
  }

  private async commonDbConnection(): Promise<Connection> {
    const liveConnections = await DataBases.getConnection(
      this.mongooseConfigService.createDbUrl(),
    );
    return liveConnections;
  }
}
