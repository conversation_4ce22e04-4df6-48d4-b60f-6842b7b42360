import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { Api<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>pi<PERSON>eader, ApiTags } from '@nestjs/swagger';
import { RequestWithUser } from '../auth/interfaces/authorize.interface';
import {
  CreateReturnSaleDto,
  FindInvoiceDesktopDto,
  FindInvoiceDto,
  UpdateReturnSaleDto,
} from './dto';
import { ReturnSaleService } from './return-sale.service';
import { CaslGuard } from '../casl/guards/casl.guard';
import { BranchHeaderDto } from '../../utils/dto/request-headers.dto';
import { RequestHeaders } from '../../utils/decorators/request-header.validator';
import { GetTemplateDto } from '../sale/dto';
import { Abilities } from '../casl/guards/abilities.decorator';
import { authType } from '../auth/guards/auth-type.decorator';

@ApiTags('return-sale')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@Controller('trade/return-sale')
export class ReturnSaleController {
  constructor(private readonly returnSaleService: ReturnSaleService) {}

  @UseGuards(CaslGuard)
  @Abilities({ action: 'create', subject: 'sales_return_invoice' })
  @Post()
  create(
    @Req() req: RequestWithUser,
    @Body() createReturnSaleDto: CreateReturnSaleDto,
  ) {
    return this.returnSaleService.create(req, createReturnSaleDto);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'sales_return_invoice' })
  @authType('both')
  @ApiBearerAuth('TOKEN')
  @Get()
  findAll(
    @Req() req: RequestWithUser,
    @Query() findInvoiceDto: FindInvoiceDto,
    @RequestHeaders() headers: BranchHeaderDto,
  ) {
    const { branch } = headers;

    return this.returnSaleService.findAll(req, findInvoiceDto, branch);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'list', subject: 'sales_return_invoice' })
  @authType('both')
  @ApiBearerAuth('TOKEN')
  @Get('/desktop')
  findAllDesktop(
    @Query() findInvoiceDto: FindInvoiceDesktopDto,
    @RequestHeaders() headers: BranchHeaderDto,
  ) {
    const { branch } = headers;

    return this.returnSaleService.findAllDesktop(findInvoiceDto, branch);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'read', subject: 'sales_return_invoice' })
  @Get(':id')
  findOne(@Param('id') id: string, @Req() req: RequestWithUser) {
    return this.returnSaleService.findOne(req.user?.code, id);
  }

  @Post('/template')
  printOne(
    @Body() getTemplateDto: GetTemplateDto,
    @Req() req: RequestWithUser,
  ): Promise<any> {
    return this.returnSaleService.printOne(req, getTemplateDto);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'edit', subject: 'sales_return_invoice' })
  @Patch(':id')
  update(
    @Req() req: RequestWithUser,
    @Param('id') id: string,
    @Body() updateReturnSaleDto: UpdateReturnSaleDto,
  ) {
    return this.returnSaleService.update(req, id, updateReturnSaleDto);
  }

  @UseGuards(CaslGuard)
  @Abilities({ action: 'delete', subject: 'sales_return_invoice' })
  @Delete(':id')
  remove(@Req() req: RequestWithUser, @Param('id') id: string) {
    return this.returnSaleService.delete(req, id);
  }
}
