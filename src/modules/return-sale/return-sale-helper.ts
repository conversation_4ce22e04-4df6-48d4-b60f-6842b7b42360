import { HttpException } from '@nestjs/common';
import { nameOf } from '../../utils/object-key-name';
import { itemType } from '../sale/dto';
import { Calculator } from '../../utils/calculator';
import { usersExceptionCode } from '../../exceptions/exception-code.users';
import { CustomHttpException } from '../../utils/custom-http.exception';

export async function validateTransactions(
  transactions,
  taxRate,
  saleInvoice,
  total_discounts,
  total_vat,
  invoice_total,
  isUpdate = false,
  returnSaleInvoice = null,
) {
  const subtotalAfterDiscountSum = new Calculator();
  const totalRowDiscount = new Calculator();
  const totalRowVat = new Calculator();
  const totalRowTotal = new Calculator();
  const totalRowInvoiceDiscount = new Calculator();
  const totalRowInvoiceDiscountVat = new Calculator();
  const costOfAllItems = new Calculator(0);

  if (!saleInvoice) {
    throw new HttpException(
      nameOf(
        usersExceptionCode,
        (exceptions) => exceptions.salesInvoiceNotFound,
      ),
      usersExceptionCode.salesInvoiceNotFound,
    );
  }
  if (saleInvoice.invoice_type === 'voucher') {
    throw new HttpException(
      nameOf(
        usersExceptionCode,
        (exceptions) => exceptions.invoiceTypeNotValid,
      ),
      usersExceptionCode.invoiceTypeNotValid,
    );
  }
  // check items and units
  const filteredItems = transactions.filter(
    (transaction) => transaction.item_type === itemType.goods,
  );

  const requiredInventoryJournalling = filteredItems.length ? true : false;

  const existItems = saleInvoice.transactions.map((item) => String(item.item));

  const salesInvoiceSubtotalAfterDiscount = saleInvoice.transactions.reduce(
    (summation, item) =>
      summation + Calculator.subtract(item.subtotal, item.discount).number(),
    0,
  );

  const itemChangesLog = [];

  for (const transaction of transactions) {
    if (!existItems.includes(transaction.item)) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.transactionDoesNotHaveItems,
        ),
        usersExceptionCode.transactionDoesNotHaveItems,
      );
    }

    const saleItem = saleInvoice.transactions.find(
      (item) =>
        item.item === transaction.item &&
        item.unit === transaction.unit &&
        String(item._id) === String(transaction.transaction_reference),
    );
    let beforeUpdatetransaction;
    if (isUpdate && returnSaleInvoice) {
      beforeUpdatetransaction = returnSaleInvoice.transactions.find(
        (item) =>
          item.item === transaction.item &&
          item.unit === transaction.unit &&
          String(item.transaction_reference) ===
            String(transaction.transaction_reference),
      );
    }
    console.log('saleItem', saleItem);
    //TODO: replacing returned_qty has bug when we have other return invoices
    if (
      (isUpdate && saleItem.qty < transaction.qty) ||
      (!isUpdate &&
        saleItem.qty <
          saleItem.returned_qty +
            transaction.qty -
            (beforeUpdatetransaction?.qty | 0))
    ) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.itemQuantityNotValid,
        ),
        usersExceptionCode.itemQuantityNotValid,
      );
    }

    const returnedQtyTemp = saleItem.returned_qty;
    const singleItemChange = {
      changed_qty: 0,
      type: 'increase_return',
    };
    if (returnedQtyTemp < transaction.qty) {
      // value of transaction increased
      singleItemChange.changed_qty = Math.abs(
        returnedQtyTemp - transaction.qty,
      );
      singleItemChange.type = 'increase_return';
      itemChangesLog.push({ ...singleItemChange });
    }
    if (returnedQtyTemp > transaction.qty) {
      // value of transaction reduced
      singleItemChange.changed_qty = Math.abs(
        returnedQtyTemp - transaction.qty,
      );
      singleItemChange.type = 'reduce_return';
      itemChangesLog.push({ ...singleItemChange });
    }

    if (isUpdate) {
      saleItem.returned_qty +=
        transaction.qty - (beforeUpdatetransaction?.qty | 0);
    } else {
      saleItem.returned_qty += transaction.qty;
    }
    transaction.item_invoice_name = saleItem.item_invoice_name;
    transaction.description = (saleItem as any).description;

    if (saleItem.price !== transaction.price) {
      throw new CustomHttpException(
        nameOf(usersExceptionCode, (exceptions) => exceptions.invalidSubtotal),
        usersExceptionCode.invalidSubtotal,
        {
          calculated_price: saleItem.price,
          submitted_price: transaction.price,
        },
      );
    }

    const subtotal = Math.min(
      Calculator.mul(transaction.price, transaction.qty).round().number(),
      saleItem.remaining_subtotal,
    );
    if (
      saleItem.subtotal <
      Calculator.mul(transaction.price, singleItemChange.changed_qty)
        .round()
        .number()
    ) {
      throw new CustomHttpException(
        nameOf(usersExceptionCode, (exceptions) => exceptions.invalidSubtotal),
        usersExceptionCode.invalidSubtotal,
        {
          calculated_subtotal: subtotal,
          submitted_subtotal: transaction.subtotal,
        },
      );
    }
    if (singleItemChange.changed_qty > 0) {
      if (singleItemChange.type === 'reduce_return') {
        console.log('reduced ');
        console.log(singleItemChange.changed_qty, transaction.price);

        saleItem.remaining_subtotal = Calculator.sum(
          saleItem.remaining_subtotal,
          Calculator.mul(transaction.price, singleItemChange.changed_qty)
            .round()
            .number(),
        ).number();
        console.log(saleItem.remaining_subtotal, 'sub total');
      }
      if (singleItemChange.type === 'increase_return') {
        console.log('add');
        console.log(singleItemChange.changed_qty, transaction.price);

        saleItem.remaining_subtotal = Calculator.subtract(
          saleItem.remaining_subtotal,
          Calculator.mul(transaction.price, singleItemChange.changed_qty)
            .round()
            .number(),
        ).number();
        console.log(saleItem.remaining_subtotal, 'sub total');
      }
    }

    const discount = Math.min(
      Calculator.mul(
        saleItem.discount,
        Calculator.div(transaction.qty, saleItem.qty).number(),
      )
        .round()
        .number(),
      saleItem.remaining_discount,
    );
    if (discount !== transaction.discount) {
      throw new CustomHttpException(
        nameOf(usersExceptionCode, (exceptions) => exceptions.invalidDiscount),
        usersExceptionCode.invalidDiscount,
        {
          calculated_discount: discount,
          submitted_discount: transaction.discount,
        },
      );
    }

    totalRowDiscount.plus(transaction.discount);

    const itemDiscount = Calculator.mul(saleItem.discount, saleItem.qty)
      .round()
      .number();
    if (singleItemChange.changed_qty != 0) {
      if (singleItemChange.type === 'reduce_return') {
        saleItem.remaining_discount = Calculator.sum(
          saleItem.remaining_discount,
          Calculator.mul(itemDiscount, singleItemChange.changed_qty)
            .round()
            .number(),
        )
          .round()
          .number();
      }
      if (singleItemChange.type === 'increase_return') {
        saleItem.remaining_discount = Calculator.subtract(
          saleItem.remaining_discount,
          Calculator.mul(itemDiscount, singleItemChange.changed_qty)
            .round()
            .number(),
        )
          .round()
          .number();
      }
    }
    /*     saleItem.remaining_discount = Calculator.subtract(
      saleItem.remaining_discount,
      transaction.discount,
    ).number();
 */

    const subtotalAfterDiscount = Calculator.subtract(
      transaction.subtotal,
      transaction.discount,
    ).number();

    subtotalAfterDiscountSum.plus(subtotalAfterDiscount);
    const vatPerItem = Calculator.mul(saleItem.price, taxRate).number();
    const trxVat = Calculator.mul(vatPerItem, singleItemChange.changed_qty)
      .round()
      .number();
    if (saleInvoice.free_tax) {
      if (transaction.vat !== 0) {
        throw new CustomHttpException(
          nameOf(usersExceptionCode, (exception) => exception.freeTaxVat),
          usersExceptionCode.freeTaxVat,
          {
            calculated_vat: 0,
            submitted_vat: trxVat,
          },
        );
      }
      transaction.free_tax = true;
    } else {
      if (!saleItem.free_tax) {
        const vat = Math.min(
          Calculator.mul(subtotalAfterDiscount, taxRate).round().number(),
          saleItem.remaining_vat,
        );
        if (saleItem.vat < trxVat) {
          throw new CustomHttpException(
            nameOf(usersExceptionCode, (exceptions) => exceptions.invalidVat),
            usersExceptionCode.invalidVat,
            {
              calculated_vat: vat,
              submitted_vat: trxVat,
            },
          );
        }
        transaction.free_tax = false;
      } else {
        if (trxVat !== 0) {
          throw new CustomHttpException(
            nameOf(usersExceptionCode, (exception) => exception.freeTaxVat),
            usersExceptionCode.freeTaxVat,
            {
              calculated_vat: 0,
              submitted_vat: trxVat,
            },
          );
        }
        transaction.free_tax = true;
      }
    }

    if (singleItemChange.changed_qty != 0) {
      if (singleItemChange.type === 'reduce_return') {
        saleItem.remaining_vat = Calculator.sum(
          saleItem.remaining_vat,
          Calculator.mul(vatPerItem, singleItemChange.changed_qty)
            .round()
            .number(),
        ).number();
      }
      if (singleItemChange.type === 'increase_return') {
        saleItem.remaining_vat = Calculator.subtract(
          saleItem.remaining_vat,
          Calculator.mul(vatPerItem, singleItemChange.changed_qty)
            .round()
            .number(),
        ).number();
      }
    }

    /*     saleItem.remaining_vat = Calculator.subtract(
      saleItem.remaining_vat,
      transaction.vat,
    ).number();
 */
    totalRowVat.plus(transaction.vat);

    const total = Calculator.sum(
      subtotalAfterDiscount,
      transaction.vat,
    ).number();

    if (total !== transaction.total) {
      throw new CustomHttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.invalidTotalWithVat,
        ),
        usersExceptionCode.invalidTotalWithVat,
        {
          calculated_total: total,
          submitted_total: transaction.total,
        },
      );
    }

    totalRowTotal.plus(transaction.total);

    const rowInvoiceDiscount = Math.min(
      Calculator.mul(
        Calculator.div(
          saleInvoice.invoice_discount,
          salesInvoiceSubtotalAfterDiscount,
        ).number(),
        transaction.subtotal,
      )
        .round()
        .number(),
      saleItem.remaining_row_invoice_discount,
    );

    if (rowInvoiceDiscount !== transaction.row_invoice_discount) {
      throw new CustomHttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.rowInvoiceDiscountNotValid,
        ),
        usersExceptionCode.rowInvoiceDiscountNotValid,
        {
          calculated_row_invoice_discount: rowInvoiceDiscount,
          submitted_row_invoice_discount: transaction.row_invoice_discount,
        },
      );
    }

    totalRowInvoiceDiscount.plus(transaction.row_invoice_discount);

    saleItem.remaining_row_invoice_discount = Calculator.subtract(
      saleItem.remaining_row_invoice_discount,
      transaction.row_invoice_discount,
    ).number();

    let rowInvoiceDiscountVat;
    if (saleInvoice.free_tax || saleItem.free_tax) {
      rowInvoiceDiscountVat = Math.min(
        Calculator.mul(transaction.row_invoice_discount, 0).round().number(),
        saleItem.remaining_row_invoice_discount_vat,
      );
    } else {
      rowInvoiceDiscountVat = Math.min(
        Calculator.mul(transaction.row_invoice_discount, taxRate)
          .round()
          .number(),
        saleItem.remaining_row_invoice_discount_vat,
      );
    }

    if (rowInvoiceDiscountVat !== transaction.row_invoice_discount_vat) {
      throw new CustomHttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.rowInvoiceDiscountVatNotValid,
        ),
        usersExceptionCode.rowInvoiceDiscountVatNotValid,
        {
          calculated_row_invoice_discount_vat: rowInvoiceDiscountVat,
          submitted_row_invoice_discount_vat:
            transaction.row_invoice_discount_vat,
        },
      );
    }

    totalRowInvoiceDiscountVat.plus(transaction.row_invoice_discount_vat);

    saleItem.remaining_row_invoice_discount_vat = Calculator.subtract(
      saleItem.remaining_row_invoice_discount_vat,
      transaction.row_invoice_discount_vat,
    ).number();

    const averageCost = Math.min(
      Calculator.mul(saleItem.average_cost, transaction.qty).round().number(),
      saleItem.remaining_average_cost,
    );

    costOfAllItems.plus(averageCost);

    transaction.average_cost = +averageCost;

    saleItem.remaining_average_cost = Calculator.subtract(
      saleItem.remaining_average_cost,
      averageCost,
    ).number();
  }

  const totalDiscount = Calculator.sum(
    totalRowDiscount.valueOf(),
    totalRowInvoiceDiscount.valueOf(),
  )
    .round()
    .number();

  if (totalDiscount !== total_discounts) {
    throw new CustomHttpException(
      nameOf(
        usersExceptionCode,
        (exceptions) => exceptions.totalDiscountNotValid,
      ),
      usersExceptionCode.totalDiscountNotValid,
      {
        calculated_total_discount: totalDiscount,
        submitted_total_discount: total_discounts,
      },
    );
  }

  const totalVat = Calculator.subtract(
    totalRowVat.valueOf(),
    totalRowInvoiceDiscountVat.valueOf(),
  )
    .round()
    .number();
  if (totalVat !== total_vat) {
    throw new CustomHttpException(
      nameOf(usersExceptionCode, (exceptions) => exceptions.totalVatNotValid),
      usersExceptionCode.totalVatNotValid,
      {
        calculated_total_vat: totalVat,
        submitted_total_vat: total_vat,
      },
    );
  }

  const subtotals = Calculator.subtract(
    subtotalAfterDiscountSum.valueOf(),
    totalRowInvoiceDiscount.valueOf(),
  ).number();

  const finalPrice = Calculator.subtract(
    Calculator.subtract(
      Number(totalRowTotal),
      totalRowInvoiceDiscountVat.valueOf(),
    ).number(),
    totalRowInvoiceDiscount.valueOf(),
  ).number();

  if (finalPrice !== invoice_total) {
    throw new CustomHttpException(
      nameOf(
        usersExceptionCode,
        (exceptions) => exceptions.finalPriceIsNotValid,
      ),
      usersExceptionCode.finalPriceIsNotValid,
      {
        calculated_invoice_total: finalPrice,
        submitted_invoice_total: invoice_total,
      },
    );
  }
  // eslint-disable-next-line @typescript-eslint/naming-convention
  return { costOfAllItems, subtotals, requiredInventoryJournalling };
}
