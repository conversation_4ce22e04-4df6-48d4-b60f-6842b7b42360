import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SaleModule } from '../sale/sale.module';
import { invoiceSchema } from '../sale/schema/invoice.schema';
import { ReturnSaleController } from './return-sale.controller';
import { ReturnSaleService } from './return-sale.service';
import { ReturnSale, returnSaleSchema } from './schema/return-sale.schema';
import { TemplateDesignModule } from '../template-design/template-design.module';
import { CompanyModule } from '../company/company.module';
import { StoreModule } from '../store/store.module';
import { ItemsModule } from '../inventory-item/items.module';
import { PaymentTypesModule } from '../payment-types/payment-types.module';
import { BranchModule } from '../branch/branch.module';

@Module({
  imports: [
    TemplateDesignModule,
    MongooseModule.forFeature([
      { name: ReturnSale.name, schema: returnSaleSchema },
      { name: 'Sale', schema: invoiceSchema },
    ]),
    forwardRef(() => SaleModule),
    CompanyModule,
    StoreModule,
    ItemsModule,
    BranchModule,
    PaymentTypesModule,
  ],
  controllers: [ReturnSaleController],
  providers: [ReturnSaleService],
  exports: [ReturnSaleService],
})
export class ReturnSaleModule {}
