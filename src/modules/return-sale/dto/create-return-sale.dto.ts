import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Types } from 'mongoose';
import { CreateTransactionItemReturnDto } from './create-transaction-item.dto';
import { invoiceType } from './enums';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';
import { isTwoDecimalPlaces } from '../../../utils/decorators/number-two-digits-places';
import { PrintOptionsDto } from '../../sale/dto/print-options.dto';

export class CreateReturnSaleDto {
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  invoice_no: number;

  @Transform((param) => param.value.toLowerCase())
  @ApiProperty({ enum: invoiceType })
  @IsEnum(invoiceType)
  invoice_type: invoiceType;

  @ApiProperty()
  @IsString()
  @IsOptional()
  payment_type: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  customer_name_ar: string;

  @ApiProperty()
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  sale_id: Types.ObjectId;

  @ApiProperty()
  @IsOptional()
  @IsString()
  note: string;

  @ApiProperty()
  @IsNumber()
  @isTwoDecimalPlaces()
  total_discounts: number = 0;

  @ApiProperty()
  @IsNumber()
  //TODO enable this once frontend enabled
  // @isTwoDecimalPlaces()
  total_vat: number;

  @ApiProperty()
  @IsNumber()
  @isTwoDecimalPlaces()
  invoice_total: number;

  @ApiProperty({ example: '2022-11-10' })
  @IsOptional()
  @IsDateString()
  invoice_date: string = new Date().toISOString();

  @ApiProperty()
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch_id: Types.ObjectId;

  @ApiProperty()
  @IsArray()
  @ValidateNested()
  @Type(() => CreateTransactionItemReturnDto)
  transactions: CreateTransactionItemReturnDto[];

  @ApiProperty()
  @ValidateNested()
  @Type(() => PrintOptionsDto)
  printOptions: PrintOptionsDto;

  @ApiHideProperty()
  registered_customer: boolean;

  @ApiHideProperty()
  free_tax: boolean;

  @ApiHideProperty()
  customer_name: string;

  @ApiHideProperty()
  customer_id: string;

  @ApiHideProperty()
  customer_phone: string;

  @ApiHideProperty()
  tax_no: string;

  @ApiHideProperty()
  invoice_discount: number;

  @ApiHideProperty()
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  store: Types.ObjectId;

  @ApiHideProperty()
  @IsOptional()
  @IsString()
  sales_representative: string;

  @ApiHideProperty()
  sale_total_discounts: number;

  @ApiHideProperty()
  qr_code: string;

  @ApiHideProperty()
  sale_number: number;
}
