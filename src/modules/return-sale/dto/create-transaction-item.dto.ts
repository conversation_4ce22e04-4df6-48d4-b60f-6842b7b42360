import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumber, IsString } from 'class-validator';
import { ObjectId, Types } from 'mongoose';
import { Transform } from 'class-transformer';
import { itemType } from '../../sale/dto';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';
import { isTwoDecimalPlaces } from '../../../utils/decorators/number-two-digits-places';

export class CreateTransactionItemReturnDto {
  @ApiProperty()
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  transaction_reference: Types.ObjectId;

  @ApiProperty()
  @IsString()
  item: string;

  @ApiProperty()
  @IsString()
  unit: string;

  @ApiHideProperty()
  free_tax: boolean;

  @Transform((param) => param.value.toLowerCase())
  @ApiProperty({ enum: itemType })
  @IsNotEmpty()
  @IsEnum(itemType)
  item_type: itemType;

  @ApiProperty({ default: 0 })
  @IsNumber()
  qty: number;

  @ApiProperty({ default: 0 })
  @IsNumber()
  @isTwoDecimalPlaces()
  price: number;

  @ApiProperty({ default: 0 })
  @IsNumber()
  @isTwoDecimalPlaces()
  subtotal: number;

  @ApiProperty({ default: 0 })
  @IsNumber()
  @isTwoDecimalPlaces()
  discount: number = 0;

  @ApiProperty({ default: 0 })
  @IsNumber()
  @isTwoDecimalPlaces()
  vat: number;

  @ApiProperty({ default: 0 })
  @IsNumber()
  @isTwoDecimalPlaces()
  total: number;

  @ApiProperty({ default: 0 })
  @IsNumber()
  @isTwoDecimalPlaces()
  row_invoice_discount: number;

  @ApiProperty({ default: 0 })
  @IsNumber()
  @isTwoDecimalPlaces()
  row_invoice_discount_vat: number;

  @ApiHideProperty()
  item_invoice_name: string;

  @ApiHideProperty()
  description: string;

  @ApiHideProperty()
  // eslint-disable-next-line @typescript-eslint/naming-convention
  itemObject: { _id: ObjectId; units_prices: Array<any> };

  @ApiHideProperty()
  item_number: number;

  @ApiHideProperty()
  profit_margin: number;

  @ApiHideProperty()
  average_cost: number;
}
