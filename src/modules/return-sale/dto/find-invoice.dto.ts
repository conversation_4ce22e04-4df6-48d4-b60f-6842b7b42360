import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsEnum, IsOptional } from 'class-validator';
import { PaginationDto } from '../../../utils/dto/pagination.dto';
import { invoiceType } from './enums';
import { Expose, Transform } from 'class-transformer';
import { Types } from 'mongoose';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';

export class FindInvoiceDto extends PaginationDto {
  @ApiProperty({
    description: 'search through invoice_no and customer_name',
  })
  @IsOptional()
  queries?: string;

  @ApiProperty()
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  store?: Types.ObjectId;

  @ApiProperty({ enum: invoiceType })
  @IsOptional()
  @IsEnum(invoiceType)
  @Expose({ name: 'invoice_type' })
  invoiceType?: invoiceType;

  @ApiProperty({ example: '2022-11-10' })
  @IsOptional()
  @IsDateString()
  @Expose({ name: 'from_date' })
  fromDate?: string;

  @ApiProperty({ example: '2022-11-10' })
  @IsOptional()
  @IsDateString()
  @Expose({ name: 'to_date' })
  toDate?: string = new Date().toISOString();
}
