import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsOptional } from 'class-validator';
import { Expose, Transform } from 'class-transformer';
import { Types } from 'mongoose';
import { safeMongoIdTransformer } from '../../../utils/transformers/mongo-id-transformer';

export class FindInvoiceDesktopDto {
  @ApiProperty()
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  store?: Types.ObjectId;

  @ApiProperty({ example: '2022-11-10' })
  @IsOptional()
  @IsDateString()
  @Expose({ name: 'from_date' })
  fromDate?: string;
}
