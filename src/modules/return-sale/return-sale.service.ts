import {
  forwardRef,
  HttpException,
  Inject,
  Injectable,
  Scope,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { extend } from 'lodash';
import { Model, Types } from 'mongoose';
import { nameOf } from '../../utils/object-key-name';
import { RequestWithUser } from '../auth/interfaces/authorize.interface';
import { GetTemplateDto } from '../sale/dto';
import { SaleService } from '../sale/sale.service';
import { Invoice } from '../sale/schema/invoice.schema';
import {
  CreateReturnSaleDto,
  FindInvoiceDesktopDto,
  FindInvoiceDto,
  UpdateReturnSaleDto,
} from './dto';
import { ReturnSale, ReturnSaleDocument } from './schema/return-sale.schema';
import { TemplateDesignService } from '../template-design/template-design.service';
import * as salesTax from 'sales-tax';
import { templateTypeEnum } from '../template-design/dto/create-template-design.dto';
import { SoftDeleteModel } from '../../utils/schemas';
import { validateTransactions } from './return-sale-helper';
import { usersExceptionCode } from '../../exceptions/exception-code.users';
import { paginate } from '../../utils/dto/pagination.dto';
import { PrintOptionsDto } from '../sale/dto/print-options.dto';
import { Calculator } from '../../utils/calculator';
import { CompanyService } from '../company/company.service';
import { StoreService } from '../store/store.service';
import { ItemsService } from '../inventory-item/items.service';
import { PaymentTypesService } from '../payment-types/payment-types.service';
import { TLV } from '../../utils/qr-code';
import { BranchService } from '../branch/branch.service';
import { TenantServices } from '../tenants/tenant.service';

@Injectable({ scope: Scope.REQUEST, durable: true })
export class ReturnSaleService {
  constructor(
    @InjectModel(ReturnSale.name)
    private readonly returnSaleModel: SoftDeleteModel<ReturnSaleDocument>,
    @InjectModel('Sale') private readonly invoiceModel: Model<Invoice>,
    @Inject(forwardRef(() => SaleService))
    private readonly saleService: SaleService,
    private readonly templateDesignService: TemplateDesignService,
    private readonly companyService: CompanyService,
    private readonly storeService: StoreService,
    private readonly itemsService: ItemsService,
    private readonly paymentTypesService: PaymentTypesService,
    private readonly branchService: BranchService,
    private readonly tenantServices: TenantServices,
  ) {}

  async create(req: RequestWithUser, createReturnSaleDto: CreateReturnSaleDto) {
    const taxRate = (await salesTax.getSalesTax('SA')).rate;

    try {
      const invoiceExists = await this.returnSaleModel
        .findOne({
          invoice_no: createReturnSaleDto.invoice_no,
          branch_id: createReturnSaleDto.branch_id,
          invoice_type: createReturnSaleDto.invoice_type,
        })
        .exec();

      if (invoiceExists) {
        throw new HttpException(
          nameOf(usersExceptionCode, (exception) => exception.numberDuplicated),
          usersExceptionCode.numberDuplicated,
        );
      }

      const saleInvoice: any = await this.saleService.findOne(
        req.user?.code,
        String(createReturnSaleDto.sale_id),
      );

      if (!createReturnSaleDto.invoice_no) {
        const lastInvoiceNo =
          (
            await this.returnSaleModel
              .findOne({ branch_id: createReturnSaleDto.branch_id })
              .sort({ invoice_no: -1 })
              // eslint-disable-next-line @typescript-eslint/naming-convention
              .setOptions({ overrideSoftDelete: true })
          )?.invoice_no || 0;
        createReturnSaleDto.invoice_no = lastInvoiceNo + 1;
      }

      await validateTransactions(
        createReturnSaleDto.transactions,
        taxRate,
        saleInvoice,
        createReturnSaleDto.total_discounts,
        createReturnSaleDto.total_vat,
        createReturnSaleDto.invoice_total,
      );

      createReturnSaleDto.registered_customer = saleInvoice.registered_customer;
      createReturnSaleDto.free_tax = saleInvoice.free_tax;
      createReturnSaleDto.customer_name = saleInvoice.customer_name;
      createReturnSaleDto.customer_name_ar = saleInvoice.customer_name_ar;
      createReturnSaleDto.customer_id = saleInvoice.customer_id?.id;
      createReturnSaleDto.customer_phone = saleInvoice.customer_phone;
      createReturnSaleDto.tax_no = saleInvoice.tax_no;
      createReturnSaleDto.sale_number = saleInvoice.invoice_no;
      createReturnSaleDto.store = saleInvoice.store?._id;
      createReturnSaleDto.sales_representative = (
        saleInvoice.sales_representative as any
      )?.id;
      createReturnSaleDto.sale_total_discounts = saleInvoice.total_discounts;
      createReturnSaleDto.invoice_type = saleInvoice.invoice_type as any;

      const branch = await this.branchService.findByIds([
        String(createReturnSaleDto.branch_id),
      ]);

      createReturnSaleDto.qr_code = await new TLV(
        String(branch[0]?.general_information?.name?.en),
        String(branch[0]?.general_information?.tax_code),
        String(createReturnSaleDto.invoice_total),
        String(createReturnSaleDto.total_vat),
        new Date().toISOString(),
      )
        .toTlvB64()
        .toQrCode();

      const newInvoice = new this.returnSaleModel(createReturnSaleDto);
      const invoice = await newInvoice.save();
      // update saleInvoice return quantity
      await this.saleService.update(createReturnSaleDto.sale_id, {
        transactions: saleInvoice.transactions,
      });

      await invoice.save();
      let renderedTemplate = '';
      if (createReturnSaleDto.printOptions?.templateLang) {
        renderedTemplate = await this.createReturnInvoiceTemplate(
          req,
          invoice.id,
          createReturnSaleDto.printOptions.templateLang,
        );
        await this.sendTemplate(
          renderedTemplate,
          createReturnSaleDto.printOptions,
        );
      }
      return {
        template: createReturnSaleDto.printOptions?.print
          ? renderedTemplate
          : '',
        invoice,
      };
    } catch (err) {
      throw err;
    }
  }

  async findAll(
    req: RequestWithUser,
    findInvoiceDto: FindInvoiceDto,
    branch: Types.ObjectId,
  ) {
    const {
      limit,
      page,
      fromDate,
      toDate,
      queries,
      store,
      invoiceType,
      sortBy,
      sortType,
    } = findInvoiceDto;
    let search: any = { $and: [] };
    const sort = { [sortBy]: sortType };
    if (branch) {
      search.$and.push({
        branch_id: { $eq: branch },
      });
    }
    if (invoiceType) {
      search.$and.push({ invoice_type: { $eq: invoiceType } });
    }
    if (store) {
      search.$and.push({ store: { $eq: store } });
    }
    if (fromDate) {
      search.$and.push({
        createdAt: { $gte: new Date(fromDate), $lte: new Date(toDate) },
      });
    }
    if (queries) {
      const searchfield = { $regex: new RegExp(queries), $options: 'i' };
      search.$and.push({
        $or: [
          { invoice_no: +queries === 0 ? 0 : +queries || null },
          { customer_name: searchfield },
          { customer_name_ar: searchfield },
        ],
      });
    }
    if (search.$and.length === 0) {
      search = {};
    }
    const result = await this.returnSaleModel
      .find(search, null, { sort })
      .select({
        _id: 1,
        invoice_no: 1,
        invoice_type: 1,
        invoice_total: 1,
        invoice_date: 1,
        store: 1,
        registered_customer: 1,
        customer_name: 1,
        customer_name_ar: 1,
        customer_id: 1,
        createdAt: 1,
        sale_number: 1,
      })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate({
        path: 'customer_id',
        model: 'Partner',
        localField: 'customer_id',
        foreignField: 'id',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        justOne: true,
      })
      .lean();

    const meta = await paginate(limit, page, this.returnSaleModel, search);
    return { meta, result };
  }
  async findAllDesktop(
    findInvoiceDto: FindInvoiceDesktopDto,
    branch: Types.ObjectId,
  ) {
    const { fromDate, store } = findInvoiceDto;
    let search: any = { $and: [] };
    if (branch) {
      search.$and.push({
        branch_id: { $eq: branch },
      });
    }
    if (store) {
      search.$and.push({ store: { $eq: store } });
    }
    if (fromDate) {
      search.$and.push({
        createdAt: { $gte: new Date(fromDate) },
      });
    }
    if (search.$and.length === 0) {
      search = {};
    }
    const result = await this.returnSaleModel.find(search).lean();
    const updatedResult = result.map(({ payment_type, ...invoice }) => {
      const paymentTypeId = payment_type;
      const invoiceTotal = invoice.invoice_total;

      // eslint-disable-next-line @typescript-eslint/naming-convention
      const payment_types = paymentTypeId
        ? [
            {
              id: paymentTypeId,
              amount: invoiceTotal,
            },
          ]
        : undefined;

      return {
        ...invoice,
        ...(payment_types && { payment_types }),
      };
    });
    return { result: updatedResult };
  }

  async findOne(code: number, _id: string) {
    const invoice: any = await this.returnSaleModel
      .findOne({ _id })
      .populate({
        path: 'customer_id',
        model: 'Partner',
        localField: 'customer_id',
        foreignField: 'id',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        justOne: true,
      })
      .populate({
        path: 'sales_representative',
        model: 'SalesRepresentative',
        localField: 'sales_representative',
        foreignField: 'id',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        justOne: true,
      })
      .lean();

    if (!invoice) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.invoiceDoesNotExist,
        ),
        usersExceptionCode.invoiceDoesNotExist,
      );
    }
    const paymentType = await this.paymentTypesService.findOne({
      id: invoice.payment_type,
    });

    invoice.payment_type = paymentType;
    return invoice;
  }

  async findOne2(code: number, _id: string) {
    const invoice: any = await this.returnSaleModel
      .findOne({ _id })
      .populate({
        path: 'customer_id',
        model: 'Partner',
        localField: 'customer_id',
        foreignField: 'id',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        justOne: true,
      })
      .populate({
        path: 'sales_representative',
        model: 'SalesRepresentative',
        localField: 'sales_representative',
        foreignField: 'id',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        justOne: true,
      })
      .lean();
    if (!invoice) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.invoiceDoesNotExist,
        ),
        usersExceptionCode.invoiceDoesNotExist,
      );
    }
    invoice.sale_id = await this.saleService.findOne(
      code,
      String(invoice.sale_id),
    );

    const items = invoice.transactions.map((transaction) => transaction.item);

    const itemsData = await this.itemsService.findByIds(items, invoice.store);
    const retrievedStore = await this.storeService.findByIds([invoice.store]);

    invoice.store = retrievedStore[0];

    invoice.transactions.forEach((transaction) => {
      transaction.itemObject = itemsData.find(
        (item) => String(item.id) === String(transaction.item),
      );
    });

    invoice.tax_percentage = (await salesTax.getSalesTax('SA'))?.rate * 100;
    invoice.item_total = invoice.transactions.reduce(
      (sum, { qty }) => sum + qty,
      0,
    );
    return invoice;
  }

  async update(
    req: RequestWithUser,
    _id: string,
    updateReturnSaleDto: UpdateReturnSaleDto,
  ) {
    const returnSaleInvoice = await this.returnSaleModel.findById(_id);
    if (!returnSaleInvoice) {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.invoiceDoesNotExist,
        ),
        usersExceptionCode.invoiceDoesNotExist,
      );
    }

    const taxRate = (await salesTax.getSalesTax('SA')).rate;

    try {
      const saleInvoice: any = await this.saleService.findOne(
        req.user?.code,
        String(returnSaleInvoice.sale_id),
      );
      await validateTransactions(
        updateReturnSaleDto.transactions,
        taxRate,
        saleInvoice,
        updateReturnSaleDto.total_discounts,
        updateReturnSaleDto.total_vat,
        updateReturnSaleDto.invoice_total,
        true,
        returnSaleInvoice,
      );
      extend(returnSaleInvoice, updateReturnSaleDto);
      (returnSaleInvoice.transactions as any) =
        updateReturnSaleDto.transactions;
      await returnSaleInvoice.save();

      await this.saleService.update(returnSaleInvoice.sale_id, {
        transactions: saleInvoice.transactions,
      });

      await returnSaleInvoice.save();
      return returnSaleInvoice;
    } catch (err) {
      throw err;
    }
  }

  async delete(req: RequestWithUser, _id: string) {
    const returnSaleInvoice = await this.returnSaleModel.findById(_id);
    returnSaleInvoice.branch_id = new Types.ObjectId(
      returnSaleInvoice.branch_id,
    );
    returnSaleInvoice.store = new Types.ObjectId(returnSaleInvoice.store);
    try {
      // 1.softDelete ERPDocument
      const invoice = await this.returnSaleModel.findById(_id).exec();
      await invoice.softDelete();

      // update saleInvoice return quantity
      await this.saleService.rollbackReturnedQuantity(
        returnSaleInvoice.sale_id.toString(),
        returnSaleInvoice.transactions,
      );
    } catch (err) {
      throw err;
    }

    return returnSaleInvoice;
  }

  async remove(_id: string) {
    return await this.returnSaleModel.findOneAndDelete({ _id }).catch(() => {
      throw new HttpException(
        nameOf(
          usersExceptionCode,
          (exceptions) => exceptions.invoiceDoesNotExist,
        ),
        usersExceptionCode.invoiceDoesNotExist,
      );
    });
  }

  async sendTemplate(renderedTemplate: string, sendOptions: PrintOptionsDto) {
    if (sendOptions.sendEmail) {
    }
    if (sendOptions.sendSms) {
      // send template via sms
    }
    return true;
  }

  async printOne(req: RequestWithUser, getTemplateDto: GetTemplateDto) {
    const { invoiceId, ...sendOptions } = getTemplateDto;
    const template = await this.createReturnInvoiceTemplate(
      req,
      invoiceId,
      sendOptions.templateLang,
    );
    await this.sendTemplate(template, sendOptions);
    return { template: sendOptions.print ? template : null };
  }

  async createReturnInvoiceTemplate(req, invoiceId, lang = 'en'): Promise<any> {
    const companyInfo = await this.companyService.get();
    const tenant = await this.tenantServices.getTenantData(req.user?.code);

    const templateData: any = await this.findOne2(req.user?.code, invoiceId);
    templateData.branch = {
      general_information: {
        tax_code:
          templateData.sale_id.store.branch.general_information.tax_code,
      },
    };
    templateData.sale_id.store.branch = undefined;
    templateData.reminder = Calculator.subtract(
      templateData.sale_id.invoice_total,
      templateData.sale_id.paid,
    )
      .round()
      .number();
    templateData.due_date = templateData.sale_id.due_date;
    templateData.company_info = companyInfo;
    templateData.company_info.store_name = templateData?.store?.name;
    templateData.customer_name = templateData?.customer_id?.name || {
      ar: templateData.customer_name,
      en: templateData.customer_name,
    };
    templateData.company_info.logo = tenant?.logo || '';
    templateData.invoice_date = new Date(templateData.createdAt).toISOString();
    delete templateData.sale_id;
    delete templateData.accounting_journal;
    templateData.total_item_qty = 0;
    templateData.total_subtotal = 0;
    templateData.transactions.forEach((transaction) => {
      transaction.code = transaction.itemObject.code;
      templateData.total_item_qty = Calculator.sum(
        templateData.total_item_qty,
        transaction.qty,
      )
        .round()
        .number();
      templateData.total_subtotal = Calculator.sum(
        templateData.total_subtotal,
        transaction.subtotal,
      )
        .round()
        .number();
    });
    templateData.tax_rate = templateData.tax_percentage;
    return await this.templateDesignService.renderTemplateByType(
      templateTypeEnum.sales_return,
      {
        data: templateData,
      },
      lang,
    );
  }

  async salesRelatedInvocies(sale_id: string) {
    return this.returnSaleModel.countDocuments({ sale_id }).exec();
  }
}
