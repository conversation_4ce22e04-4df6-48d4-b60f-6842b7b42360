import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { invoiceType } from '../dto/enums';
import {
  TransactionItem,
  transactionItemSchema,
} from './transaction-items.schema';
import { SoftDeleteDocument, softDeletePlugin } from '../../../utils/schemas';
import { Store } from '../../store/schema/store.schema';

export type ReturnSaleDocument = ReturnSale & SoftDeleteDocument & Document;

@Schema({ timestamps: true })
export class ReturnSale {
  @Prop({ required: true })
  invoice_no: number;

  @Prop({ required: true, enum: invoiceType })
  invoice_type: invoiceType;

  @Prop()
  payment_type: string;

  @Prop({
    required: true,
  })
  registered_customer: boolean;

  @Prop({ default: false })
  free_tax: boolean;

  @Prop()
  customer_name: string;

  @Prop({ type: Date })
  invoice_date: Date;

  @Prop()
  customer_name_ar: string;

  @Prop({
    type: String,
    ref: 'Partner',
    required: false,
  })
  customer_id?: string;

  @Prop()
  customer_phone: string;

  @Prop({
    type: Types.ObjectId,
    ref: 'Sale',
    required: true,
  })
  sale_id: Types.ObjectId;

  @Prop({ type: Number, default: 0 })
  sale_number;

  @Prop({ type: String })
  tax_no: string;

  @Prop({
    type: Types.ObjectId,
    ref: Store.name,
    required: true,
  })
  store: Types.ObjectId;

  @Prop({
    type: String,
    ref: 'SalesRepresentative',
    required: false,
  })
  sales_representative?: string;

  @Prop()
  note: string;

  @Prop()
  total_discounts: number;

  @Prop()
  total_vat: number;

  @Prop({ required: true })
  invoice_total: number;

  @Prop()
  qr_code: string;

  @Prop({ required: true })
  branch_id: Types.ObjectId;

  @Prop({ type: [transactionItemSchema] })
  transactions: TransactionItem[];
}

export const returnSaleSchema = SchemaFactory.createForClass(ReturnSale);
returnSaleSchema.index({ branch_id: 1, invoice_no: 1 }, { unique: true });

returnSaleSchema.plugin(softDeletePlugin);
