import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { itemType } from '../../sale/dto';
import { Types } from 'mongoose';

@Schema()
export class TransactionItem {
  @Prop({ required: true, type: Types.ObjectId })
  transaction_reference: string;

  @Prop({ type: String })
  item: string;

  @Prop({ type: String, required: true })
  item_invoice_name: string;

  @Prop({ required: true, enum: itemType })
  item_type: itemType;

  @Prop({ type: String, required: false })
  description: string;

  @Prop({
    required: true,
  })
  unit: string;

  @Prop({ default: false })
  free_tax: boolean;

  @Prop({ type: Number, required: true })
  qty: number;

  @Prop({ required: true, type: Number, default: 0 })
  price: number;

  @Prop({ required: true, type: Number, default: 0 })
  subtotal: number;

  @Prop({ type: Number, default: 0 })
  discount: number;

  @Prop({ required: true, type: Number, default: 0 })
  vat: number;

  @Prop({ required: true, type: Number, default: 0 })
  total: number;

  @Prop({ type: Number, default: 0 })
  row_invoice_discount: number;

  @Prop({ type: Number, default: 0 })
  row_invoice_discount_vat: number;

  @Prop({ type: Number, default: 0 })
  average_cost: number;
}

export const transactionItemSchema =
  SchemaFactory.createForClass(TransactionItem);
