import { join } from 'path';
import mongoose, { Model } from 'mongoose';
import { ServiceVersion, serviceVersionSchema } from './schema/version.schema';
import { commonSchemaVersion, schemaVersion } from './database_scripts/version';
import { OnApplicationBootstrap } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Tenant } from '../tenants/schemas';
import { InjectModel } from '@nestjs/mongoose';

// since we talked about it and we wanted to use it as counter so i changed the localeCompare too
const version = +schemaVersion.version;
const commonVersion = +commonSchemaVersion.version;
const maxRetry = 5;
let retry = 0;

export class ScriptRunnerService implements OnApplicationBootstrap {
  constructor(
    @InjectModel(Tenant.name, 'commonDb')
    private readonly tenantModel: Model<Tenant>,
    @InjectModel(ServiceVersion.name, 'commonDb')
    private readonly serviceVersionModel: Model<ServiceVersion>,
    private readonly configService: ConfigService,
  ) {}

  public async executeDatabaseScripts(tenantId: number) {
    try {
      const dbConnection = await this.tenantDbConnection(tenantId);
      const currnetDbVersion = await this.checkVersionWithDatabase(
        dbConnection,
        tenantId,
      );
      if ((currnetDbVersion || 0) >= version) {
        return;
      }
      for (let index = currnetDbVersion + 1; index <= version; index++) {
        // the reason why we pass one by one is migration should run in order
        const execPromise = await this.loadMigrations(
          dbConnection,
          index,
          false,
        );
        await this.executeScripts(execPromise);
        await this.updateDatabaseVersion(dbConnection, index);
      }

      console.log(`All scripts for teannt ${tenantId} has been executed...`);
    } catch (e) {
      console.log(e);
      throw e;
    }
  }

  async executeForAll() {
    try {
      const codes = await this.getAllTenants();
      for (const code of codes) {
        await this.executeDatabaseScripts(code);
      }
    } catch (error) {
      console.log(error);
      if (retry < maxRetry) {
        retry++;
        await this.executeForAll();
      } else {
        throw error;
      }
    }
  }

  async executeCommonDB() {
    const currnetDbVersion =
      (await this.serviceVersionModel.findOne())?.version || 0;
    if (currnetDbVersion >= commonVersion) {
      return;
    }
    const dbConnection = await this.commonDbConnection();
    for (let index = currnetDbVersion + 1; index <= commonVersion; index++) {
      const execPromise = await this.loadMigrations(dbConnection, index, true);
      await this.executeScripts(execPromise);
      await this.updateDatabaseVersion(dbConnection, index);
    }
  }

  async commonDbConnection() {
    const dbName = this.configService.get<string>('COMMON_DB');
    const mongoDbUrl = new URL(
      `${this.configService.get('PREFIX_DB') || ''}${dbName}`,
      this.configService.get('DB_URL'),
    );
    mongoDbUrl.searchParams.set('authSource', 'admin');
    return mongoose.createConnection(mongoDbUrl.href);
  }

  async tenantDbConnection(tenantId: number) {
    const dbUrl = new URL(
      (this.configService.get('PREFIX_DB') || '') + 'tenant_' + tenantId,
      this.configService.get('DB_URL'),
    );
    dbUrl.searchParams.set('authSource', 'admin');
    return mongoose.createConnection(dbUrl.href);
  }

  async onApplicationBootstrap() {
    if (
      !(JSON.parse(this.configService.get('RUN_MIGRATIONS') || null) === false)
    ) {
      await this.executeCommonDB();
      await this.executeForAll();
    }
  }

  /**
   * Executing script files
   * @param execPromise List of the script promises
   * @private
   */
  private async executeScripts(execPromise: any) {
    await Promise.all(execPromise);
  }

  async getAllTenants(): Promise<number[]> {
    const tenantCodes = await this.tenantModel.find().lean();
    const tenants = tenantCodes.map((tenant) => tenant.code);
    return tenants;
  }

  async loadMigrations(
    db: mongoose.Connection,
    projectVersion: number,
    common: boolean,
  ) {
    try {
      const migrationsPath = join(
        __dirname,
        'database_scripts',
        String(projectVersion),
        'index.js',
      );
      const migrationsModule = await import(migrationsPath);

      if (typeof migrationsModule.migrations === 'function') {
        return await migrationsModule.migrations(db, common);
      } else {
        console.error(`Migrations function not found in ${migrationsPath}`);
      }
    } catch (error) {
      console.error(`Error loading migrations for version ${version}:`, error);
      throw error;
    }
  }

  async checkVersionWithDatabase(db: mongoose.Connection, tenantId: number) {
    const serviceVersionModel = db.model(
      'serviceversion',
      serviceVersionSchema,
    );
    const dbVersion = await serviceVersionModel.findOne();

    if ((dbVersion?.version || 0) >= version) {
      console.log(
        `database version is(${dbVersion.version}) the same or upper than app version(${version}) for tenant: ${tenantId}`,
      );
    }
    return dbVersion?.version || 0;
  }

  async updateDatabaseVersion(db: mongoose.Connection, projectVersion: number) {
    const serviceVersionModel = db.model(
      'serviceversion',
      serviceVersionSchema,
    );
    await serviceVersionModel.updateOne(
      {},
      { version: projectVersion },
      { upsert: true },
    );
  }

  async updateServiceVersion(tenantNumber: number) {
    // connect to db
    const dbConnection = await this.tenantDbConnection(tenantNumber);
    const serviceVersionModel = dbConnection.model(
      'serviceversion',
      serviceVersionSchema,
    );

    await serviceVersionModel.updateOne(
      {},
      { version: schemaVersion.version },
      { upsert: true },
    );
  }
}
