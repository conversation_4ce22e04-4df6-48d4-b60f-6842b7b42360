import { Modu<PERSON> } from '@nestjs/common';
import { ScriptRunnerService } from './script-runner.service';
import { MongooseModule } from '@nestjs/mongoose';
import { ServiceVersion, serviceVersionSchema } from './schema/version.schema';
import { Tenant } from '../tenants/schemas';
import { tenantSchema } from './schema/tenants.schema';
import { ScriptRunnerController } from './script-runner.controller';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ServiceVersion.name, schema: serviceVersionSchema },
    ]),
    MongooseModule.forFeature(
      [
        { name: Tenant.name, schema: tenantSchema },
        { name: ServiceVersion.name, schema: serviceVersionSchema },
      ],
      'commonDb',
    ),
  ],
  controllers: [ScriptRunnerController],
  providers: [ScriptRunnerService],
})
export class ScriptRunnerModule {}
