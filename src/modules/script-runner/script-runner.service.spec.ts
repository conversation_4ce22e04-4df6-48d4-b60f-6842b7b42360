import { Test, TestingModule } from '@nestjs/testing';
import { ScriptRunnerService } from './script-runner.service';
import { ConfigService } from '@nestjs/config';
import { getModelToken } from '@nestjs/mongoose';
import { Tenant } from '../tenants/schemas';
import { ServiceVersion } from './schema/version.schema';

describe('ScriptRunnerService', () => {
  let service: ScriptRunnerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScriptRunnerService,
        {
          provide: ConfigService,
          useValue: {},
        },
        {
          provide: getModelToken(Tenant.name, 'commonDb'),
          useValue: {},
        },
        {
          provide: getModelToken(ServiceVersion.name, 'commonDb'),
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<ScriptRunnerService>(ScriptRunnerService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
