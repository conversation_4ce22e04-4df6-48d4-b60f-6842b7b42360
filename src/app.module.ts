import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from './modules/auth/auth.module';
import { DatabaseModule } from './modules/database/database.module';
import { MongooseConfigService } from './modules/database/mongoose.service';
import { UsersModule } from './modules/users/users.module';
import { RolesModule } from './modules/roles/roles.module';
import { TenantModule } from './modules/tenants/tenant.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { BranchModule } from './modules/branch/branch.module';
import AppConfig from './config/app.config';
import { WinstonModule } from 'nest-winston';
import { WinstonConfigAsync } from './config/winston.config';
import { APP_FILTER } from '@nestjs/core';
import { validate } from './env.validation';
import { CaslModule } from './modules/casl/casl.module';
import { PagerMiddleware } from './middleware/pager.middleware';
import { HealthModule } from './modules/health/health.module';
import { CompanyModule } from './modules/company/company.module';
import { PaymentTypesModule } from './modules/payment-types/payment-types.module';
import { FlexibleExceptionFilter } from './filter/flexible-exception.filter';
import { PermissionsModule } from './modules/permissions/permissions.module';
import { UserBranchesRolesModule } from './modules/user-branches-roles/user-branch-role.module';
import { ExceptionsModule } from './exceptions/exceptions.module';
import { ScriptRunnerModule } from './modules/script-runner/script-runner.module';
import { CommonDbConfigService } from './modules/database/common-db-config.service';
import { DatabaseService } from './modules/database/database.service';
import { StoreModule } from './modules/store/store.module';
import { GroupItemModule } from './modules/group-item/group-item.module';
import { ItemsModule } from './modules/inventory-item/items.module';
import { TemplateDesignModule } from './modules/template-design/template-design.module';
import { SaleModule } from './modules/sale/sale.module';
import { ReturnSaleModule } from './modules/return-sale/return-sale.module';
import { PartnerModule } from './modules/partners/partner.module';
import { SalesRepresentativeModule } from './modules/sales-representative/sales-representative.module';
import { UsersProfileModule } from './modules/users-profile/users-profile.module';
import { UnitModule } from './modules/unit/unit.module';
import { ApiTokenModule } from './modules/api-token/api-token.module';
import { ItemExplorerModule } from './modules/item-explorer/item-explorer.module';
import { MinioClientModule } from './modules/minio/minio-client.module';
import { BundleModule } from './modules/bundle/bundle.module';

@Module({
  imports: [
    MongooseModule.forRootAsync({
      useClass: MongooseConfigService,
    }),
    MongooseModule.forRootAsync({
      useClass: CommonDbConfigService,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      connectionName: 'commonDb',
    }),
    AuthModule,
    TenantModule,
    BranchModule,
    UsersModule,
    DatabaseModule,
    MongooseModule.forRootAsync({
      imports: [ConfigModule, DatabaseModule],
      inject: [ConfigService, DatabaseService],
      useFactory: async (
        configService: ConfigService,
        databaseService: DatabaseService,
      ) => {
        const mongoUrl = await databaseService.createCommonDbUrl();
        return {
          uri: mongoUrl,
        };
      },
      // eslint-disable-next-line @typescript-eslint/naming-convention
      connectionName: 'common',
    }),
    RolesModule,
    CaslModule,
    CompanyModule,
    ConfigModule.forRoot({ validate, envFilePath: '.env', isGlobal: true }),
    HealthModule,
    PaymentTypesModule,
    WinstonModule.forRoot(WinstonConfigAsync),
    PermissionsModule,
    UserBranchesRolesModule,
    ScriptRunnerModule,
    ExceptionsModule,
    StoreModule,
    GroupItemModule,
    ItemsModule,
    TemplateDesignModule,
    SaleModule,
    ReturnSaleModule,
    PartnerModule,
    SalesRepresentativeModule,
    UsersProfileModule,
    UnitModule,
    ApiTokenModule,
    ItemExplorerModule,
    MinioClientModule,
    BundleModule,
  ],
  providers: [
    AppConfig,
    { provide: APP_FILTER, useClass: FlexibleExceptionFilter },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(PagerMiddleware)
      .forRoutes({ path: '*', method: RequestMethod.GET });
  }
}

@Module({})
export class DynamicDatabaseModule {
  static forRoot(request: Request) {
    if (request['bypassDbConnection']) {
      return {
        module: DynamicDatabaseModule,
      };
    }
    return {
      module: DynamicDatabaseModule,
      imports: [
        MongooseModule.forRootAsync({
          useClass: MongooseConfigService,
        }),
      ],
    };
  }
}
