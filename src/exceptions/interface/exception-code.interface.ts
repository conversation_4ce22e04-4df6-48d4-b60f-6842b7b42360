import { HttpStatusCode } from 'axios';

export interface UsersExceptionCode {
  invalidSearchQuery: number;
  incorrectCredentials: HttpStatusCode;
  emailExist: HttpStatusCode;
  phoneExist: HttpStatusCode;
  invalidOtp: HttpStatusCode;
  branchNotFound: HttpStatusCode;
  cannotDeleteBranch: HttpStatusCode;
  roleAlreadyExist: HttpStatusCode;
  roleNotFound: HttpStatusCode;
  permNotFound: HttpStatusCode;
  tenantNotFound: HttpStatusCode;
  accountNotFound: HttpStatusCode;
  userNotFound: HttpStatusCode;
  tenantDisabled: HttpStatusCode;
  companyNotFound: HttpStatusCode;
  otpSend: HttpStatusCode;
  otpInvalid: HttpStatusCode;
  otpExpired: HttpStatusCode;
  internalServerError: HttpStatusCode;
  numberDuplicated: HttpStatusCode;
  accountingNodeTypeCashierOrBank: HttpStatusCode;
  paymentTypeNotFound: HttpStatusCode;
  accountingNodeNotFound: HttpStatusCode;
  zatcaError: HttpStatusCode;
  csrAlreadyOnboarded: HttpStatusCode;
  itemGroupNotFound: HttpStatusCode;
  unitNotFound: HttpStatusCode;
  duplicatedCode: HttpStatusCode;
  itemNotFound: HttpStatusCode;
  invalidItemGroup: HttpStatusCode;
  customerGroupNotFound: HttpStatusCode;
  expenseNotFound: HttpStatusCode;
  vendorGroupNotFound: HttpStatusCode;
  salesRepresentativeNotFound: HttpStatusCode;
  customerDoesNotExist: HttpStatusCode;
  vendorDoesNotExist: HttpStatusCode;
  invoiceDoesNotExist: HttpStatusCode;
  customerIdRequiredIfRegisterCustomerTrue: HttpStatusCode;
  customerIsCashOnly: HttpStatusCode;
  transactionDoesNotHaveItems: HttpStatusCode;
  itemDoesNotHaveUnit: HttpStatusCode;
  branchDoesNotExist: HttpStatusCode;
  documentCantDelete: HttpStatusCode;
  journalSnapshotDoesNotExist: HttpStatusCode;
  itemQuantityNotValid: HttpStatusCode;
  invalidSubtotal: HttpStatusCode;
  freeTaxVat: HttpStatusCode;
  invalidVat: HttpStatusCode;
  invalidTotalWithVat: HttpStatusCode;
  rowInvoiceDiscountNotValid: HttpStatusCode;
  rowInvoiceDiscountVatNotValid: HttpStatusCode;
  totalDiscountNotValid: HttpStatusCode;
  invoiceDiscountVatNotValid: HttpStatusCode;
  totalVatNotValid: HttpStatusCode;
  templateDoesNotExist: HttpStatusCode;
  salesInvoiceNotFound: HttpStatusCode;
  invoiceTypeNotValid: HttpStatusCode;
  invalidDiscount: HttpStatusCode;
  finalPriceIsNotValid: HttpStatusCode;
  storeDoesNotExist: HttpStatusCode;
  bundleItemTotalWrong: HttpStatusCode;
  bundleOriginalPriceWrong: HttpStatusCode;
  bundleDiscountPercentageWrong: HttpStatusCode;
}
