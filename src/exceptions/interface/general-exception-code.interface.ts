import { HttpStatusCode } from 'axios';

export interface GeneralExceptionCode {
  unauthorized: HttpStatusCode;
  internalServerError: HttpStatusCode;
  badRequest: HttpStatusCode;
  forbidden: HttpStatusCode;
  notFound: HttpStatusCode;
  conflict: HttpStatusCode;
  tooManyRequest: HttpStatusCode;
  settingsNotFound: HttpStatusCode;
  branchNotFound: HttpStatusCode;
  incorrectCredentials: HttpStatusCode;
}
