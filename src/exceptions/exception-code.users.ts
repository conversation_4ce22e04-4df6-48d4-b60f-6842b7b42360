import { HttpStatusCode } from 'axios';
import { UsersExceptionCode } from './interface/exception-code.interface';

/* eslint-disable */
export const usersExceptionCode: UsersExceptionCode = {
  incorrectCredentials: HttpStatusCode.Unauthorized,
  emailExist: HttpStatusCode.Conflict,
  phoneExist: HttpStatusCode.Conflict,
  invalidOtp: HttpStatusCode.Forbidden,
  branchNotFound: HttpStatusCode.NotFound,
  cannotDeleteBranch: HttpStatusCode.Forbidden,
  roleAlreadyExist: HttpStatusCode.Conflict,
  roleNotFound: HttpStatusCode.NotFound,
  permNotFound: HttpStatusCode.NotFound,
  tenantNotFound: HttpStatusCode.NotFound,
  accountNotFound: HttpStatusCode.NotFound,
  userNotFound: HttpStatusCode.NotFound,
  tenantDisabled: HttpStatusCode.NotFound,
  companyNotFound: HttpStatusCode.NotFound,
  otpSend: HttpStatusCode.Forbidden,
  otpInvalid: HttpStatusCode.Forbidden,
  otpExpired: HttpStatusCode.Forbidden,
  internalServerError: HttpStatusCode.InternalServerError,
  numberDuplicated: HttpStatusCode.BadRequest,
  accountingNodeTypeCashierOrBank: HttpStatusCode.Forbidden,
  paymentTypeNotFound: HttpStatusCode.NotFound,
  accountingNodeNotFound: HttpStatusCode.NotFound,
  zatcaError: HttpStatusCode.Forbidden,
  csrAlreadyOnboarded: HttpStatusCode.Forbidden,
  itemGroupNotFound: HttpStatusCode.NotFound,
  unitNotFound: HttpStatusCode.NotFound,
  duplicatedCode: HttpStatusCode.Forbidden,
  itemNotFound: HttpStatusCode.NotFound,
  invalidItemGroup: HttpStatusCode.NotFound,
  customerGroupNotFound: HttpStatusCode.NotFound,
  expenseNotFound: HttpStatusCode.NotFound,
  vendorGroupNotFound: HttpStatusCode.NotFound,
  salesRepresentativeNotFound: HttpStatusCode.NotFound,
  customerDoesNotExist: HttpStatusCode.NotFound,
  vendorDoesNotExist: HttpStatusCode.NotFound,
  invoiceDoesNotExist: HttpStatusCode.NotFound,
  customerIdRequiredIfRegisterCustomerTrue: HttpStatusCode.Forbidden,
  customerIsCashOnly: HttpStatusCode.Forbidden,
  transactionDoesNotHaveItems: HttpStatusCode.Forbidden,
  itemDoesNotHaveUnit: HttpStatusCode.Forbidden,
  branchDoesNotExist: HttpStatusCode.NotFound,
  documentCantDelete: HttpStatusCode.Forbidden,
  journalSnapshotDoesNotExist: HttpStatusCode.NotFound,
  itemQuantityNotValid: HttpStatusCode.Forbidden,
  invalidSubtotal: HttpStatusCode.Forbidden,
  freeTaxVat: HttpStatusCode.Forbidden,
  invalidVat: HttpStatusCode.Forbidden,
  invalidTotalWithVat: HttpStatusCode.Forbidden,
  rowInvoiceDiscountNotValid: HttpStatusCode.Forbidden,
  rowInvoiceDiscountVatNotValid: HttpStatusCode.Forbidden,
  totalDiscountNotValid: HttpStatusCode.Forbidden,
  invoiceDiscountVatNotValid: HttpStatusCode.Forbidden,
  totalVatNotValid: HttpStatusCode.Forbidden,
  templateDoesNotExist: HttpStatusCode.NotFound,
  salesInvoiceNotFound: HttpStatusCode.NotFound,
  invoiceTypeNotValid: HttpStatusCode.Forbidden,
  invalidDiscount: HttpStatusCode.Forbidden,
  finalPriceIsNotValid: HttpStatusCode.Forbidden,
  invalidSearchQuery: HttpStatusCode.BadRequest,
  storeDoesNotExist: HttpStatusCode.NotFound,
  bundleItemTotalWrong: HttpStatusCode.UnprocessableEntity,
  bundleOriginalPriceWrong: HttpStatusCode.UnprocessableEntity,
  bundleDiscountPercentageWrong: HttpStatusCode.UnprocessableEntity,
};
