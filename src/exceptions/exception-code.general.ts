import { HttpStatusCode } from 'axios';
import { GeneralExceptionCode } from './interface/general-exception-code.interface';

export const generalExceptionCode: GeneralExceptionCode = {
  unauthorized: HttpStatusCode.Unauthorized,
  internalServerError: HttpStatusCode.InternalServerError,
  badRequest: HttpStatusCode.BadRequest,
  forbidden: HttpStatusCode.Forbidden,
  notFound: HttpStatusCode.NotFound,
  conflict: HttpStatusCode.Conflict,
  tooManyRequest: HttpStatusCode.TooManyRequests,
  settingsNotFound: HttpStatusCode.NotFound,
  branchNotFound: HttpStatusCode.NotFound,
  incorrectCredentials: HttpStatusCode.Unauthorized,
};
