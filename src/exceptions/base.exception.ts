import { ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { InternalCode } from './internal-code.mapping';

export type CustomReply = {
  message: string;
  internalCode: number;
  statusCode: number;
  data: string | Record<string, any> | null | any;
};

export class BaseException extends HttpException {
  constructor(
    message: string,
    status: HttpStatus,
    internalCode: InternalCode,
    additionalData: string | Record<string, any> | any = null,
  ) {
    super(
      {
        message,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        internalCode,
        statusCode: status,
        data: additionalData,
      },
      status,
    );
  }
  catch(exception: Error, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();

    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    response.status(status).json({
      statusCode: status,
      message: exception.message,
      path: request.url,
    });
  }
}
