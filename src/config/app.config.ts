import {
  HttpException,
  Injectable,
  LogLevel,
  ValidationPipeOptions,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ValidationError } from 'class-validator';
import { nameOf } from '../utils/object-key-name';
import { generalExceptionCode } from '../exceptions/exception-code.general';

@Injectable()
export default class AppConfig {
  constructor(private configService: ConfigService) {}

  get name(): string {
    return this.configService.get<string>('APP_NAME') || 'Nest App';
  }

  get logLevel(): LogLevel[] {
    return this.configService.get<LogLevel[]>('LOG_LEVEL');
  }

  get port(): number {
    return this.configService.get<number>('PORT') || 3000;
  }

  get IsDebugMode(): boolean {
    return this.configService.get<boolean>('APP_DEBUG') || true;
  }

  get env(): string {
    return this.configService.get<string>('NODE_ENV') || 'dev';
  }

  get dbUrl(): string {
    return this.configService.get<string>('DB_URL');
  }

  get prefixDB(): string {
    return this.configService.get<string>('PREFIX_DB');
  }

  get baseUrl(): string[] {
    return this.configService.get<string[]>('BASE_URL');
  }

  get accessTokenExpire(): number {
    return this.configService.get<number>('ACCESS_TOKEN_EXPIRE');
  }

  get refreshTokenExpire(): number {
    return this.configService.get<number>('REFRESH_TOKEN_EXPIRE');
  }

  get appUrlPrefix(): string {
    return this.configService.get<string>('APP_URL_PREFIX');
  }
}

export const validationConfig: ValidationPipeOptions = {
  whitelist: true,
  transform: true,
  // eslint-disable-next-line @typescript-eslint/naming-convention
  validateCustomDecorators: true,
  skipNullProperties: false,
  skipUndefinedProperties: false,
  validationError: { target: false, value: false },
  stopAtFirstError: false,
  forbidUnknownValues: false,
  exceptionFactory(validationErrors: ValidationError[] = []) {
    throw new HttpException(
      {
        exceptionCode: nameOf(generalExceptionCode, (x) => x.badRequest),
        response: validationErrors, //JSON.stringify(validationErrors, null, 2),
      },
      generalExceptionCode.badRequest,
    );
  },
};
