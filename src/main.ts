import * as cookieParser from 'cookie-parser';
import * as dotenv from 'dotenv';
import { LogLevel, ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ContextIdFactory, NestFactory, Reflector } from '@nestjs/core';
import AppConfig, { validationConfig } from './config/app.config';
import { AppModule } from './app.module';
import { JwtAuthGuard } from './modules/auth/guards/jwt-auth.guard';
import { join } from 'path';
import { Logger } from '@nestjs/common';
import { AggregateByTenantContextIdStrategy } from './modules/auth/strategies/aggregate-by-tenant-context-id.strategy';
import { CombinedAuthGuard } from './modules/auth/guards/combined.guard';
import { ApiKeyAuthGuard } from './modules/auth/guards/api-token.guard';

dotenv.config();
async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  const appConfig: AppConfig = app.get(AppConfig);
  const logLevels: LogLevel[] = appConfig.logLevel;

  Logger.overrideLogger(logLevels);

  const reflector = app.get(Reflector);
  ContextIdFactory.apply(new AggregateByTenantContextIdStrategy());

  app.useGlobalPipes(new ValidationPipe(validationConfig));
  const swaggerPrefix = process.env.APP_URL_PREFIX || '/';
  app.useGlobalGuards(
    new CombinedAuthGuard(
      reflector,
      new JwtAuthGuard(reflector),
      new ApiKeyAuthGuard(reflector),
    ),
  );
  app.setGlobalPrefix(swaggerPrefix);
  app.enableCors({
    origin: appConfig.baseUrl,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: 'Content-Type, Accept, Origin, Branch',
    credentials: true,
  });
  app.use(cookieParser());

  const config = new DocumentBuilder()
    .addBearerAuth(
      // eslint-disable-next-line @typescript-eslint/naming-convention
      { type: 'http', scheme: 'bearer', bearerFormat: 'JWT' },
      'TOKEN',
    )
    .build();
  const document = SwaggerModule.createDocument(app, config, {
    ignoreGlobalPrefix: false,
  });

  SwaggerModule.setup(join(swaggerPrefix, 'docs'), app, document);
  await app.listen(appConfig.port);
  Logger.verbose(`Application is running on: ${await app.getUrl()}`);
}
bootstrap();
