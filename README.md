# Development Environment Setup Guide for Backend 

## Prerequisites
1. **Install Docker**  
   Refer to the [Docker Installation Guide](https://docs.docker.com/get-docker/).

---

## Clone Repositories
```bash
mkdir osuscloud && cd osuscloud
git clone https://dev.osusgroup.com/osuscloud/service-users
git clone https://dev.osusgroup.com/osuscloud/service-accounting
git clone https://dev.osusgroup.com/osuscloud/service-inventory
git clone https://dev.osusgroup.com/osuscloud/service-trade
```

## Run Docker Compose
```bash
cd users-service
docker-compose up
```

## Prepare Environment Variables
```bash
cd users-service && cp env.example .env
cd ../accounting-service && cp env.example .env
cd ../inventory-service && cp env.example .env
cd ../trade-service && cp env.example .env
```
## Seed the database
```bash
cd users-service && npm run seed:dev
cd ../accounting-invnetory && npm run seed:dev
## the accounting service doesn't have a seed script yet
## you can login using swagger then run accounting api seed endpoint or use front end to seed the accounting the database
```

## Install Dependencies 
```bash
cd users-service && npm i 
cd ../accounting-service && npm i
cd ../inventory-service && npm i
cd ../trade-service && npm i
```
## Run the services
Open a new terminal for each service.
```bash
cd users-service && npm run start:dev
cd ../accounting-service && npm run start:dev
cd ../inventory-service && npm run start:dev
cd ../trade-service && npm run start:dev
# Optionally run the services in the background (logs won't be directly accessible):
# cd users-service && npm i && npm run start:dev &
# To put the job in the background later, hit 'Ctrl+Z' and run 'bg'
# To bring the job back to the foreground, run 'fg'
```

## Note: Nginx Configuration
- Validate `service-user/nginx.conf` and `.env` ports match.

## Accessing Services
Access each service at `localhost:3000/<service-name>/s`.

- Users Swagger: `http://localhost:3000/users/docs`
- Accounting Swagger: `http://localhost:3000/accounting/docs`
- Inventory Swagger: `http://localhost:3000/inventory/docs`
- Trade Swagger: `http://localhost:3000/trade/docs`
- RabbitMQ Management Console: `http://localhost:3010/`
- Minio S3 Console: `http://localhost:3011/`
- MongoExpress: `http://localhost:3012/`