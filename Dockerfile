# Build the applicaiton
FROM dev.osusgroup.com:443/osuscloud/dependency_proxy/containers/node:20 AS builder
WORKDIR /build-stage
COPY package*.json ./
RUN npm ci 
COPY . ./
RUN npm run build

# Install Producation depeandncy 
FROM dev.osusgroup.com:443/osuscloud/dependency_proxy/containers/node:20 AS production-depandancy
WORKDIR /dependancy-stage
COPY package*.json .
RUN npm ci --omit=dev

# Prepare production image
FROM dev.osusgroup.com:443/osuscloud/dependency_proxy/containers/node:20-slim AS production 
WORKDIR /app
COPY --from=production-depandancy --chown=node:node /dependancy-stage/node_modules ./node_modules 
COPY --from=builder --chown=node:node /build-stage/dist ./dist
USER  node 
ENTRYPOINT ["node", "dist/main.js"]