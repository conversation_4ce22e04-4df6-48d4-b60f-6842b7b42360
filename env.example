PORT=3001
RABBIT_MQ=amqp://user:bitnami@localhost:5672/ #user:jbhi5MOMxCNCRjHP@
DB_URL=mongodb://localhost:27017 #root:YPhyqR37yJ
MAIN_DB=tenant_0
SERVICE_NAME=invoice_portal
COMMON_DB=common
PREFIX_DB="prt_"
BASE_URL=["localhost:4000"]
APP_URL_PREFIX="/portal"
REDIS_HOST=localhost
REDIS_TTL=600000
REDIS_DB_NUMBER=1
ACCESS_TOKEN_EXPIRE=43200
REFRESH_TOKEN_EXPIRE=864000
NODE_ENV=dev
S3_ENDPOINT=localhost
S3_PORT=9000
S3_ACCESS_KEY=minioadmin
S3_SECRET_KEY=minioadmin
S3_BUCKET_LOGO=logo
test_URL="https://api-dev.osus.cloud"
ZATCA_URL="https://gw-fatoora.zatca.gov.sa/e-invoicing/simulation/"
RUN_MIGRATIONS=false
LOG_LEVEL=["error"]
JWT_PK="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

JWT_PUBKEY="-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAljgbGXgyDC9s5HzdzqLW
DrM28dmcmid2SQ3Xyfe28lbFxoBSwSR4JZuoFY3sHunDwT9RbBj6QkU+Q7i+OY29
f3DCIaPxUdy4767YCyd99sXAacuJ/Vc1dXDL0uZEBFM148CDhwK4j575A06vK5Im
wk1JKMMqbEGmhJz9mu9dZbR8cIBGwfdlbwLhXgpgPwspD0YNyNL2slrFHCmTL7AE
r9QlVMraYuDpt0/pEI0MlVdLYfkVncElPFXNq7wWkHTAHQKy3toggbScvdPe9yQd
GokvcorbxU1vJUeBRof7ksXZ63TAF9nrHb8mlkGgDJ4kOy+6/yGqPlz3Xo5N97JJ
WQIDAQAB
-----END PUBLIC KEY-----"
