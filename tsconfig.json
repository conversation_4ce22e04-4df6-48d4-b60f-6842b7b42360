{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2017", "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "tsBuildInfoFile": "./dist/tsconfig.build.tsbuildinfo", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "resolveJsonModule": true}, "exclude": ["test-e2e/**/*", "node_modules", "dist", "src/**/*.spec.ts", "**/*spec.ts"], "include": ["src/**/*.ts", "src/**/*.json"]}