{"name": "service-portal", "version": "0.0.1", "description": "test", "author": "", "license": "UNLICENSED", "scripts": {"prepare": "husky install", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "seed": "node dist/utils/seeder.js", "seed:dev": "ts-node src/utils/seeder.ts", "start:dev": "if [ \"$DEBUG\" = \"true\" ]; then node --inspect=0.0.0.0:9230 -r ts-node/register -r tsconfig-paths/register src/main.ts; else NODE_ENV=dev nest start --watch; fi", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint '{src,apps,libs,test}/**/*.ts' --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --maxWorkers=1 --config  ./test-e2e/jest-e2e.json", "scriptRunner": "ts-node src/utils/script-runner.ts"}, "dependencies": {"@casl/ability": "^6.7.3", "@faker-js/faker": "^8.1.0", "@nestjs/cache-manager": "^3.0.1", "@nestjs/class-validator": "^0.13.4", "@nestjs/common": "^11.1.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "2.1.0", "@nestjs/microservices": "^11.1.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.0", "@nestjs/swagger": "^11.1.6", "@types/lodash": "^4.17.16", "argon2": "^0.43.0", "axios": "^1.9.0", "big.js": "^7.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "class-validator-mongo-object-id": "^1.4.0", "cookie-parser": "^1.4.7", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "ejs": "^3.1.10", "husky": "^9.1.7", "jsrsasign": "^11.1.0", "lodash": "^4.17.21", "minio": "^8.0.5", "mongoose": "^8.14.1", "mongoose-paginate-v2": "^1.9.0", "nest": "^0.1.6", "nest-casl": "^1.9.15", "nest-winston": "^1.10.2", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-headerapikey": "^1.2.2", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "prom-client": "^15.1.3", "qrcode": "^1.5.4", "reflect-metadata": "^0.2.2", "rimraf": "6.0.1", "rxjs": "7.8.2", "sales-tax": "^2.18.0", "uuid": "^11.1.0", "winston": "^3.17.0", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.0", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/jsrsasign": "^10.5.15", "@types/node": "^22.15.3", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "eslint": "^8.52.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-filenames": "^1.3.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.6", "jest": "^29.7.0", "jest-junit": "^16.0.0", "prettier": "^3.5.3", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node"}, "lint-staged": {"src/**/*.ts": "eslint"}}