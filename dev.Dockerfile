# Build the applicaiton
FROM dev.osusgroup.com:443/osuscloud/dependency_proxy/containers/node:20 AS builder
WORKDIR /build-stage
COPY package*.json ./
RUN npm ci 
COPY . ./
RUN npm run build

# Prepare production image
FROM dev.osusgroup.com:443/osuscloud/dependency_proxy/containers/node:20-slim AS production 
WORKDIR /app
COPY --from=builder --chown=node:node /build-stage/ ./
# Create the junit.xml file with the correct permissions
RUN touch /app/junit.xml && chown node:node /app/junit.xml && chmod 644 /app/junit.xml

# Set the correct ownership and permissions
RUN chown -R node:node /app && chmod -R 755 /app
USER  node
ENTRYPOINT ["node", "dist/main.js"]