import { inventoryRoutes, service, usersRoutes, tradeRoutes } from '../utils';
import { get, login, post } from '../utils/network';

import * as sale from '../mockData/sale-invoice.json';
import * as saleReturn from '../mockData/sale-return.json';
import * as tokenCreation from '../mockData/token-creation.json';
import * as items from '../mockData/item.json';
import * as groupItems from '../mockData/group-item.json';
import * as paymentTypes from '../mockData/payment-types.json';
import * as units from '../mockData/units.json';
import * as customers from '../mockData/customer.json';
import * as saleRepresentatives from '../mockData/sale-representative.json';

describe('create sales invoice (e2e)', () => {
  let cookie, Authorization, saleReturnId, saleId,transactionRefrence;

  beforeAll(async () => {
    cookie = await login();
  });

  // create API token
  it('create API token (POST)/users/users-profile/token', async () => {
    const res = await post(
      service.users,
      usersRoutes.user_token,
      tokenCreation,
      {
        branch: '64956ef5f506e9edf21df4e8',
        Cookie: cookie,
      },
    );
    Authorization = `Bearer ${res.data.api_token}`;
    expect(res.status).toEqual(201);
  });

  // test token
  it('get token test (GET)/users/users-profile/test', async () => {
    const res = await get(service.users, `${usersRoutes.token_test}`, {
      branch: '64956ef5f506e9edf21df4e8',
      Authorization,
    });
    expect(res.status).toEqual(200);
  });

  // get branches
  it('get branches (GET)/users/branch', async () => {
    const res = await get(service.users, `${usersRoutes.branch}`, {
      branch: '64956ef5f506e9edf21df4e8',
      Authorization,
    });
    expect(res.status).toEqual(200);
  });

  // get stores
  it('get stores (GET)/inventory/stores/desktop', async () => {
    const res = await get(
      service.inventory,
      `${inventoryRoutes.desktop_stores}`,
      {
        branch: '64956ef5f506e9edf21df4e8',
        Authorization,
      },
    );
    expect(res.status).toEqual(200);
  });

  // import payment types
  it('import payment types (POST)/users/payment-types', async () => {
    const res = await post(
      service.users,
      usersRoutes.payment_types,
      paymentTypes,
      {
        branch: '64956ef5f506e9edf21df4e8',
        Authorization,
      },
    );
    expect(res.status).toEqual(201);
  });

  // import item group
  it('import item group (POST)/inventory/group-item', async () => {
    const res = await post(
      service.inventory,
      inventoryRoutes.group_item,
      groupItems,
      {
        branch: '64956ef5f506e9edf21df4e8',
        Authorization,
      },
    );
    expect(res.status).toEqual(201);
  });

  // import units
  it('import units (POST)/inventory/units', async () => {
    const res = await post(service.inventory, inventoryRoutes.units, units, {
      branch: '64956ef5f506e9edf21df4e8',
      Authorization,
    });
    expect(res.status).toEqual(201);
  });

  // import customers
  it('import customers (POST)/trade/partner/customer', async () => {
    const res = await post(
      service.trade,
      tradeRoutes.partner_customer,
      customers,
      {
        branch: '64956ef5f506e9edf21df4e8',
        Authorization,
      },
    );
    expect(res.status).toEqual(201);
  });

  // import sale representatives
  it('import sale representatives (POST)/trade/sales-representative', async () => {
    const res = await post(
      service.trade,
      tradeRoutes.sales_representative,
      saleRepresentatives,
      {
        branch: '64956ef5f506e9edf21df4e8',
        Authorization,
      },
    );
    expect(res.status).toEqual(201);
  });

  // import items
  it('import items (POST)/inventory/items', async () => {
    const res = await post(service.inventory, inventoryRoutes.items, items, {
      branch: '64956ef5f506e9edf21df4e8',
      Authorization,
    });
    expect(res.status).toEqual(201);
  });

  // get item snapshot
  it('get item snapshot (GET)/inventory/stores/id/items/id', async () => {
    const res = await get(
      service.inventory,
      `${inventoryRoutes.stores}/64ad000ab16fbc375c9649de/items/S11`,
      {
        branch: '64956ef5f506e9edf21df4e8',
        Cookie: cookie,
      },
    );
    expect(res.status).toEqual(200);
  });

  // create sale invoice
  it('create sale invoice (POST)/trade/sale', async () => {
    const res = await post(service.trade, tradeRoutes.sale, sale, {
      branch: '64956ef5f506e9edf21df4e8',
      Cookie: cookie,
    });
    saleId = res.data.invoice._id;
    expect(res.status).toEqual(201);
  });

  // get sale invoice
  it('get sale invoice (GET)/trade/sale/id', async () => {
    const res = await get(service.trade, `${tradeRoutes.sale}/${saleId}`, {
      branch: '64956ef5f506e9edf21df4e8',
      Cookie: cookie,
    });
    transactionRefrence = res.data.transactions[0]._id
    expect(res.status).toEqual(200);
  });

  // get sale invoices by desktop
  it('get sale invoices by desktop (GET)/trade/sale', async () => {
    const res = await get(service.trade, `${tradeRoutes.sale}`, {
      branch: '64956ef5f506e9edf21df4e8',
      Authorization,
    });
    expect(res.status).toEqual(200);
  });

  // create sale return invoice
  it('create sale return invoice (POST)/trade/return-sale', async () => {
    saleReturn.transactions[0].transaction_reference = transactionRefrence
    const res = await post(
      service.trade,
      tradeRoutes.return_sale,
      { ...saleReturn, sale_id: saleId },
      {
        branch: '64956ef5f506e9edf21df4e8',
        Cookie: cookie,
      },
    );
    saleReturnId = res.data.invoice._id;
    expect(res.status).toEqual(201);
  });

  // get sale return
  it('get sale return invoice (GET)/trade/return-sale/id', async () => {
    const res = await get(
      service.trade,
      `${tradeRoutes.return_sale}/${saleReturnId}`,
      {
        branch: '64956ef5f506e9edf21df4e8',
        Cookie: cookie,
      },
    );
    expect(res.status).toEqual(200);
  });

  // get sale invoices by desktop
  it('get sale return invoices by desktop (GET)/trade/return-sale', async () => {
    const res = await get(service.trade, `${tradeRoutes.return_sale}`, {
      branch: '64956ef5f506e9edf21df4e8',
      Authorization,
    });
    expect(res.status).toEqual(200);
  });
});
