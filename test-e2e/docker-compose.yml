services:
  invoice-portal:
    build:
      context: ..
      dockerfile: dev.Dockerfile
    expose:
      - 3000
    ports:
      - 3000:3000
    environment:
      - NODE_ENV=dev
      - DB_URL=***********************************/
      - SERVICE_NAME=invoice_portal
      - ACCESS_TOKEN_EXPIRE=43200
      - REFRESH_TOKEN_EXPIRE=864000
      - COMMON_DB=common
      - BASE_URL=["localhost:4000"]
      - JWT_PK=${JWT_PK}
      - JWT_PUBKEY=${JWT_PUBKEY}
      - PORT=3000
      - RUN_MIGRATIONS=false
      - S3_ENDPOINT=minio
      - S3_PORT=9000
      - S3_SSL=false
      - S3_ACCESS_KEY=minioadmin
      - S3_SECRET_KEY=minioadmin
      - S3_BUCKET_LOGO=logo
      - S3_BUCKET_PROFILE=profile
      - TEST_URL=http://localhost:3000
    depends_on:
      mongodb:
        condition: service_started

  mongodb:
    image: mongo
    environment:
      - 'MONGO_INITDB_ROOT_USERNAME=mongo'
      - 'MONGO_INITDB_ROOT_PASSWORD=mongo'
    expose:
      - 27017
    ports:
      - 27017:27017
    volumes:
      - mongo:/data/db

networks:
  default:
    driver: bridge

volumes:
  mongo:
