import axios from 'axios';
import {
  accountingRoutes,
  BASE_URL,
  inventoryRoutes,
  service,
  usersRoutes,
  tradeRoutes,
} from '.';
import * as user from '../mockData/user.json';

// Base function for GET requests
export const get = (
  service: service,
  route:
    | usersRoutes
    | accountingRoutes
    | inventoryRoutes
    | tradeRoutes
    | string,
  header?,
) => {
  const headers: any = {
    ...header,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    withCredentials: true,
  };
  return axios({
    method: 'get',
    url: `${BASE_URL}/${service}/${route}`,
    headers,
  });
};

export const patch = (
  service: service,
  route:
    | usersRoutes
    | accountingRoutes
    | inventoryRoutes
    | tradeRoutes
    | string,
  body,
  header?,
) => {
  const headers: any = {
    ...header,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    withCredentials: true,
  };
  return axios({
    method: 'patch',
    url: `${BASE_URL}/${service}/${route}`,
    data: body,
    headers,
  });
};

// Base function for POST requests
export const post = async (
  service: service,
  route:
    | usersRoutes
    | accountingRoutes
    | inventoryRoutes
    | tradeRoutes
    | string,
  body,
  header?,
) => {
  const headers = {
    ...header,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    withCredentials: true,
  };
  console.log('url', `${BASE_URL}/${service}/${route}`);

  return axios({
    method: 'post',
    url: `${BASE_URL}/${service}/${route}`,
    headers,
    data: body,
  });
};

export const login = async () => {
  const res = await post(service.users, usersRoutes.login, user);
  const cookie = res.headers['set-cookie']?.[0].split(';')[0];
  return cookie;
};

// Add a response interceptor
axios.interceptors.response.use(
  function (response) {
    return response;
  },
  function (error) {
    return error;
  },
);
