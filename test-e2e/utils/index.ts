import * as dotenv from 'dotenv';

dotenv.config();
// eslint-disable-next-line @typescript-eslint/naming-convention
export const BASE_URL = process.env.TEST_URL;

export enum service {
  users = 'users',
  accounting = 'accounting',
  inventory = 'inventory',
  trade = 'trade',
}

export enum usersRoutes {
  login = 'login',
  verify = 'verify',
  branch = 'branch',
  payment_types = 'payment-types',
  user_token = 'users-profile/token',
  token_test = "users-profile/test"
}

export enum accountingRoutes {
  general_journal = 'general-journal',
  receipt_voucher = 'vouchers/receipt',
  payment_voucher = 'vouchers/payment',
  memo_voucher = 'vouchers/memo',
  temp_beginning_balance = 'temp-beginning-balance',
  customer_account_activity = 'reports/customer-account-activity',
  vendor_account_activity = 'reports/vendor-account-activity',
  account_activity = 'reports/account-activity',
  trail_balance = 'reports/trail-balance',
  balance_sheet = 'reports/balance-sheet',
  income_statement = 'reports/income-statement',
  journal = 'reports/journal',
  voucher_activity = 'reports/voucher-activity',
  report_request = 'report-request',
}

export enum inventoryRoutes {
  stores = 'stores',
  desktop_stores = 'stores/desktop',
  abstract_items = 'items/abstract',
  items = 'items',
  units = 'units',
  group_item = 'group-item',
  physical_inventory = 'physical-inventory',
  physical_inventory_posting = 'posting',
  physical_inventory_posting_report = 'reports/physical-inventory-posting-report',
  item_activity = 'reports/item-activity',
  report_request = 'report-request',
}

export enum tradeRoutes {
  sale = 'sale',
  sales_representative = 'sales-representative',
  return_sale = 'return-sale',
  purchase = 'purchase',
  partner_customer = 'partner/customer',
  partner_vendor = 'partner/vendor',
  reports_sale = 'reports/sale',
  report_request = 'report-request',
  reporting_purchase = 'reporting/purchase',
}
