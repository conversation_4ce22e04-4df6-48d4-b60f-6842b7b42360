import { get, post } from '../utils/network';
import { service, usersRoutes } from '../utils';

import * as user from '../mockData/user.json';

describe('Auth (e2e)', () => {
  let cookie;

  beforeAll(async () => {});
  it('login on /login', async () => {
    const res = await post(service.users, usersRoutes.login, user);
    cookie = res.headers['set-cookie']?.[0].split(';')[0];
    expect(res.status).toEqual(201);
  });

  it('verify on /verify', async () => {
    const res = await get(service.users, usersRoutes.verify, {
      Cookie: cookie,
    });
    expect(res.status).toEqual(200);
  });

  it('get branches on /branch', async () => {
    const res = await get(service.users, usersRoutes.branch, {
      Cookie: cookie,
      Branch: '64956ef5f506e9edf21df4e8',
    });
    expect(res.status).toEqual(200);
  });
});
